syntax = "proto3";

// Client <-> Server

message CreateSession {
  uint32 mapgen_ver = 1;
  uint32 config_ver = 2;
  repeated uint32 used_boosters = 3;
}

message Session {
  uint64 id = 1;
  repeated string outer_seed = 2;
  repeated string inner_seed = 3;
  uint32 multiplier = 4;
  uint32 highest_score = 5;
  repeated uint32 excluded_presets = 6;

  optional int64 total_score_event = 100;
  optional int64 highest_score_event = 101;

  // DEPRECATED
  optional int64 ton_event_allowed_amount_of_coins = 102;
  // DEPRECATED
  optional int64 ton_event_coin_value = 103;

  // DEPRECATED
  optional int64 trump_event_allowed_amount_of_coins = 104;
  // DEPRECATED
  optional int64 trump_event_coin_value = 105;

  // Values of each TON coin that can spawn (in nanotons)
  repeated uint64 ton_coin_values = 300;

  // An array of Y positions where ton must spawn (score of gameplay)
  repeated uint32 ton_spawn_positions = 301;
  repeated uint32 used_boosters = 400;

  optional uint32 custom_coin_type = 500;
  repeated uint32 custom_coin_values = 501;
  repeated uint32 custom_coin_spawn_positions = 502;

  optional uint32 dynamic_coin_type = 503;
  repeated uint32 dynamic_coin_values = 504;
  repeated uint32 dynamic_coin_spawn_positions = 505;
}

message SessionUpdate {
  uint64 id = 1;
  repeated ChunkUpdate chunks = 2;

  uint32 score = 10;
}

message ChunkUpdate {
  uint32 index = 1;
  repeated EntityUpdate entities = 2;
}

message EntityUpdate {
  uint32 update_type = 1;
  uint32 entity_index = 2;
  float position_x = 3;
  float position_y = 4;
  uint32 time = 5;
}
