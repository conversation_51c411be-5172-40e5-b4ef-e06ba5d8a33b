import { useWindowQueueStore, type WindowID } from '@/stores/windowQueueStore'
import { computed, onUnmounted } from 'vue'

export function useWindowQueue(windowId: WindowID) {
  const windowQueue = useWindowQueueStore()

  const openWindowInQueue = () => {
    windowQueue.openWindow(windowId)
  }

  const closeWindowInQueue = () => {
    windowQueue.closeWindow(windowId)
  }

  const isOpen = computed(() => windowQueue.currentWindow === windowId)

  onUnmounted(closeWindowInQueue)

  return {
    isOpen,
    openWindowInQueue,
    closeWindowInQueue
  }
}
