import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp.ts'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

export function useWheelSpinTimer() {
  const isFreeSpinBlocked = ref(false)

  const { playerState } = usePlayerState()

  const { getNow } = useNowTimestamp()
  const { hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer('wheelSpin', {
    onTimerEnd: () => {
      isFreeSpinBlocked.value = false
    }
  })

  const freeSpinAvailableAtTime = computed(() => {
    return playerState.value?.wheelSpins?.freeAvailableAt ?? 0
  })

  const recalculateFreeSpinAvailableTime = async (availableAt: number) => {
    isFreeSpinBlocked.value = true
    const now = await getNow()
    const timeLeft = Math.floor(availableAt - now)
    if (timeLeft > 0) {
      initTimerWithTotal(timeLeft)
    } else {
      isFreeSpinBlocked.value = false
    }
  }

  watch(
    freeSpinAvailableAtTime,
    () => {
      recalculateFreeSpinAvailableTime(freeSpinAvailableAtTime.value)
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', () => {
      recalculateFreeSpinAvailableTime(freeSpinAvailableAtTime.value)
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', () => {
      recalculateFreeSpinAvailableTime(freeSpinAvailableAtTime.value)
    })
  })

  return { isFreeSpinBlocked, hours, minutes, seconds }
}
