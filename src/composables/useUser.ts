import { useUserStore } from '@/stores/userStore'
import { datadogRum } from '@datadog/browser-rum'
import { retrieveLaunchParams } from '@telegram-apps/sdk'

export function useUser() {
  const store = useUserStore()

  function setUser() {
    const { initData } = retrieveLaunchParams()
    store.setUser(initData?.user ?? null)
    console.log(initData?.user?.id.toString())
    datadogRum?.setUser({
      id: initData?.user?.id.toString() ?? 'unknown',
      name: initData?.user?.username ?? 'unknown'
    })
  }

  function getUser() {
    return store
  }

  return {
    setUser,
    getUser,
    user: store
  }
}
