import { useNowTimestamp } from '@/services/client/useNowTimestamp.ts'
import { usePlayerState } from '@/services/client/usePlayerState'
import type { PlayerStateResponse } from '@/services/openapi'
import { computed, onMounted, onUnmounted, ref, watch, type ComputedRef, type Ref } from 'vue'
import { useCountdownTimer } from './useCountdownTimer'

import buttonButtonImageDeepDive from '@/assets/images/temp/deep-dive/button-moon.png'
import oceanButtonImageDeepDive from '@/assets/images/temp/deep-dive/button-ocean.png'
import moonButtonImageScrolling from '@/assets/images/temp/scrolling-offer/button-moon.png'
import oceanButtonImageScrolling from '@/assets/images/temp/scrolling-offer/button-ocean.png'
import deepButtonImageSnake from '@/assets/images/temp/snake-offer/button-deep.png'
import moonButtonImageSnake from '@/assets/images/temp/snake-offer/button-moon.png'

type Eventinfo = {
  endsAt: number
}

export function useEventInfo(event: ComputedRef<Eventinfo | null>) {
  const { now } = useNowTimestamp()
  const isEventTimeOver = ref(false)
  const isEventActive = computed(() => event.value !== null && !isEventTimeOver.value)
  const eventTimeLeftInSeconds = computed(() => {
    if (event.value === null || !now.value?.utc) return 0
    return event.value.endsAt - now.value?.utc
  })

  return {
    isEventTimeOver,
    isEventActive,
    eventTimeLeftInSeconds
  }
}

export function useOnePercentEventInfo(
  playerState: Ref<PlayerStateResponse, PlayerStateResponse> | Ref<undefined, undefined>
) {
  const event = computed(() => {
    return playerState.value?.onepercentEvent ?? null
  })
  return useEventInfo(event)
}

export function useHotrecordEventInfo(
  playerState: Ref<PlayerStateResponse, PlayerStateResponse> | Ref<undefined, undefined>
) {
  const event = computed(() => {
    return playerState.value?.hotrecordEvent ?? null
  })
  return useEventInfo(event)
}

export function useTonOnPlatformEventInfo(
  playerState: Ref<PlayerStateResponse, PlayerStateResponse> | Ref<undefined, undefined>
) {
  const event = computed(() => {
    return playerState.value?.tonOnPlatformEvent ?? null
  })
  return useEventInfo(event)
}

export function useDeepDiveEventInfo() {
  const { playerState, refetchPlayerState } = usePlayerState()
  const { getNow } = useNowTimestamp()
  const isEventTimeOver = ref(false)

  const progressiveOffer = computed(() => {
    return playerState.value?.progressiveOffers.find(
      offer => offer.usageDynamicCoins && !offer.isCompleted
    )
  })

  const isEventActive = computed(() => !!progressiveOffer.value && !isEventTimeOver.value)

  const { countdown, days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'deepDive',
    {
      onTimerEnd: async () => {
        if (!isEventActive.value) return
        isEventTimeOver.value = true
        await refetchPlayerState()
        await recalculateTime()
      }
    }
  )

  const recalculateTime = async () => {
    if (isEventActive.value === null) return
    const now = await getNow()
    const timeLeft = (progressiveOffer.value!.endsAt ?? 0) - now
    if (timeLeft > 0) {
      initTimerWithTotal(timeLeft)
    } else {
      isEventTimeOver.value = true
    }
  }

  watch(
    () => isEventActive.value,
    async () => {
      if (!isEventActive.value) return
      await recalculateTime()
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  const OFFER_ID_TO_BUTTON_IMAGE: Record<number, string> = {
    10000: buttonButtonImageDeepDive,
    10001: oceanButtonImageDeepDive
  }

  const buttonImage = computed(() => {
    return OFFER_ID_TO_BUTTON_IMAGE[progressiveOffer.value?.id ?? 10000]
  })

  return {
    isEventActive,
    isEventTimeOver,
    buttonImage,
    countdown,
    days,
    hours,
    minutes,
    seconds
  }
}

export function useScrollingEventInfo() {
  const { playerState, refetchPlayerState } = usePlayerState()
  const { getNow } = useNowTimestamp()
  const isEventTimeOver = ref(false)

  const progressiveOffer = computed(() => {
    return playerState.value?.progressiveOffers.find(
      offer => (offer.id === 10002 || offer.id === 10003) && !offer.isCompleted
    )
  })

  const isEventActive = computed(() => !!progressiveOffer.value && !isEventTimeOver.value)

  const { countdown, days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'scrollingOffer',
    {
      onTimerEnd: async () => {
        if (!isEventActive.value) return
        isEventTimeOver.value = true
        await refetchPlayerState()
        await recalculateTime()
      }
    }
  )

  const recalculateTime = async () => {
    if (isEventActive.value === null) return
    const now = await getNow()
    const timeLeft = (progressiveOffer.value!.endsAt ?? 0) - now
    if (timeLeft > 0) {
      initTimerWithTotal(timeLeft)
    } else {
      isEventTimeOver.value = true
    }
  }

  watch(
    () => isEventActive.value,
    async () => {
      if (!isEventActive.value) return
      await recalculateTime()
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  const OFFER_ID_TO_BUTTON_IMAGE: Record<number, string> = {
    10002: moonButtonImageScrolling,
    10003: oceanButtonImageScrolling
  }

  const buttonImage = computed(() => {
    return OFFER_ID_TO_BUTTON_IMAGE[progressiveOffer.value?.id ?? 10002]
  })

  return {
    isEventActive,
    isEventTimeOver,
    buttonImage,
    countdown,
    days,
    hours,
    minutes,
    seconds
  }
}

export function useSnakeEventInfo() {
  const { playerState, refetchPlayerState } = usePlayerState()
  const { getNow } = useNowTimestamp()
  const isEventTimeOver = ref(false)

  const progressiveOffer = computed(() => {
    return playerState.value?.progressiveOffers.find(
      offer => (offer.id === 10004 || offer.id === 10005) && !offer.isCompleted
    )
  })

  const isEventActive = computed(() => !!progressiveOffer.value && !isEventTimeOver.value)

  const { countdown, days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'snakeOffer',
    {
      onTimerEnd: async () => {
        if (!isEventActive.value) return
        isEventTimeOver.value = true
        await refetchPlayerState()
        await recalculateTime()
      }
    }
  )

  const recalculateTime = async () => {
    if (isEventActive.value === null) return
    const now = await getNow()
    const timeLeft = (progressiveOffer.value!.endsAt ?? 0) - now
    if (timeLeft > 0) {
      initTimerWithTotal(timeLeft)
    } else {
      isEventTimeOver.value = true
    }
  }

  watch(
    () => isEventActive.value,
    async () => {
      if (!isEventActive.value) return
      await recalculateTime()
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  const OFFER_ID_TO_BUTTON_IMAGE: Record<number, string> = {
    10004: moonButtonImageSnake,
    10005: deepButtonImageSnake
  }

  const buttonImage = computed(() => {
    return OFFER_ID_TO_BUTTON_IMAGE[progressiveOffer.value?.id ?? 10004]
  })

  return {
    isEventActive,
    isEventTimeOver,
    buttonImage,
    countdown,
    days,
    hours,
    minutes,
    seconds
  }
}
