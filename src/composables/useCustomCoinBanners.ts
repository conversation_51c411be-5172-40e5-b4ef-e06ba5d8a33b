import { useCustomCoinEventInfo } from '@/composables/useCustomCoinEventInfo.ts'
import { useEventPromoCheck, useEventWelcomeCheck } from '@/composables/useEventWelcomeCheck.ts'
import { useWindowQueue } from '@/composables/useWindowQueue.ts'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { useClaimCustomCoinEvent } from '@/services/client/useClaimCustomCoinEvent.ts'
import { usePlayerReceivedEventReward } from '@/services/client/usePlayerFlag.ts'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import leaguesService from '@/services/local/leagues.ts'
import { useRewardStore } from '@/stores/rewardStore.ts'
import { sendAnalyticsEvent } from '@/utils/analytics.ts'
import { onMounted, watch } from 'vue'

const HAS_SEEN_EVENT_PROMO_BANNER_KEY = 'hasSeenCustomCoinEventPromoBanner'
const HAS_SEEN_EVENT_BANNER_KEY = 'hasSeenCustomCoinEventBanner'

export function useCustomCoinBanners() {
  const {
    isOpen: isOpenRewardBanner,
    openWindowInQueue: openRewardWindowInQueue,
    closeWindowInQueue: closeRewardWindowInQueue
  } = useWindowQueue('custom-coin-reward-window')
  const {
    isOpen: isOpenWelcomeBanner,
    openWindowInQueue: openWelcomeWindowInQueue,
    closeWindowInQueue: closeWelcomeWindowInQueue
  } = useWindowQueue('custom-coin-welcome-window')
  const {
    isOpen: isOpenPromoBanner,
    openWindowInQueue: openPromoWindowInQueue,
    closeWindowInQueue: closePromoWindowInQueue
  } = useWindowQueue('custom-coin-promo-window')

  const { playerState } = usePlayerState()
  const { isCustomCoinEventActive, isCustomCoinEventPromoActive } =
    useCustomCoinEventInfo(playerState)

  const { checkPromoBanner } = useEventPromoCheck()
  const { checkWelcomeBanner } = useEventWelcomeCheck()
  const { claimEventReward } = useClaimCustomCoinEvent()

  const rewardStore = useRewardStore()
  const { onPlayerRecievedCustomCoinReward } = usePlayerReceivedEventReward()

  const closeBanner = () => {
    if (isOpenRewardBanner.value) {
      const coinsCollected = playerState.value?.customCoinEventReward?.coinsCollected ?? 0
      const reward = playerState.value!.customCoinEventReward?.reward?.amount ?? 0

      if (coinsCollected && reward) {
        const prevBalance = playerState.value!.hard ?? 0
        rewardStore.showReward([
          {
            type: 'hard',
            prevValue: getCurrencyRealAmount(prevBalance - reward, 'hard'),
            value: getCurrencyRealAmount(reward, 'hard')
          }
        ])
        sendAnalyticsEvent('event_end', {
          event: 'custom_coin',
          total_points: coinsCollected,
          reward_type: 'hard',
          reward_amount: reward
        })
        onPlayerRecievedCustomCoinReward()
        closeRewardWindowInQueue()
      } else {
        claimEventReward()
          .then(data => {
            const prevBalance = playerState.value!.ton ?? 0
            const reward = data.toncoin
            rewardStore.showReward([
              {
                type: 'ton',
                prevValue: getCurrencyRealAmount(prevBalance, 'ton'),
                value: getCurrencyRealAmount(reward, 'ton')
              }
            ])
            closeRewardWindowInQueue()
          })
          .catch(() => {
            closeRewardWindowInQueue()
          })
      }
    } else if (isOpenWelcomeBanner.value) {
      closeWelcomeWindowInQueue()
    } else if (isOpenPromoBanner.value) {
      closePromoWindowInQueue()
    }
  }

  const checkRewardBanner = () => {
    return (
      (!playerState.value!.customCoinEvent &&
        playerState.value!.customCoin !== undefined &&
        playerState.value!.customCoinConvertRate !== undefined) ||
      playerState.value?.customCoinEventReward?.reward?.amount
    )
  }

  watch(
    () => playerState.value?.customCoinEventReward?.reward?.amount,
    () => {
      const isOpenRewardBanner = checkRewardBanner()
      if (isOpenRewardBanner) {
        openRewardWindowInQueue()
      }
    }
  )

  onMounted(async () => {
    const isOpenRewardBanner = checkRewardBanner()
    if (isOpenRewardBanner) {
      openRewardWindowInQueue()
    } else {
      const isOpenWelcomeBanner = await checkWelcomeBanner(
        HAS_SEEN_EVENT_BANNER_KEY,
        isCustomCoinEventActive.value,
        leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'customCoinEvent')
      )
      if (isOpenWelcomeBanner) {
        openWelcomeWindowInQueue()
      } else {
        const isOpenPromoBanner = await checkPromoBanner(
          HAS_SEEN_EVENT_PROMO_BANNER_KEY,
          isCustomCoinEventPromoActive.value,
          leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'customCoinEvent')
        )
        if (isOpenPromoBanner) {
          openPromoWindowInQueue()
        }
      }
    }
  })

  return {
    isOpenRewardBanner,
    isOpenPromoBanner,
    isOpenWelcomeBanner,

    closeBanner
  }
}
