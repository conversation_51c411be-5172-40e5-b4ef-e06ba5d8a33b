import { useAchievementsList, useAchievementsState } from '@/services/client/useAchievements.ts'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { useSkinsList } from '@/services/client/useSkins.ts'
import { cloudStorageService } from '@/shared/storage/cloudStorageService.ts'
import { useNotification } from '@/stores/notificationStore.ts'
import { computed } from 'vue'

const NOTIFICATION_TIMEOUT = 5000

const SKINS_ACHIEVEMENT_ID = 12
const REFS_ACHIEVEMENT_ID = 1
const SOFT_ACHIEVEMENT_ID = 4

const REFS_ACHIEVEMENT_REQUIREMENT = 'refsAchievementRequirement'
const SOFT_ACHIEVEMENT_REQUIREMENT = 'softAchievementRequirement'

export function useSkinsAchievement() {
  const notificationsStore = useNotification()
  const { checkSoftAchievement } = useSoftAchievement()

  const { achievementsList } = useAchievementsList(false)
  const { skinsList } = useSkinsList(false)

  const skinsAchievement = computed(() => {
    return achievementsList.value.find(({ id }) => id === SKINS_ACHIEVEMENT_ID)
  })

  const purchasedSkins = computed(() => {
    return skinsList.value.filter(({ purchased }) => purchased)
  })

  const checkSkinsAchievement = () => {
    if (!skinsAchievement.value || !purchasedSkins.value.length) return

    const isSkinsAchievementCompleted = !skinsAchievement.value?.levels.length

    if (isSkinsAchievementCompleted) {
      return checkSoftAchievement()
    }

    const requirement = skinsAchievement.value?.levels[0].requirement
    if (purchasedSkins.value.length === requirement) {
      notificationsStore.showNotification(skinsAchievement.value, NOTIFICATION_TIMEOUT)

      setTimeout(() => {
        checkSoftAchievement()
      }, NOTIFICATION_TIMEOUT)
    } else {
      checkSoftAchievement()
    }
  }

  return { checkSkinsAchievement }
}

export function useRefsAchievement() {
  const notificationsStore = useNotification()

  const { achievementsList } = useAchievementsList(false)
  const { playerState } = usePlayerState()

  const refsAchievement = computed(() => {
    return achievementsList.value.find(({ id }) => id === REFS_ACHIEVEMENT_ID)
  })

  const refs = computed(() => {
    return playerState.value?.refs ?? 0
  })

  const checkRefsAchievement = () => {
    if (!refsAchievement.value || !refs.value) return

    const isRefsAchievementCompleted = !refsAchievement.value?.levels.length

    cloudStorageService.load<number>(REFS_ACHIEVEMENT_REQUIREMENT).then(requirement => {
      if (isRefsAchievementCompleted && requirement) {
        return cloudStorageService.delete(REFS_ACHIEVEMENT_REQUIREMENT)
      }

      if (isRefsAchievementCompleted) return

      const currentRequirement = refsAchievement.value?.levels[0].requirement

      const achievement = {
        ...refsAchievement.value,
        levels: refsAchievement.value.levels
          ?.filter(({ requirement }) => requirement <= refs.value)
          .reverse()
      }

      if (requirement !== currentRequirement && refs.value >= currentRequirement) {
        cloudStorageService.save(REFS_ACHIEVEMENT_REQUIREMENT, currentRequirement)
        notificationsStore.showNotification(achievement)
      }
    })
  }

  return { checkRefsAchievement }
}

export function useSoftAchievement() {
  const notificationsStore = useNotification()

  const { refetchPlayerState } = usePlayerState()
  const { achievementsList, refetch } = useAchievementsList(false)
  const { achievementsList: achievementsState } = useAchievementsState()

  const softAchievement = computed(() => {
    return achievementsList.value.find(({ id }) => id === SOFT_ACHIEVEMENT_ID)
  })

  const currentProgress = computed(() => {
    return (
      achievementsState.value.find(({ id }) => id === SOFT_ACHIEVEMENT_ID)?.currentProgress ?? 0
    )
  })

  const checkSoftAchievement = () => {
    const isSoftAchievementCompleted = !softAchievement.value?.levels.length

    cloudStorageService.load<number>(SOFT_ACHIEVEMENT_REQUIREMENT).then(requirement => {
      if (isSoftAchievementCompleted && requirement) {
        return cloudStorageService.delete(SOFT_ACHIEVEMENT_REQUIREMENT)
      }

      if (!softAchievement.value || !currentProgress.value || isSoftAchievementCompleted) return

      const achievement = {
        ...softAchievement.value,
        levels: softAchievement.value.levels
          ?.filter(({ requirement }) => requirement <= currentProgress.value)
          .reverse()
      }

      const currentRequirement = softAchievement.value?.levels[0].requirement

      if (requirement !== currentRequirement && currentProgress.value >= currentRequirement) {
        cloudStorageService.save(SOFT_ACHIEVEMENT_REQUIREMENT, currentRequirement)
        notificationsStore.showNotification(achievement)
      }
    })
    refetch()
    refetchPlayerState()
  }

  return { checkSoftAchievement }
}
