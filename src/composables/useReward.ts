import { useOpenLootbox } from '@/services/client/useLootboxes'
import { addToPlayerState, usePlayerState } from '@/services/client/usePlayerState'
import type { RewardInfo } from '@/services/openapi'
import { useLootboxReward, useRewardStore, useSkinReward } from '@/stores/rewardStore'
import type {
  LootboxRewardInfo,
  NumeralRewardInfo,
  Reward,
  SimpleRewardInfo,
  SkinRewardInfo,
  TimeRewardInfo
} from '@/types'
import {
  isLootboxRewardType,
  isNumeralRewardType,
  isObjectRewardType,
  isSimpleRewardType,
  isSkinRewardType,
  isStackableRewardType,
  isTimeRewardType
} from '@/types'

export const useReward = () => {
  const { playerState, refetchPlayerState } = usePlayerState()
  const { updatePlayerState } = addToPlayerState()
  const { openLootbox } = useOpenLootbox()
  const rewardStore = useRewardStore()
  const lootBoxStore = useLootboxReward()
  const skinStore = useSkinReward()

  const showRewards = (reward: RewardInfo[]) => {
    return new Promise<void>(resolve => {
      const numeralRewards = reward.filter((r): r is NumeralRewardInfo =>
        isNumeralRewardType(r.type)
      )
      const lootboxRewards = reward.filter((r): r is LootboxRewardInfo =>
        isLootboxRewardType(r.type)
      )
      const timeRewards = reward.filter((r): r is TimeRewardInfo => isTimeRewardType(r.type))
      const simpleRewards = reward.filter((r): r is SimpleRewardInfo => isSimpleRewardType(r.type))
      const skinRewards = reward.filter((r): r is SkinRewardInfo => isSkinRewardType(r.type))

      const rewards: Reward[] = [
        ...numeralRewards.map(r => ({
          type: r.type,
          value: r.value,
          prevValue: isStackableRewardType(r.type)
            ? (playerState.value?.boostersView?.[r.type] ?? (0 as number))
            : isObjectRewardType(r.type)
              ? (playerState.value?.[r.type]?.amount ?? (0 as number))
              : (playerState.value?.[r.type] ?? (0 as number))
        })),
        ...timeRewards.map(r => ({
          type: r.type,
          duration: r.value
        })),
        ...simpleRewards.map(r => ({
          type: r.type
        }))
      ]

      const numericPromise = () =>
        new Promise<void>(resolve => {
          rewardStore.showReward(rewards, () => {
            const isTimeReward = rewards.some(r => isTimeRewardType(r.type))
            if (isTimeReward) {
              refetchPlayerState()
            } else {
              // TODO: rewatch is a good place for updating cache here
              if (timeRewards.length) {
                refetchPlayerState()
              } else {
                numeralRewards.forEach(r => {
                  updatePlayerState(r.type, r.value)
                })
              }
            }
            resolve()
          })
        })

      const lootboxPromise = () =>
        new Promise<void>(resolve => {
          if (!lootboxRewards.length) {
            resolve()
            return
          }
          Promise.all(
            lootboxRewards.map(r =>
              openLootbox(r.type).then(data => ({
                type: data.lootboxType,
                rewards: data.rewards
              }))
            )
          ).then(lootboxes => {
            lootBoxStore.showReward(lootboxes, resolve)
          })
        })

      const skinPromise = () =>
        new Promise<void>(resolve => {
          if (!skinRewards.length) {
            resolve()
            return
          }
          skinStore.showReward(
            skinRewards.map((r, index) => ({
              skinId: r.value,
              multiplier: skinStore.getMultiplier(
                index,
                playerState.value!.multiplier ?? 1,
                skinRewards
              ),
              plusMultiplier: r.multiplier
            })),
            resolve
          )
        })

      numericPromise()
        .then(() => lootboxPromise())
        .then(() => skinPromise())
        .then(() => resolve())
    })
  }

  const showReward = (reward: RewardInfo) => {
    return new Promise<void>(resolve => {
      if (isNumeralRewardType(reward.type)) {
        rewardStore.showReward(
          [
            {
              type: reward.type,
              value: reward.value,
              prevValue: isStackableRewardType(reward.type)
                ? (playerState.value?.boostersView?.[reward.type] ?? (0 as number))
                : isObjectRewardType(reward.type)
                  ? (playerState.value?.[reward.type]?.amount ?? (0 as number))
                  : (playerState.value?.[reward.type] ?? (0 as number))
            }
          ],
          resolve
        )
      } else if (isTimeRewardType(reward.type)) {
        rewardStore.showReward(
          [
            {
              type: reward.type,
              duration: reward.value
            }
          ],
          resolve
        )
      } else if (isSimpleRewardType(reward.type)) {
        rewardStore.showReward(
          [
            {
              type: reward.type
            }
          ],
          resolve
        )
      } else if (isLootboxRewardType(reward.type)) {
        openLootbox(reward.type).then(data => {
          lootBoxStore.showReward(
            [
              {
                type: data.lootboxType,
                rewards: data.rewards
              }
            ],
            resolve
          )
        })
      } else if (isSkinRewardType(reward.type)) {
        skinStore.showReward(
          [
            {
              skinId: reward.value,
              multiplier: playerState.value!.multiplier ?? 1,
              plusMultiplier: reward.multiplier ?? 1
            }
          ],
          resolve
        )
      } else {
        resolve()
      }
    })
  }

  return { showRewards, showReward }
}
