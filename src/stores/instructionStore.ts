import type { InstructionType } from '@/constants/instructions'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useInstructionStore = defineStore('instructionStore', () => {
  const instructionType = ref<InstructionType | undefined>(undefined)

  function showInstruction(type: InstructionType) {
    instructionType.value = type
  }

  function hideInstruction() {
    instructionType.value = undefined
  }

  return {
    instructionType,
    showInstruction,
    hideInstruction
  }
})
