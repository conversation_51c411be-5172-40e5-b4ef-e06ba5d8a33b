import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

type OnePercentWindowID = 'onepercent-welcome-window' | 'onepercent-reward-window'
type CustomCoinWindowID =
  | 'custom-coin-welcome-window'
  | 'custom-coin-reward-window'
  | 'custom-coin-promo-window'
type HotRecordWindowID = 'hotrecord-welcome-window' | 'hotrecord-reward-window'
type ClanEventWindowID = 'clan-event-welcome-window' | 'clan-event-reward-window'
type TonMiningWindowID = 'tonmining-welcome-window'
type ExploitersWindowID =
  | 'exploiters-welcome-window'
  | 'exploiters-collect-window'
  | 'exploiters-last-chance-window'
type OfferWindowID =
  | 'snake-offer-banner'
  | 'scrolling-offer-banner'
  | 'deep-dive-banner'
  | 'loot-box-offer-banner'
  | 'crypto-offer-banner'
  | 'lucky-friend-banner'
  | 'skin-for-ton-banner'

export type WindowID =
  | 'ton-limit-reached-banner'
  | 'league-reward-screen'
  | 'daily-reward-dialog'
  | 'subscription-dialog'
  | OfferWindowID
  | OnePercentWindowID
  | CustomCoinWindowID
  | HotRecordWindowID
  | TonMiningWindowID
  | ClanEventWindowID
  | ExploitersWindowID

const WINDOWS_ORDER: Array<WindowID> = [
  // 1. Rewards for events
  'hotrecord-reward-window',
  'onepercent-reward-window',
  'custom-coin-reward-window',

  // 2. Game session end dialogs
  'exploiters-welcome-window',
  'exploiters-collect-window',
  'exploiters-last-chance-window',
  'ton-limit-reached-banner',

  // 3. Default events
  'hotrecord-welcome-window',
  'onepercent-welcome-window',
  'tonmining-welcome-window',

  // 4. Special events (Skin Event)
  'custom-coin-promo-window',
  'custom-coin-welcome-window',

  // 5. Other Rewards
  'daily-reward-dialog',
  'league-reward-screen',

  // 6. Offers
  'skin-for-ton-banner',
  'loot-box-offer-banner',
  'crypto-offer-banner',
  'lucky-friend-banner',
  'snake-offer-banner',
  'scrolling-offer-banner',
  'deep-dive-banner',

  // 7. Subscription dialog
  'subscription-dialog'
]

export const useWindowQueueStore = defineStore('windowQueueStore', () => {
  const windowQueue = ref<WindowID[]>([])
  const sortedWindowQueue = computed(() => {
    return windowQueue.value.sort((a, b) => {
      return WINDOWS_ORDER.indexOf(a) - WINDOWS_ORDER.indexOf(b)
    })
  })
  const currentWindow = computed(() => sortedWindowQueue.value[0] || '')

  const openWindow = (windowName: WindowID) => {
    if (!windowName) return
    if (windowQueue.value.includes(windowName)) return
    windowQueue.value.push(windowName)
  }

  const closeWindow = (windowName: WindowID) => {
    if (!windowQueue.value.length || !windowQueue.value.includes(windowName)) return
    windowQueue.value = windowQueue.value.filter(name => name !== windowName)
  }

  return {
    currentWindow,
    openWindow,
    closeWindow
  }
})
