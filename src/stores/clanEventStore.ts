import { useCountdownTimer } from '@/composables/useCountdownTimer'
import { useNowTimestamp } from '@/services/client/useNowTimestamp'
import { usePlayerState } from '@/services/client/usePlayerState.ts'
import { defineStore } from 'pinia'
import { computed, onMounted, onUnmounted, watch } from 'vue'

export const useClanEventStore = defineStore('clanEventStore', () => {
  const { getNow } = useNowTimestamp()
  const { playerState, refetchPlayerState } = usePlayerState()

  const isClanEventActive = computed(() => {
    return !!playerState.value?.clanEventState
  })

  const customCoinEndsAt = computed(() => {
    return playerState.value?.customCoinEvent?.endsAt ?? 0
  })

  const { countdown, days, hours, minutes, seconds, initTimerWithTotal } = useCountdownTimer(
    'clanEvent',
    {
      onTimerEnd: async () => {
        if (!isClanEventActive.value) return
        await refetchPlayerState()
        await recalculateTime()
      }
    }
  )

  const recalculateTime = async () => {
    if (isClanEventActive.value === null) return
    const now = await getNow()
    const timeLeft = customCoinEndsAt.value - now
    if (timeLeft > 0) {
      initTimerWithTotal(timeLeft)
    }
  }

  watch(
    () => isClanEventActive.value,
    async () => {
      if (!isClanEventActive.value) return
      await recalculateTime()
    },
    { immediate: true }
  )

  onMounted(() => {
    window.addEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  onUnmounted(() => {
    window.removeEventListener('visibilitychange', async () => {
      await recalculateTime()
    })
  })

  return {
    isClanEventActive,
    countdown,
    days,
    hours,
    minutes,
    seconds
  }
})
