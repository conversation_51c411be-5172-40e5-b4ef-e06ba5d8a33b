import star from '@/assets/images/temp/instructions/star.png'
import uni from '@/assets/images/temp/instructions/uni.png'

import hotRecord1 from '@/assets/images/temp/instructions/hot-record-event/1.png'
import hotRecord2 from '@/assets/images/temp/instructions/hot-record-event/2.png'
import hotRecordHeader from '@/assets/images/temp/instructions/hot-record-event/header.png'

import onePercent1 from '@/assets/images/temp/instructions/onepercent-event/1.png'
import onePercentHeader from '@/assets/images/temp/instructions/onepercent-event/header.png'

import tonMining1 from '@/assets/images/temp/instructions/ton-event/1.png'
import tonMining2 from '@/assets/images/temp/instructions/ton-event/2.png'
import tonMiningHeader from '@/assets/images/temp/instructions/ton-event/header.png'

import customCoin1 from '@/assets/images/temp/instructions/custom-coin-event/1.png'
import customCoin2 from '@/assets/images/temp/instructions/custom-coin-event/2.png'
import customCoin3 from '@/assets/images/temp/instructions/custom-coin-event/3.png'
import customCoinHeader from '@/assets/images/temp/instructions/custom-coin-event/header.png'

import leagues1 from '@/assets/images/temp/instructions/leagues/1.png'
import leagues2 from '@/assets/images/temp/instructions/leagues/2.png'
import leagues3 from '@/assets/images/temp/instructions/leagues/3.png'
import leagues4 from '@/assets/images/temp/instructions/leagues/4.png'
import leaguesHeader from '@/assets/images/temp/instructions/leagues/header.png'

export type InstructionType =
  | 'custom-coin-instruction'
  | 'hot-record-instruction'
  | 'one-percent-instruction'
  | 'leagues-instruction'
  | 'ton-mining-instruction'

export const CUSTOM_COIN_INSTRUCTION = 'custom-coin-instruction'
export const HOT_RECORD_INSTRUCTION = 'hot-record-instruction'
export const ONE_PERCENT_INSTRUCTION = 'one-percent-instruction'
export const LEAGUES_INSTRUCTION = 'leagues-instruction'
export const TON_MINING_INSTRUCTION = 'ton-mining-instruction'

export type InstructionStep = {
  image: string
  class?: string
  text?: string
  textClass?: string
  arrowClass?: string
}

export const INSTRUCTION_TYPE_TO_HEADER: Record<InstructionType, string> = {
  'custom-coin-instruction': customCoinHeader,
  'hot-record-instruction': hotRecordHeader,
  'one-percent-instruction': onePercentHeader,
  'leagues-instruction': leaguesHeader,
  'ton-mining-instruction': tonMiningHeader
}

export const INSTRUCTION_STEPS: Record<InstructionType, InstructionStep[]> = {
  'custom-coin-instruction': [
    {
      image: customCoin1,
      class: 'absolute h-[25%] w-[50%] top-[5%]',
      text: 'instructions.custom_coin.1',
      textClass: '-bottom-[10%]'
    },
    {
      image: customCoin2,
      class: 'absolute h-[25%] w-[50%] bottom-[45%] right-0',
      text: 'instructions.custom_coin.2',
      textClass: '-bottom-[10%]',
      arrowClass: 'absolute top-0 right-1/2 -translate-y-[80%] w-[60%]'
    },
    {
      image: customCoin3,
      class: 'absolute h-[25%] w-[50%] bottom-[5%]',
      text: 'instructions.custom_coin.3',
      textClass: 'bottom-0',
      arrowClass:
        'absolute top-0 left-[30%] -translate-y-[100%] w-[60%] -scale-x-100 -rotate-[10deg]'
    }
  ],
  'hot-record-instruction': [
    {
      image: uni,
      class: 'absolute h-[25%] w-[50%] top-[5%]',
      text: 'instructions.hot_record.1',
      textClass: 'bottom-0 translate-y-full pl-[12%] !text-start'
    },
    {
      image: hotRecord1,
      class: 'absolute h-[25%] w-[50%] bottom-[45%] right-0',
      text: 'instructions.hot_record.2',
      textClass: 'bottom-0',
      arrowClass: 'absolute top-0 right-1/2 -translate-y-[80%] w-[60%]'
    },
    {
      image: star,
      class: 'absolute h-[25%] w-[50%] bottom-[15%]',
      text: 'instructions.hot_record.3',
      textClass: 'bottom-0',
      arrowClass: 'absolute bottom-[30%] right-[10%] translate-x-[100%] w-[60%] rotate-[110deg]'
    },
    {
      image: hotRecord2,
      class: 'h-[15%] w-1/2 bottom-[5%] right-0',
      text: 'instructions.hot_record.4',
      textClass: 'bottom-0 translate-y-full'
    }
  ],
  'one-percent-instruction': [
    {
      image: uni,
      class: 'absolute h-[25%] w-[50%] top-[5%]',
      text: 'instructions.one_percent.1',
      textClass: 'bottom-0 translate-y-full pl-[12%] !text-start'
    },
    {
      image: onePercent1,
      class: 'absolute h-[25%] w-[50%] bottom-[45%] right-0',
      text: 'instructions.one_percent.2',
      textClass: 'bottom-0',
      arrowClass: 'absolute top-0 right-1/2 -translate-y-[80%] w-[60%]'
    },
    {
      image: star,
      class: 'absolute h-[25%] w-[50%] bottom-[15%]',
      text: 'instructions.one_percent.3',
      textClass: 'bottom-0',
      arrowClass: 'absolute bottom-[30%] right-[10%] translate-x-[100%] w-[60%] rotate-[110deg]'
    }
  ],
  'leagues-instruction': [
    {
      image: leagues1,
      class: 'absolute h-[25%] w-[50%] -top-[3%]',
      text: 'instructions.leagues.1',
      textClass: 'bottom-0'
    },
    {
      image: leagues2,
      class: 'absolute h-[25%] w-[50%] bottom-[53%] right-0',
      text: 'instructions.leagues.2',
      textClass: 'bottom-0',
      arrowClass: 'absolute top-0 right-1/2 -translate-y-[80%] w-[60%]'
    },
    {
      image: leagues3,
      class: 'absolute h-[25%] w-[50%] bottom-[23%]',
      text: 'instructions.leagues.3',
      textClass: 'bottom-[10%]',
      arrowClass: 'absolute bottom-[30%] right-[10%] translate-x-[100%] w-[60%] rotate-[110deg]'
    },
    {
      image: leagues4,
      class: 'absolute bottom-[5%] left-1/2 -translate-x-full h-[13%] aspect-[1.88/1]',
      text: 'instructions.leagues.4',
      textClass:
        'top-1/2 -right-[10px] translate-x-full -translate-y-1/2 !text-start whitespace-nowrap'
    }
  ],
  'ton-mining-instruction': [
    {
      image: uni,
      class: 'absolute h-[25%] w-[50%] top-[5%]',
      text: 'instructions.ton_mining.1',
      textClass: 'bottom-0 translate-y-full pl-[12%] !text-start'
    },
    {
      image: tonMining1,
      class: 'absolute h-[25%] w-[50%] bottom-[45%] right-0',
      text: 'instructions.ton_mining.2',
      textClass: 'bottom-0',
      arrowClass: 'absolute top-0 right-1/2 -translate-y-[80%] w-[60%]'
    },
    {
      image: tonMining2,
      class: 'absolute h-[25%] w-[50%] bottom-[15%]',
      text: 'instructions.ton_mining.3',
      textClass: 'bottom-0',
      arrowClass: 'absolute bottom-[30%] right-[10%] translate-x-[100%] w-[60%] rotate-[110deg]'
    }
  ]
}
