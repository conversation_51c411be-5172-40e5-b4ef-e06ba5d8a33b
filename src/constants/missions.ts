import oneUniImage from '@/assets/images/temp/missions/1uni.png'
import threeUniImage from '@/assets/images/temp/missions/3uni.png'
import fiveUniImage from '@/assets/images/temp/missions/5uni.png'
import aimImage from '@/assets/images/temp/missions/aim.png'
import boosterImage from '@/assets/images/temp/missions/booster.png'
import communityImage from '@/assets/images/temp/missions/community.png'
import homeScreenImage from '@/assets/images/temp/missions/home-screen.png'
import jumpImage from '@/assets/images/temp/missions/jump.png'
import magnetImage from '@/assets/images/temp/missions/magnet.png'
import miniapp10 from '@/assets/images/temp/missions/miniapp_10.png'
import miniapp15 from '@/assets/images/temp/missions/miniapp_15.jpg'
import miniapp19 from '@/assets/images/temp/missions/miniapp_19.jpg'
import miniapp22 from '@/assets/images/temp/missions/miniapp_22.png'
import miniapp23 from '@/assets/images/temp/missions/miniapp_23.jpg'
import miniapp24 from '@/assets/images/temp/missions/miniapp_24.jpg'
import miniapp25 from '@/assets/images/temp/missions/miniapp_25.png'
import miniapp26 from '@/assets/images/temp/missions/miniapp_26.jpg'
import miniapp27 from '@/assets/images/temp/missions/miniapp_27.jpg'
import miniapp28 from '@/assets/images/temp/missions/miniapp_28.jpg'
import miniapp29 from '@/assets/images/temp/missions/miniapp_29.jpg'
import miniapp30 from '@/assets/images/temp/missions/miniapp_30.png'
import miniapp31 from '@/assets/images/temp/missions/miniapp_31.jpg'
import miniapp32 from '@/assets/images/temp/missions/miniapp_32.jpg'
import miniapp33 from '@/assets/images/temp/missions/miniapp_33.jpg'
import miniapp34 from '@/assets/images/temp/missions/miniapp_34.png'
import miniapp35 from '@/assets/images/temp/missions/miniapp_35.png'
import miniapp36 from '@/assets/images/temp/missions/miniapp_36.jpg'
import miniapp37 from '@/assets/images/temp/missions/miniapp_37.jpg'
import miniapp38 from '@/assets/images/temp/missions/miniapp_38.jpg'
import miniapp39 from '@/assets/images/temp/missions/miniapp_39.png'
import miniapp40 from '@/assets/images/temp/missions/miniapp_40.jpg'
import miniapp41 from '@/assets/images/temp/missions/miniapp_41.jpg'
import miniapp42 from '@/assets/images/temp/missions/miniapp_42.jpg'
import miniapp43 from '@/assets/images/temp/missions/miniapp_43.png'
import miniapp44 from '@/assets/images/temp/missions/miniapp_44.jpg'
import miniapp45 from '@/assets/images/temp/missions/miniapp_45.jpg'
import miniapp46 from '@/assets/images/temp/missions/miniapp_46.jpg'
import miniapp47 from '@/assets/images/temp/missions/miniapp_47.jpg'
import miniapp48 from '@/assets/images/temp/missions/miniapp_48.jpg'
import miniapp9 from '@/assets/images/temp/missions/miniapp_9.png'
import monsterImage from '@/assets/images/temp/missions/monster.png'
import playImage from '@/assets/images/temp/missions/play.png'
import purchaseSkinsImage from '@/assets/images/temp/missions/purchase_skins.png'
import reviveImage from '@/assets/images/temp/missions/revive.png'
import secondLeague from '@/assets/images/temp/missions/second-league.png'
import spendStarsImage from '@/assets/images/temp/missions/spend_stars.png'
import springImage from '@/assets/images/temp/missions/spring.png'
import telegramBoostImage from '@/assets/images/temp/missions/telegram_boost.png'
import ticket from '@/assets/images/temp/missions/ticket.png'
import transactionImage from '@/assets/images/temp/missions/transaction1.png'
import walletImage from '@/assets/images/temp/missions/wallet.png'
import telegramImage from '@/assets/images/temp/socials/telegram.png'
import xImage from '@/assets/images/temp/socials/x.png'

export type MissionName =
  | 'subscribe_main_channel'
  | 'invite_1_friend'
  | 'invite_3_friend'
  | 'invite_5_friend'
  | 'connect_wallet'
  | 'first_transaction'
  | 'use_booster'
  | 'jump_to_score'
  | 'kill_monster'
  | 'invite_ref'
  | 'play_game'
  | 'catch_ticket'
  | 'unlock_league'
  | 'use_revive'
  | 'add_to_home_screen'
  | 'subscribe_community_chat'
  | 'subscribe_x'
  | 'purchase_in_shop_for_stars'
  | 'purchase_skin_for_stars'
  | 'boost_telegram_channel'
  | 'buy_skin'
  | 'daily_total_jump'
  | 'use_aimbot_booster'
  | 'use_jumper_booster'
  | 'use_magnet_booster'
  | 'go_to_miniapp_9'
  | 'go_to_miniapp_10'
  | 'go_to_miniapp_15'
  | 'go_to_miniapp_19'
  | 'go_to_miniapp_22'
  | 'go_to_miniapp_23'
  | 'go_to_miniapp_24'
  | 'go_to_miniapp_25'
  | 'go_to_miniapp_26'
  | 'go_to_miniapp_27'
  | 'go_to_miniapp_28'
  | 'go_to_miniapp_29'
  | 'go_to_miniapp_30'
  | 'go_to_miniapp_31'
  | 'go_to_miniapp_32'
  | 'go_to_miniapp_33'
  | 'go_to_miniapp_34'
  | 'go_to_miniapp_35'
  | 'go_to_miniapp_36'
  | 'go_to_miniapp_37'
  | 'go_to_miniapp_38'
  | 'go_to_miniapp_39'
  | 'go_to_miniapp_40'
  | 'go_to_miniapp_41'
  | 'go_to_miniapp_42'
  | 'go_to_miniapp_43'
  | 'go_to_miniapp_44'
  | 'go_to_miniapp_45'
  | 'go_to_miniapp_46'
  | 'go_to_miniapp_47'
  | 'go_to_miniapp_48'

export const MISSIONS_ORDER: Array<MissionName> = [
  'go_to_miniapp_48',
  'go_to_miniapp_47',
  'go_to_miniapp_46',
  'go_to_miniapp_45',
  'go_to_miniapp_44',
  'go_to_miniapp_43',
  'go_to_miniapp_42',
  'go_to_miniapp_41',
  'go_to_miniapp_40',
  'go_to_miniapp_39',
  'go_to_miniapp_38',
  'go_to_miniapp_37',
  'go_to_miniapp_36',
  'go_to_miniapp_35',
  'go_to_miniapp_34',
  'go_to_miniapp_33',
  'go_to_miniapp_32',
  'go_to_miniapp_31',
  'go_to_miniapp_30',
  'go_to_miniapp_29',
  'go_to_miniapp_28',
  'go_to_miniapp_27',
  'go_to_miniapp_26',
  'go_to_miniapp_25',
  'go_to_miniapp_24',
  'go_to_miniapp_23',
  'go_to_miniapp_22',
  'go_to_miniapp_19',
  'go_to_miniapp_15',
  'go_to_miniapp_10',
  'go_to_miniapp_9',
  'connect_wallet',
  'first_transaction',
  'invite_1_friend',
  'invite_3_friend',
  'invite_5_friend',
  'subscribe_main_channel',
  'subscribe_x',
  'subscribe_community_chat',
  'add_to_home_screen',
  'purchase_in_shop_for_stars',
  'purchase_skin_for_stars',
  'boost_telegram_channel',
  'play_game',
  'catch_ticket',
  'jump_to_score',
  'unlock_league',
  'kill_monster',
  'use_booster',
  'invite_ref',
  'use_revive',
  'use_aimbot_booster',
  'use_jumper_booster',
  'use_magnet_booster'
]

export const MISSIONS_IMAGES: Record<MissionName, string> = {
  subscribe_main_channel: telegramImage,
  invite_1_friend: oneUniImage,
  invite_3_friend: threeUniImage,
  invite_5_friend: fiveUniImage,
  connect_wallet: walletImage,
  first_transaction: transactionImage,
  use_booster: boosterImage,
  jump_to_score: jumpImage,
  kill_monster: monsterImage,
  invite_ref: oneUniImage,
  play_game: playImage,
  catch_ticket: ticket,
  unlock_league: secondLeague,
  use_revive: reviveImage,
  add_to_home_screen: homeScreenImage,
  subscribe_community_chat: communityImage,
  subscribe_x: xImage,
  purchase_in_shop_for_stars: purchaseSkinsImage,
  purchase_skin_for_stars: spendStarsImage,
  boost_telegram_channel: telegramBoostImage,
  buy_skin: telegramBoostImage,
  daily_total_jump: jumpImage,
  use_aimbot_booster: aimImage,
  use_jumper_booster: springImage,
  use_magnet_booster: magnetImage,
  go_to_miniapp_9: miniapp9,
  go_to_miniapp_10: miniapp10,
  go_to_miniapp_15: miniapp15,
  go_to_miniapp_19: miniapp19,
  go_to_miniapp_22: miniapp22,
  go_to_miniapp_23: miniapp23,
  go_to_miniapp_24: miniapp24,
  go_to_miniapp_25: miniapp25,
  go_to_miniapp_26: miniapp26,
  go_to_miniapp_27: miniapp27,
  go_to_miniapp_28: miniapp28,
  go_to_miniapp_29: miniapp29,
  go_to_miniapp_30: miniapp30,
  go_to_miniapp_31: miniapp31,
  go_to_miniapp_32: miniapp32,
  go_to_miniapp_33: miniapp33,
  go_to_miniapp_34: miniapp34,
  go_to_miniapp_35: miniapp35,
  go_to_miniapp_36: miniapp36,
  go_to_miniapp_37: miniapp37,
  go_to_miniapp_38: miniapp38,
  go_to_miniapp_39: miniapp39,
  go_to_miniapp_40: miniapp40,
  go_to_miniapp_41: miniapp41,
  go_to_miniapp_42: miniapp42,
  go_to_miniapp_43: miniapp43,
  go_to_miniapp_44: miniapp44,
  go_to_miniapp_45: miniapp45,
  go_to_miniapp_46: miniapp46,
  go_to_miniapp_47: miniapp47,
  go_to_miniapp_48: miniapp48
}
