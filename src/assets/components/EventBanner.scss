
.event-banner {
  position: relative;

  display: flex;
  flex-direction: column;
  align-items: center;

  width: 100%;
  max-width: 346px;
  border: 6px solid #ffd634;
  border-radius: 13px;
  background: var(--event-background);

  &__banner {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%, calc(-100% + 45px));
    pointer-events: none;
    width: 90%;
  }

  &__image {
    width: 100%;
    border-radius: 7px 7px 0 0;

    &_full {
      border-radius: 7px;
    }
  }

  &__description {
    color: #c2e2ff;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    white-space: pre;
    letter-spacing: normal;
  }

  &__loading {
    position: absolute;
    z-index: 1;
    pointer-events: none;
    opacity: 0;
    inset: 0;
    background-color: #064c8dbd;
    border-radius: inherit;

    display: flex;
    align-items: center;
    justify-content: center;

    font-size: 24px;
    color: #6db0ed;
    transition: opacity 0.3s;
    transition-delay: 1s;

    &_active {
      opacity: 1;
      pointer-events: auto;
    }
  }
}