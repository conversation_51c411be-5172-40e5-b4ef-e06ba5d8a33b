<script setup lang="ts">
import ModalWindow from '@/components/UI/ModalWindow.vue'
import VButton from '@/components/UI/VButton.vue'
import BoxRewardItem from '@/components/shop/BoxRewardItem.vue'
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import {
  LOOTBOX_REWARD_ID_TO_IMAGE,
  LUCKY_LOOTBOX_REWARD_ID_TO_CHANCE,
  RAINBOW_LOOTBOX_REWARD_ID_TO_CHANCE,
  JUSTICE_REWARD_ID_TO_CHANCE,
  type LootboxRewardId
} from '@/constants/lootboxes.ts'
import type { LootBoxOffer, LootBoxReward, LootBoxType, ShopItemPrice } from '@/services/openapi'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import { formatNumberToShortString } from '@/utils/number'

const { t } = useI18n()

const LOOTBOX_TYPE_TO_REWARDS: Partial<Record<LootBoxType, Record<number, number>>> = {
  luckyLootBox: LUCKY_LOOTBOX_REWARD_ID_TO_CHANCE,
  rainbowLootBox: RAINBOW_LOOTBOX_REWARD_ID_TO_CHANCE,
  justiceLootBox: JUSTICE_REWARD_ID_TO_CHANCE
}

const {
  isOpen = false,
  lootboxType,
  image = '',
  hours = 0,
  minutes = 0,
  seconds = 0,
  shineImage = '',
  isFree = false,
  isFreeAvailable = false,
  rewards = [],
  offers = [],
  isLoading
} = defineProps<{
  isOpen: boolean
  lootboxType: LootBoxType
  image: string
  shineImage?: string
  hours: number
  minutes: number
  seconds: number
  isFree?: boolean
  isFreeAvailable?: boolean
  rewards: LootBoxReward[] | undefined
  offers: LootBoxOffer[] | undefined
  isLoading: boolean
}>()

const emit = defineEmits(['close', 'getFree', 'purchase'])

const sortedOffers = computed(() => {
  return offers.slice().sort((a, b) => a.price.displayPrice.amount - b.price.displayPrice.amount)
})

const purchase = (id: number, value: number, price: ShopItemPrice) => {
  hapticsService.triggerImpactHapticEvent('light')
  emit('purchase', id, value, price)
}

const getFree = () => {
  hapticsService.triggerImpactHapticEvent('light')
  emit('getFree', lootboxType)
}

const getChance = (id: number) => {
  return LOOTBOX_TYPE_TO_REWARDS[lootboxType]?.[id] ?? 0
}
</script>

<template>
  <ModalWindow
    :is-open="isOpen"
    :title="t(`lootboxes.${lootboxType}`)"
    class="box-details"
    short
    :isLoading="isLoading"
    @close="() => emit('close')"
  >
    <div class="h-[30%] flex items-center justify-center">
      <img class="h-[100%] z-10" :src="image" alt="box" />
      <div class="shine-rotate-animation h-[80%] z-0">
        <img class="shine-pulse-animation h-[50%]" :src="shineImage" alt="shineImage" />
      </div>
    </div>

    <div class="relative w-full h-[10%] flex items-center justify-center gap-2">
      <VButton
        v-if="isFree && isFreeAvailable"
        class="!w-[32%]"
        type="accent"
        size="medium"
        :text="t('shop.free')"
        @click="getFree"
      />
      <div
        v-for="item in sortedOffers.filter(offer => !isFree || offer.value !== 1 || !isFreeAvailable)"
        :key="item.id"
        class="relative w-[32%]"
      >
        <VButton
          class="!w-full !text-[17px]"
          :image-class="CURRENCY_TO_IMAGE_CLASS[item.price.displayPrice.currency] + ' !w-5'"
          :text="formatNumberToShortString(item.price.displayPrice.amount)"
          type="success"
          size="medium"
          @click="() => purchase(item.id, item.value, item.price)"
        >
          <template #pre-content>
            <p class="text-[14px] text-shadow text-shadow_black">x{{ item.value }}</p>
          </template>
        </VButton>
        <div v-if="isFree && !isFreeAvailable && item.value === 1" class="w-max flex absolute -top-1 left-0 h-4 -translate-y-full bg-[#0F4589B2] rounded-[3px] text-[12px] leading-[16px] text-[#FFFFFF]">
          <div class="h-full px-2 bg-[#0F4589] rounded-[3px]">
            {{ t('nextFreeAfter') }}
          </div>
          <div class="w-[60px]">
            <CountdownTimerManual
              class="text-shadow text-shadow_black text-shadow_thin"
              :hours="hours"
              :minutes="minutes"
              :seconds="seconds"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="box-details__rewards-block">
      <div class="rewards-list">
        <p class="rewards-list__description">
          {{ t('shop.boxDescription') }}
        </p>
        <div
          v-for="reward in rewards"
          :key="reward.id"
          class="rewards-list__item-wrapper"
          :class="{
            'rewards-list__item-wrapper_style-low': getChance(reward.id) < 5,
            'rewards-list__item-wrapper_style-medium':
              getChance(reward.id) < 40 && getChance(reward.id) >= 5,
            'rewards-list__item-wrapper_style-high': getChance(reward.id) >= 40
          }"
        >
          <div class="rewards-list__item rewards-list__item_style">
            <BoxRewardItem
              class="w-full"
              :description="reward.description"
              :src="LOOTBOX_REWARD_ID_TO_IMAGE[reward.id as LootboxRewardId]"
              :multiplier="reward.multiplier"
              :chance="getChance(reward.id)"
            />
          </div>
        </div>
      </div>
    </div>
  </ModalWindow>
</template>

<style lang="scss">
.box-details {
  height: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-align: center;

  .modal-window__title {
    margin: 0;
  }

  &__rewards-block {
    position: relative;
    width: 100%;
    height: 50%;
    border-radius: 8px;
    background: #0f4589;
    overflow-y: auto;
    padding-bottom: 15px;
  }
}

.rewards-list {
  overflow: visible;
  display: grid;
  height: 100%;
  grid-template-columns: 31% 31% 31%;
  grid-auto-rows: min-content;
  row-gap: 9px;
  justify-content: space-between;
  padding: 9px;

  &__description {
    grid-column: span 3;
    font-size: 14px;
    line-height: 20px;
    font-weight: 900;
    color: #6db0ed;
  }

  &__item-wrapper {
    position: relative;

    &_style {
      &-medium {
        --skin-border-color: #7bd3ff;
        --skin-background: linear-gradient(180deg, #0ecef62e 0%, #00a6ff99 100%);
        --decorative-color: #9ff9ff;
      }

      &-low {
        --skin-border-color: #ff7ef3;
        --skin-background: linear-gradient(180deg, #ff2ce32e 0%, #ff2ce399 100%);
        --decorative-color: #ffb9e7;
      }

      &-high {
        --skin-border-color: #91ff70;
        --skin-background: linear-gradient(180deg, #5fdc382e 0%, #5fdc3899 100%);
        --decorative-color: #ccff7f;
      }
    }

    &::before {
      content: '';
      position: absolute;
      width: 36px;
      height: 4px;
      top: 0;
      right: 20%;
      background: linear-gradient(to right, var(--decorative-color) 60%, white 60%);
      z-index: 2;
    }

    &::after {
      content: '';
      position: absolute;
      width: 36px;
      height: 4px;
      bottom: 0;
      left: 20%;
      background: linear-gradient(to right, white 40%, var(--decorative-color) 40%);
      z-index: 2;
    }
  }

  &__item {
    max-height: 98px;
    position: relative;
    z-index: 1;

    background: var(--skin-background);
    border: 4px solid var(--skin-border-color);
    border-radius: 8px;
  }
}
</style>
