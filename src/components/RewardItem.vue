<script setup lang="ts">
import { formatNumberToShortString, formatSecondsToTime } from '@/utils/number'
import { isLootboxRewardType, isTimeRewardType } from '@/types'
import type { RewardType } from '@/services/openapi';
import { getCurrencyRealAmount } from '@/constants/currency';

withDefaults(defineProps<{
  type: RewardType,
  amount: number,
  showAmount?: boolean,
  image: string,
  valueClass?: string,
  dontGetCurrencyRealAmount?: boolean
}>(), {
  showAmount: true
})
</script>

<template>
  <div class="relative flex items-center justify-center">
    <div v-if="isLootboxRewardType(type)" class="shine-pulse-radial-animation w-auto h-[150%] aspect-square"></div>
    <div v-else-if="type === 'refsFake'" class="shine-rotate-animation w-full h-full">
      <img
        class="shine-pulse-animation object-contain w-[110%] h-[110%]"
        src="@/assets/images/temp/big-icons/shine-blue.png"
      />
    </div>
    <img class="absolute object-contain w-full h-full" :src="image" />
    <span
      v-if="showAmount && type !== 'skin' && amount > 0"
      class="absolute bottom-0 w-full text-center text-shadow text-shadow_black text-[20px]"
      :class="valueClass"
    >
      {{ !dontGetCurrencyRealAmount && type === 'ton'
        ? getCurrencyRealAmount(amount, 'ton') + ' TON'
        : isTimeRewardType(type)
          ? formatSecondsToTime(amount)
          : formatNumberToShortString(amount) }}
    </span>
  </div>
</template>
