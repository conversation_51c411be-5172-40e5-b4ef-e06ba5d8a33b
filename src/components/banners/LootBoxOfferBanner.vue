<script setup lang="ts">
import eventBanner from '@/assets/images/temp/banners/loot-box-offer/banner.png'
import eventImage from '@/assets/images/temp/banners/loot-box-offer/image.png'
import starImage from '@/assets/images/temp/currency/hard-coin.png'
import EventBanner from '@/components/events/EventBanner.vue'
import { usePurchase } from '@/composables/usePurchase'
import { useTime } from '@/composables/useTime.ts'
import { useWindowQueue } from '@/composables/useWindowQueue'
import dayjs from '@/plugins/dayjs'
import { usePlayerState } from '@/services/client/usePlayerState'
import leaguesService from '@/services/local/leagues'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { useToast } from '@/stores/toastStore.ts'
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const HAS_SEEN_OFFER_BANNER_KEY = 'hasSeenLootBoxOfferBanner'

const { t } = useI18n()
const { showToast } = useToast()
const router = useRouter()

const { isOpen } = defineProps<{ isOpen: boolean }>()
const emit = defineEmits(['close'])

const { getCurrentDayEnd } = useTime()

const { purchase } = usePurchase()
const isPendingOpenLootbox = ref(false)

const {
  isOpen: isOpenWelcomeBanner,
  openWindowInQueue,
  closeWindowInQueue
} = useWindowQueue('loot-box-offer-banner')
const { playerState } = usePlayerState()

const checkBanner = async () => {
  const hasAccess = leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'offers')
  if (!hasAccess || playerState.value!.tutorial) {
    return false
  }
  const offerBannerOpenDate = await cloudStorageService.load<number>(HAS_SEEN_OFFER_BANNER_KEY)
  const now = Math.floor(dayjs().valueOf() / 1000)
  const isSameDay =
    offerBannerOpenDate && getCurrentDayEnd(now) === getCurrentDayEnd(offerBannerOpenDate)
  if (isSameDay) return false
  cloudStorageService.save(HAS_SEEN_OFFER_BANNER_KEY, now)
  return true
}

const purchaseLootBoxes = () => {
  isPendingOpenLootbox.value = true
  purchase('justiceLootBox', 531, 5, { amount: 200, currency: 'hard' })
    .catch(reason => {
      if (reason.message === 'NOT_ENOUGH_FUNDS') {
        showToast('Not enough funds', 'warning')
        router.push({ name: 'shop', query: { scrollTo: 'hard' } })
        closeBanner()
      }
    })
    .finally(() => {
      isPendingOpenLootbox.value = false
    })
}

const closeBanner = () => {
  emit('close')
  closeWindowInQueue()
}

onMounted(async () => {
  const isOpen = await checkBanner()
  if (isOpen) {
    openWindowInQueue()
  }
})
</script>

<template>
  <EventBanner
    class="loot-box-offer-banner w-full h-full"
    :isOpen="isOpenWelcomeBanner || isOpen"
    :buttons="[{ text: '200', type: 'success', image: starImage, onClick: purchaseLootBoxes }]"
    :banner="eventBanner"
    :image="eventImage"
    imageFullSize
    showCloseButton
    :is-loading="isPendingOpenLootbox"
    @close-button="closeBanner"
  >
    <div
      class="bg-[#7A9AAE99] rounded-[9px] text-white w-[127px] h-[109px] mx-auto relative flex items-center justify-center"
    >
      <div class="absolute flex items-center justify-center -translate-y-[8px]">
        <div class="shine-pulse-radial-animation w-[90px] h-[90px]"></div>
        <img
          class="absolute w-[80px]"
          src="@/assets/images/temp/big-icons/justiceBox.png"
          alt="loot box"
        />
      </div>
      <span
        class="absolute bottom-[10px] text-white text-shadow text-shadow_thin text-shadow_blue text-[32px]"
      >
        x5
      </span>
    </div>
    <p
      class="text-center text-[14px] whitespace-normal px-[5px] text-white text-shadow text-shadow_blue my-2"
    >
      {{ t('lootBoxOffer.description') }}
    </p>
  </EventBanner>
</template>

<style lang="scss">
.loot-box-offer-banner {
  top: 0;

  .event-banner {
    transform: translateY(20px);
    max-width: 360px;
    border-radius: 22px;

    &__banner {
      width: 90%;
      top: -20px;
    }

    &__image {
      border-radius: 15px;
    }

    &__flag {
      width: 100%;
      top: -35px;
      right: 195px;
      position: relative;
    }

    .close-button {
      --close-btn-background-color: #FFFFFF;
    }
  }
}
</style>
