<script setup lang="ts">
import { computed, ref, useTemplateRef } from 'vue'

import heistBanner from '@/assets/images/temp/banners/exploiters/banner-1.png'
import collectBanner from '@/assets/images/temp/banners/exploiters/banner-2.png'
import lastChanceBanner from '@/assets/images/temp/banners/exploiters/banner-3.png'
import heistImage from '@/assets/images/temp/banners/exploiters/image-1.png'
import collectImage from '@/assets/images/temp/banners/exploiters/image-2.png'
import tonsImage from '@/assets/images/temp/banners/exploiters/tons.png'
import starImage from '@/assets/images/temp/currency/hard-coin.png'
import chainSmallImage from '@/assets/images/temp/locks/chain-small.png'
import lockImage from '@/assets/images/temp/locks/lock.png'
import EventBanner from '@/components/events/EventBanner.vue'
import PurchaseCurrencyWindow from '@/components/shop/PurchaseCurrencyWindow.vue'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import type { ButtonProps } from '@/components/UI/VButton.vue'
import { usePurchase } from '@/composables/usePurchase.ts'
import { getCurrencyFormattedAmount, getCurrencyRealAmount } from '@/constants/currency.ts'
import { useClaimLockedBalance } from '@/services/client/useClaimLockedBalance.ts'
import { useDenyWriteOffRansom } from '@/services/client/useDenyWriteOffRansom.ts'
import { addToPlayerState, usePlayerState } from '@/services/client/usePlayerState'
import { useShopItems } from '@/services/client/useShopItems.ts'
import { useExploitersStore } from '@/stores/exploitersStore.ts'
import { useRewardStore } from '@/stores/rewardStore.ts'
import { customTruncate, formatNumberWithSeparator } from '@/utils/number.ts'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const { isOpen, isOpenWelcomeBanner, isOpenCollectBanner, isOpenLastChanceBanner } = defineProps<{
  isOpen: boolean
  isOpenWelcomeBanner: boolean
  isOpenCollectBanner: boolean
  isOpenLastChanceBanner: boolean
}>()

const emit = defineEmits(['close', 'start-game'])

const rewardStore = useRewardStore()
const exploitersStore = useExploitersStore()
const purchaseStarsWindowRef = useTemplateRef('purchaseStarsWindowRef')
const onMissingStarsPurchased = ref<Function | null>(null)

const { updatePlayerState } = addToPlayerState()

const { playerState } = usePlayerState()
const { shopItems } = useShopItems()
const { purchaseRansom: purchaseRansomMutation } = usePurchase()
const { denyWriteOffRansom } = useDenyWriteOffRansom()
const { claimLockedBalance } = useClaimLockedBalance()

const closeBanner = async () => {
  if (isOpenCollectBanner) {
    await onCollect()
  } else if (isOpenLastChanceBanner) {
    await denyWriteOffRansom().then(() => emit('close'))
  } else {
    emit('close')
  }
}

const onStartGame = () => {
  closeBanner()
  emit('start-game')
}

const onCollect = async () => {
  await claimLockedBalance().then(result => {
    if (result) {
      const prevBalance = playerState.value!.ton ?? 0
      rewardStore.showReward([
        {
          type: 'ton',
          prevValue: getCurrencyRealAmount(prevBalance, 'ton'),
          value: heistTonAmount.value
        }
      ])
    }
    emit('close')
  })
}

const ransomOffer = computed(() => {
  return shopItems.value?.ransom!
})

const hardOffers = computed(() => {
  return shopItems.value?.hard ?? []
})

const purchaseRansom = async () => {
  purchaseRansomMutation(ransomOffer.value.price.displayPrice.amount)
    .then(() => {
      const prevBalance = playerState.value!.ton ?? 0
      rewardStore.showReward(
        [
          {
            type: 'ton',
            prevValue: getCurrencyRealAmount(prevBalance, 'ton'),
            value: heistTonAmount.value
          }
        ],
        () => {
          const formattedTonAmount = getCurrencyFormattedAmount(heistTonAmount.value, 'ton')
          updatePlayerState('ton', formattedTonAmount)
          emit('close')
        }
      )
    })
    .catch((reason: any) => {
      if (reason.message === 'NOT_ENOUGH_FUNDS') {
        purchaseStarsWindowRef.value?.openWindow(hardOffers.value, 'hard', reason.payload)
        onMissingStarsPurchased.value = purchaseRansom
      }
    })
}

const mobsToKill = computed(() => {
  return playerState.value!.dailyWriteOffState?.mobsToKill ?? 0
})

const heistTonAmount = computed(() => {
  const tonLockedBalance = playerState.value!.dailyWriteOffState?.tonLockedBalance ?? 0
  return getCurrencyRealAmount(tonLockedBalance, 'ton')
})

const eventButtons = computed<Array<ButtonProps>>(() => {
  if (isOpenCollectBanner) {
    return [{ text: 'Collect', type: 'success', onClick: onCollect }]
  } else if (isOpenLastChanceBanner) {
    return [
      { text: 'Let it go!', type: 'accent', onClick: closeBanner },
      {
        text: ransomOffer.value.price.displayPrice.amount,
        image: starImage,
        type: 'success',
        onClick: purchaseRansom
      }
    ]
  }
  return [{ text: 'Get it back', type: 'success', onClick: onStartGame }]
})

const eventBanner = computed(() => {
  if (isOpenCollectBanner) {
    return collectBanner
  } else if (isOpenLastChanceBanner) {
    return lastChanceBanner
  }
  return heistBanner
})

const eventImage = computed(() => {
  if (isOpenCollectBanner) {
    return collectImage
  }
  return heistImage
})

const eventDescription = computed(() => {
  if (isOpenCollectBanner) {
    return t('exploiters.collectDescription')
  } else if (isOpenLastChanceBanner) {
    return t('exploiters.lastChanceDescription')
  }
  return t('exploiters.welcomeDescription')
})
</script>

<template>
  <div>
    <EventBanner
      class="exploiters-banner w-full h-full"
      :class="{
        'collect-banner': isOpenCollectBanner,
        'last-chance-banner': isOpenLastChanceBanner
      }"
      :isOpen="isOpen || isOpenWelcomeBanner || isOpenCollectBanner || isOpenLastChanceBanner"
      :buttons="eventButtons"
      :banner="eventBanner"
      :image="eventImage"
      :show-close-button="!isOpenLastChanceBanner"
      image-full-size
      @close="!isOpenLastChanceBanner && closeBanner()"
    >
      <CountdownTimerManual
        v-if="isOpenWelcomeBanner || isOpen"
        class="exploiters-banner__timer text-[12px] leading-[16px] text-[#FFFFFF]"
        :days="exploitersStore.days"
        :hours="exploitersStore.hours"
        :minutes="exploitersStore.minutes"
        :seconds="exploitersStore.seconds"
        digital
      />

      <div class="exploiters-banner__description text-center text-wrap">
        <p
          class="text-[14px] leading-[16px] text-white text-shadow text-shadow_black text-shadow_thin"
        >
          {{ eventDescription }}
        </p>
      </div>

      <div class="exploiters-banner__amount">
        <div class="relative flex items-center justify-center w-full h-full">
          <img :src="tonsImage" class="w-[76px] h-[46px]" alt="tons image" />
          <p
            v-if="heistTonAmount"
            class="absolute -bottom-[10px] text-[16px] leading-[18px] text-white text-shadow text-shadow_black text-shadow_thin"
          >
            {{ formatNumberWithSeparator(customTruncate(heistTonAmount)) }}
          </p>
          <div v-if="isOpenLastChanceBanner">
            <img
              class="w-[40px] bottom-[5px] absolute left-1/2 -translate-x-[95%]"
              :src="chainSmallImage"
              alt="chain"
            />
            <img
              class="w-[40px] bottom-[5px] absolute right-1/2 translate-x-[95%] -scale-x-100"
              :src="chainSmallImage"
              alt="chain"
            />
            <img
              class="w-[17px] bottom-[8px] absolute left-1/2 -translate-x-1/2"
              :src="lockImage"
              alt="lock"
            />
          </div>
        </div>
      </div>

      <div v-if="isOpenWelcomeBanner || isOpen" class="exploiters-banner__task w-full">
        <i18n-t
          class="text-[20px] leading-[22px] text-white text-shadow text-shadow_thin"
          tag="p"
          keypath="exploiters.task"
        >
          <template v-slot:count>
            <span class="text-[#FFE134]">{{ mobsToKill }} Exploiters</span>
          </template>
        </i18n-t>
      </div>

      <div v-if="isOpenLastChanceBanner" class="w-full flex justify-end px-2 py-1">
        <p class="text-[16px] leading-[22px] text-shadow text-shadow_yellow text-shadow_thin">
          {{ t('exploiters.lastChance') }}
        </p>
      </div>
    </EventBanner>
  </div>
  <PurchaseCurrencyWindow ref="purchaseStarsWindowRef" />
</template>

<style lang="scss">
.exploiters-banner {
  top: 0;
  --event-background: linear-gradient(360deg, #0281c7 0%, #5f1f5d 92.65%);
  .event-banner {
    border-color: #ff0901;

    &__banner {
      width: 360px;
      top: -25px;
    }

    .close-button {
      z-index: 9999;
      --close-btn-background-color: #31002f;
    }
  }

  &__timer {
    position: absolute;
    bottom: 320px;
    left: 35%;
    width: 30%;
    height: 21px;
    background: #ca0000;
    border-radius: 5px;
    display: flex !important;
    align-items: center;
    justify-content: center;
  }

  &__description {
    position: absolute;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    bottom: 275px;
    width: 90%;
    left: 5%;
  }

  &__amount {
    position: absolute;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    bottom: 150px;
    left: 35%;
  }

  &__task {
    bottom: 80px;
    position: absolute;
    border-radius: 10px;
    padding: 10px;
    left: 0;
    background-color: #32004da6;
  }
}

.collect-banner {
  .event-banner {
    border-color: #59ff3a;
  }
}

.last-chance-banner {
  .event-banner {
    border-color: #ffd411;
  }
}
</style>
