<script lang="ts" setup>
import { useI18n } from 'vue-i18n';
import {
  CURRENCY_IMAGES,
  CURRENCY_NAMES,
  getCurrencyRealAmount
} from '@/constants/currency';
import dayjs from '@/plugins/dayjs';
import LoaderText from '../LoaderText.vue';
import type { ExternalCurrencyType, RewardInfo } from '@/services/openapi';
import { REWARD_TO_IMAGE } from '@/composables/useIconImage';

export type Transaction = {
  amount: number
  createdAt: string
  currency: ExternalCurrencyType
  id: string
  status: string
  rewards?: Array<RewardInfo>
  fee?: number
  updatedAt?: string
  wallet?: string
}

const { t } = useI18n()

defineProps<{
  transactions: Transaction[];
  isLoading: boolean;
}>()

const emit = defineEmits(['showDetails'])
</script>

<template>
  <div class="transaction-history">
    <div v-if="isLoading" class="transaction-history__empty-list">
      <LoaderText is-loading />
    </div>
    <div v-else-if="!transactions.length" class="transaction-history__empty-list">
      {{ t('wallet.emptyHistory') }}
    </div>
    <div v-else class="transaction-history__list space-y-[11px] py-2">
      <div
        v-for="transaction in transactions"
        :key="transaction.id"
        class="asset"
        @click="() => emit('showDetails', transaction.id)"
      >
        <img v-if="transaction.rewards" class="asset__image" :src="REWARD_TO_IMAGE[transaction.rewards[0].type]" alt="reward" />
        <img v-else class="asset__image" :src="CURRENCY_IMAGES[transaction.currency]" alt="currency" />
        <div class="asset__info">
          <div v-if="transaction.rewards" class="asset__info-line">
            <p class="text-[20px] font-extrabold uppercase">
              {{ t(`reward.${transaction.rewards[0].type}`) }}
            </p>
            <p class="text-[16px] text-shadow text-shadow_black">
              {{ transaction.rewards[0].value }}
            </p>
          </div>
          <div v-else class="asset__info-line">
            <p class="text-[20px] font-extrabold uppercase">
              {{ CURRENCY_NAMES[transaction.currency] }}
            </p>
            <p class="text-[16px] text-shadow text-shadow_black">
              {{ getCurrencyRealAmount(transaction.amount, transaction.currency) }}
            </p>
          </div>
          <div class="asset__info-line text-[14px] text-[#6DB0ED] font-bold">
            <p v-if="transaction.updatedAt">
              {{ dayjs(transaction.updatedAt).format('DD.MM.YYYY HH:mm:ss') }}
            </p>
            <p v-else>
              {{ dayjs(transaction.createdAt).format('DD.MM.YYYY HH:mm:ss') }}
            </p>
            <p
              :class="[
                'transaction-status text-[16px] font-extrabold',
                `transaction-status_${transaction.status}`
              ]"
            >
              {{ t(transaction.status) }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.transaction-history {
  height: inherit;

  &__empty-list {
    height: inherit;
    display: flex;
    align-items: center;
    justify-content: center;

    color: #274E8880;
    font-size: 16px;
    line-height: 22px;
    font-weight: 800;
  }

  &__list {
    position: relative;
    height: inherit;
    max-height: inherit;
    overflow-y: auto;

    // &::before {
    //   content: '';
    //   display: block;
    //   position: sticky;
    //   top: 0;
    //   z-index: 1;
    //   width: 100%;
    //   height: 28px;
    //   background: linear-gradient(180deg, #C8EEFE 14.82%, rgba(176, 228, 251, 0) 68.56%);
    // }

    // &::after {
    //   content: '';
    //   display: block;
    //   position: sticky;
    //   bottom: 0;
    //   z-index: 1;
    //   width: 100%;
    //   height: 28px;
    //   background: linear-gradient(360deg, #9bdcf7 14.82%, rgba(168, 225, 250, 0) 68.56%);
    // }
  }
}
</style>
