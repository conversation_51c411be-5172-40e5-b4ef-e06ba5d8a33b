<script setup lang="ts">
import { computed, ref } from 'vue';
import { useFloating, offset } from '@floating-ui/vue';
import { useI18n } from 'vue-i18n';
import leaguesService from '@/services/local/leagues';

import lockImage from '@/assets/images/temp/locks/lock.png'
import chainSmallImage from '@/assets/images/temp/locks/chain-small.png'
import chainMediumImage from '@/assets/images/temp/locks/chain-medium.png'
import chainBigImage from '@/assets/images/temp/locks/chain-big.png'
import type { LeagueFeature } from '@/services/openapi';
import { usePlayerState } from '@/services/client/usePlayerState';

const { t } = useI18n()

const LOCK_CLASS: Record<LeagueFeature, string> = {
  'dailyReward': 'w-[14px] bottom-0',
  'onePercentEvent': 'w-[14px] bottom-0',
  'hotRecordEvent': 'w-[14px] bottom-0',
  'tonMiningEvent': 'w-[14px] bottom-0',
  'offers': 'w-[14px] bottom-0',
  'customCoinEvent': 'w-[14px] bottom-0',
  'dynamicCoins': 'w-[14px] bottom-0',
  'farming': 'w-[21px] -bottom-[14px]',
  'withdraw': 'w-[19px] -bottom-[18px]',
  'lives': '',
}

const CHAIN_CLASS: Record<LeagueFeature, string> = {
  'dailyReward': 'w-[33px] -bottom-[4px]',
  'onePercentEvent': 'w-[33px] -bottom-[4px]',
  'hotRecordEvent': 'w-[33px] -bottom-[4px]',
  'tonMiningEvent': 'w-[33px] -bottom-[4px]',
  'offers': 'w-[33px] -bottom-[4px]',
  'customCoinEvent': 'w-[33px] -bottom-[4px]',
  'dynamicCoins': 'w-[33px] -bottom-[4px]',
  'farming': 'w-[60px] -bottom-[16px]',
  'withdraw': 'w-[87px] -bottom-[29px]',
  'lives': '',
}

const CHAINS: Record<LeagueFeature, string> = {
  'dailyReward': chainSmallImage,
  'onePercentEvent': chainSmallImage,
  'hotRecordEvent': chainSmallImage,
  'tonMiningEvent': chainSmallImage,
  'offers': chainSmallImage,
  'customCoinEvent': chainSmallImage,
  'dynamicCoins': chainSmallImage,
  'farming': chainMediumImage,
  'withdraw': chainBigImage,
  'lives': '',
}

const props = withDefaults(defineProps<{
  userLeague: number,
  placement: 'top' | 'bottom' | 'left' | 'right',
  feature: LeagueFeature
  class?: string
}>(), {
  class: ''
})

const emit = defineEmits(['click'])

const isOpen = ref(false);
const requiredLeague = leaguesService.getRequiredLeague(props.feature);
const isFeatureBlocked = computed(() => props.userLeague < requiredLeague);

const reference = ref<null | HTMLDivElement>(null);
const floating = ref(null);
const { floatingStyles } = useFloating(reference, floating, {
  placement: props.placement,
  middleware: [offset(10)],
});

const checkLeague = (e: Event) => {
  if (isFeatureBlocked.value) {
    e.stopPropagation();
    e.preventDefault();
    return;
  } else {
    emit('click');
  }
}

const showTootip = () => {
  if (isFeatureBlocked.value) {
    isOpen.value = true;
  }
}

const hideTootip = () => {
  isOpen.value = false;
}

const { playerState } = usePlayerState()

const progressiveOfferId = computed(() => playerState.value?.progressiveOffers.find(offer => offer.usageDynamicCoins)?.id ?? 10000)

const getFeatureText = (feature: LeagueFeature) => {
  if (feature === 'dynamicCoins') {
    return progressiveOfferId.value === 10000 ? t('features.dynamicCoins_1') : t('features.dynamicCoins_2')
  }
  return t(`features.${feature}`)
}
</script>

<template>
  <div
    class="league-blocker"
    :class="{
      'league-blocker_block': isFeatureBlocked,
      [props.class]: true
    }"
    ref="reference"
    @click="checkLeague"
    @focus="showTootip"
    @blur="hideTootip"
    tabindex="0"
  >
    <slot></slot>
    <template v-if="isFeatureBlocked">
      <img
        class="league-blocker__locks absolute left-1/2 -translate-x-[95%]"
        :class="CHAIN_CLASS[feature]"
        :src="CHAINS[feature]"
      />
      <img
        class="league-blocker__locks absolute right-1/2 translate-x-[95%] -scale-x-100"
        :class="CHAIN_CLASS[feature]"
        :src="CHAINS[feature]"
      />
      <img
        class="league-blocker__locks absolute left-1/2 -translate-x-1/2"
        :class="LOCK_CLASS[feature]"
        :src="lockImage"
      />
    </template>
  </div>
  <div v-if="isOpen" ref="floating" class="bg-white rounded-[15px] py-[14px] px-2" :style="floatingStyles">
    <i18n-t
      class="text-[13px] leading-[17px] text-[#1E4073] text-center whitespace-pre"
      tag="p"
      keypath="leagues.blocker"
    >
      <template v-slot:league>
        <span class="text-[#FF8C00]">
          {{ requiredLeague }} {{ t('leagues.league') }}
        </span>
      </template>
      <template v-slot:feature>
        <span class="text-[#FF8C00]">
          {{ getFeatureText(feature) }}
        </span>
      </template>
    </i18n-t>
    <!-- <div
      ref="floatingArrow"
      :style="{
        position: 'absolute',
        left:
          middlewareData.arrow?.x != null
            ? `${middlewareData.arrow.x}px`
            : '',
        top:
          middlewareData.arrow?.y != null
            ? `${middlewareData.arrow.y}px`
            : '',
      }"
    ></div> -->
  </div>
</template>

<style lang="scss" scoped>
.league-blocker {
  position: relative;

  &_block > * {
    pointer-events: none;
    filter: brightness(0.5) contrast(130%);
  }
  
  &__locks {
    filter: brightness(1) contrast(100%);
  }
}
</style>
