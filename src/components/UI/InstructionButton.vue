<script setup lang="ts">
import instructionButtonImage from '@/assets/images/temp/question-mark.png'
import { useEventInstructionCheck } from '@/composables/useInstructionCheck.ts'
import { type InstructionType } from '@/constants/instructions'
import { useInstructionStore } from '@/stores/instructionStore.ts'
import { onMounted } from 'vue';

const { instructionType, instructionCheck } = defineProps<{
  instructionType: InstructionType
  instructionCheck?: boolean
}>()

const { checkInstruction } = useEventInstructionCheck()
const instructionStore = useInstructionStore()

const showInstruction = () => {
  instructionStore.showInstruction(instructionType)
}

onMounted(() => {
  if (instructionCheck) {
    checkInstruction(instructionType).then(result => {
      if (result) {
        instructionStore.showInstruction(instructionType)
      }
    })
  }
})
</script>

<template>
  <img
    class="instruction-button"
    :src="instructionButtonImage"
    alt="instruction button"
    @click="showInstruction"
  />
</template>

<style lang="scss">
.instruction-button {
  width: 28px;
  height: 28px;
  border-radius: 28px;
  background: linear-gradient(360deg, #ffd900 0%, #ff9b30 92.65%);
  box-shadow: inset 0 -4px #f7781e;
  border: 2px solid #9a4001;
  z-index: 10;
}
</style>
