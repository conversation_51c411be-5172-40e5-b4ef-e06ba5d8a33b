<script setup lang="ts">
import VButton from '@/components/UI/VButton.vue';
import { useI18n } from 'vue-i18n'
import { useClanInfo, useStartClanEvent } from '@/services/client/useClans'
import AvatarItem from '@/components/UI/AvatarItem.vue';
import BalanceItem from '@/components/UI/BalanceItem.vue';
import { formatNumberToShortString } from '@/utils/number';
import
  LeaderboardList,
  { type LeaderboardCurrency } from '@/components/events/LeaderboardList.vue';
import { computed, ref, watch } from 'vue';
import { openTelegramLink } from '@telegram-apps/sdk';
import clanImage from '@/assets/images/temp/clan-badge.png'
import { cloudStorageService } from '@/shared/storage/cloudStorageService';
import ClanRewardScreen from '@/components/rewards/ClanRewardScreen.vue';
import { useToast } from '@/stores/toastStore';
import { useClanEventStore } from '@/stores/clanEventStore.ts'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue';

const { t } = useI18n()

const props = defineProps<{
  userClan: number
}>()

const { isClanEventActive, days, hours, minutes, seconds } = useClanEventStore()
const { showToast } = useToast()
const { clanInfo } = useClanInfo(props.userClan)
const { startClanEvent } = useStartClanEvent()

const mappedUsersList = computed(() => {
  if (!clanInfo.value) return []
  return clanInfo.value.list.map(user => {
    return {
      id: user.id,
      name: (user.firstName + ' ' + user.lastName).trim(),
      league: user.leagueLevel,
      balance: user.rating,
      currency: 'tickets' as LeaderboardCurrency,
      isLeader: !!user.isClanLeader
    }
  })
})

const startEvent = () => {
  startClanEvent().then(() => {
    showToast('Clan Event has been started', 'info')
  })
}

const isOpenClanRewardScreen = ref(false)
const CURRENT_USER_CLAN_KEY = 'currentUserClan'
const showClanRewardScreen = (id: number) => {
  isOpenClanRewardScreen.value = true
  cloudStorageService.save(CURRENT_USER_CLAN_KEY, id)
  setTimeout(() => isOpenClanRewardScreen.value = false, 3000)
}
watch(clanInfo, (newValue) => {
  if (newValue?.clanId) {
    cloudStorageService.load(CURRENT_USER_CLAN_KEY).then(res => {
      if (!res || res !== newValue.clanId) {
        showClanRewardScreen(newValue.clanId)
      }
    })
  }
}, { immediate: true })
</script>

<template>
  <div v-if="clanInfo" class="view-container menu-item clans-view flex flex-col">
    <div class="flex-0 bg-[#00EEFF66] rounded-[11px] px-2 py-[10px] space-y-2 mb-[10px]">
      <div class="flex items-center gap-x-3">
        <AvatarItem :src="clanImage" size="63" />
        <div class="space-y-3">
          <div class="text-[20px] text-shadow text-shadow_black">
            {{ clanInfo.clanName }}
          </div>
          <div class="flex gap-x-5">
            <BalanceItem
              icon-name="ticket-bg"
            >
              {{ formatNumberToShortString(clanInfo.rating) }}
            </BalanceItem>
            <BalanceItem
              icon-name="ref-bg"
            >
              {{ formatNumberToShortString(clanInfo.membersCount) }}
            </BalanceItem>
          </div>
        </div>
      </div>
      <div class="flex gap-x-2">
        <!-- <VButton
          class="flex-1"
          text="Request"
          type="success"
          size="small"
        /> -->
        <VButton
          v-if="clanInfo?.clanLink"
          class="basis-1/2"
          :text="t('actions.openChat')"
          type="accent"
          size="small"
          @click="() => clanInfo?.clanLink && openTelegramLink(clanInfo.clanLink)"
        />
      </div>
    </div>
    <div class="flex-0 basis-[85px] flex items-end justify-end bg-[#C0C0C0] rounded-[11px] px-2 py-[10px]">
      <VButton
        v-if="!isClanEventActive && clanInfo.isEventPossible && !clanInfo.eventEndsAtEpochSec"
        type="success"
        size="small"
        :text="t('actions.startEvent')"
        @click="startEvent"
      />
      <CountdownTimerManual
        v-if="isClanEventActive"
        class="bg-[#07070773] rounded-[5px] px-[10px] py-[5px] text-[14px]"
        :days="days"
        :hours="hours"
        :minutes="minutes"
        :seconds="seconds"
      />
    </div>
    <div class="flex-1 overflow-y-auto">
      <div class="clans-view__shadow-gradient"></div>
      <LeaderboardList
        :leaderboard="mappedUsersList"
        name-class="text-white text-shadow text-shadow_black"
      />
      <div class="clans-view__shadow-gradient"></div>
    </div>
  </div>
  <ClanRewardScreen
    :is-open="isOpenClanRewardScreen"
    :clan-name="clanInfo?.clanName ?? ''"
    @close="isOpenClanRewardScreen = false"
  />
</template>

<style lang="scss" scoped></style>
