<script setup lang="ts">
import TextField from '@/components/UI/TextField.vue';
import VButton from '@/components/UI/VButton.vue';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n'
import { useClansList } from '@/services/client/useClans'
import LeaderboardItem from '@/components/events/LeaderboardItem.vue';
import clanImage from '@/assets/images/temp/clan-badge.png'
import { openTelegramLink } from '@telegram-apps/sdk';
import LoaderText from '@/components/LoaderText.vue';

withDefaults(defineProps<{
  userClan?: number | null
}>(), {
  userClan: undefined
})
const emit = defineEmits(['open-clan'])

const { t } = useI18n()
const searchText = ref('')
const { clans, isLoading } = useClansList()

const addBotToGroupLink = 'https://t.me/UniJumpieBot?startgroup=c'
</script>

<template>
  <div class="view-container menu-item clans-view flex flex-col">
    <h1 v-if="!userClan" class="text-[30px] leading-[40px] text-shadow text-center">
      {{ t('clans.clans') }}
    </h1>
    <div v-if="!userClan" class="flex items-center gap-2">
      <TextField
        class="flex-1"
        v-model="searchText"
        :placeholder="t('search')"
        size="small"
      />
      <VButton
        class="basis-[95px]"
        type="success"
        :text="t('actions.create')"
        size="small"
        @click="() => openTelegramLink(addBotToGroupLink)"
      />
    </div>
    <div class="flex-1 space-y-[6px] overflow-y-auto">
      <LoaderText
        v-if="isLoading"
        class="w-full h-full flex items-center justify-center text-[24px] text-[#6DB0ED]"
        is-loading
      />
      <template v-else>
        <div class="clans-view__shadow-gradient"></div>
        <LeaderboardItem
          v-for="(clan, index) in clans"
          :class="userClan === clan.id ? '!top-[10px] !bottom-[5px]' : ''"
          :key="clan.id"
          :username="clan.name"
          :score="clan.membersCount.toString()"
          :rank-index="index"
          :hide-rank="!userClan"
          :active="userClan === clan.id"
          :balance="clan.rating"
          :avatar="clanImage"
          balance-type="tickets"
          name-class="text-shadow text-shadow_black text-shadow_thin text-white"
          score-class="text-[#BD764A] font-extrabold"
          @click="() => emit('open-clan', clan.id)"
        />
        <div class="clans-view__shadow-gradient"></div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
