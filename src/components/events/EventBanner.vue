<script setup lang="ts">
import type { InstructionType } from '@/constants/instructions'
import { formatNumberToShortString } from '@/utils/number'
import LoaderText from '../LoaderText.vue'
import InstructionButton from '../UI/InstructionButton.vue'
import VButton, { type ButtonProps } from '../UI/VButton.vue'
import VOverlay from '../VOverlay.vue'
import type { EventRewardCurrency } from '@/services/openapi'

import { REWARD_TO_IMAGE_CLASS } from '@/composables/useIconImage'

const REWARD_TO_IMAGE_CLASS_STYLE: Record<EventRewardCurrency, string> = {
  tickets: '!w-[34px] !h-[34px]',
  hard: '!w-[34px] !h-[34px]',
  ton: '!w-[27px] !h-[27px]',
  soft: '!w-[34px] !h-[34px]',
  unlimitedLives: '!w-[34px] !h-[34px]',
  magicHorns: '!w-[34px] !h-[34px]',
  dynamicCoins: '!w-[34px] !h-[34px]',
  wheelSpins: '!w-[34px] !h-[34px]'
}

const { isOpen, reward, rewardType, buttons, image, banner, instructionType } = defineProps<{
  isOpen: boolean
  reward?: number
  rewardType?: EventRewardCurrency
  buttons?: Array<ButtonProps & { fontSize?: number }>
  image: string
  imageFullSize?: boolean
  banner: string
  isLoading?: boolean
  showCloseButton?: boolean
  instructionType?: InstructionType
}>()
const emit = defineEmits(['close', 'close-button'])

const close = () => {
  emit('close')
}

const closeWithButton = () => {
  emit('close')
  emit('close-button')
}
</script>

<template>
  <VOverlay
    :isOpen="isOpen"
    class="flex items-center justify-center px-[11px]"
    @click-self="close"
  >
    <div class="event-banner">
      <img class="event-banner__banner" :src="banner" alt="event banner" />
      <img
        :src="image"
        class="event-banner__image"
        :class="{ 'event-banner__image_full': imageFullSize }"
        alt="event banner image"
      />
      <InstructionButton
        v-if="instructionType && isOpen"
        class="absolute top-[5px] left-[5px]"
        :instruction-type="instructionType"
        :instructionCheck="true"
      />
      <div v-if="showCloseButton" class="close-button absolute top-[7px] right-[7px]" @click="closeWithButton"></div>
      <div
        class="w-full pt-2 pb-[20px] px-3"
        :class="{
          relative: !imageFullSize,
          'absolute bottom-0 left-0': imageFullSize,
          'pt-4': !!$slots.details
        }"
      >
        <div class="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <slot name="details"></slot>
        </div>
        <p class="event-banner__description">
          <slot></slot>
        </p>
        <div v-if="rewardType && reward" class="flex items-center justify-center gap-x-2 my-2">
          <div class="icon-bg relative" :class="[REWARD_TO_IMAGE_CLASS[rewardType], REWARD_TO_IMAGE_CLASS_STYLE[rewardType]]"></div>
          <p class="text-[24px] leading-[33px] text-shadow">
            {{ formatNumberToShortString(reward) }}
          </p>
        </div>
        <div v-if="buttons" class="flex gap-x-3 w-full">
          <VButton
            v-for="(button, index) in buttons"
            :key="index"
            class="flex-1"
            :text="button.text"
            :type="button.type"
            :image="button.image"
            :style="{ fontSize: button.fontSize + 'px' }"
            :disabled="button.disabled"
            @click="button.onClick ? button.onClick() : close()"
          >
            <template #content>
              <slot name="button-content"></slot>
            </template>
          </VButton>
        </div>
      </div>
    </div>
    <LoaderText
      class="event-banner__loading"
      :class="{
        'event-banner__loading_active': isLoading
      }"
      :is-loading="!!isLoading"
    />
  </VOverlay>
</template>
