<script setup lang="ts">
import { formatNumberToShortString } from '@/utils/number'
import { computed, onMounted, ref, watch } from 'vue'

import { useEventWelcomeCheck } from '@/composables/useEventWelcomeCheck'
import { usePlayerReceivedEventReward } from '@/services/client/usePlayerFlag'
import { usePlayerState } from '@/services/client/usePlayerState'
import { sendAnalyticsEvent } from '@/utils/analytics'

import eventBanner from '@/assets/images/temp/hotrecord/banner.png'
import eventImage from '@/assets/images/temp/hotrecord/image.png'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { HOT_RECORD_INSTRUCTION } from '@/constants/instructions.ts'
import leaguesService from '@/services/local/leagues'
import { useI18n } from 'vue-i18n'
import EventBanner from './EventBanner.vue'

const emit = defineEmits(['go-to-event'])
const { t } = useI18n()

const HAS_SEEN_EVENT_BANNER_KEY = 'hasSeenHotrecordEventBanner'

const {
  isOpen: isOpenRewardBanner,
  openWindowInQueue: openRewardWindowInQueue,
  closeWindowInQueue: closeRewardWindowInQueue
} = useWindowQueue('hotrecord-reward-window')
const { isOpen: isOpenWelcomeBannerInQueue, openWindowInQueue: openWelcomeWindowInQueue } =
  useWindowQueue('hotrecord-welcome-window')
const isOpenWelcomeBanner = ref(false)

const { playerState } = usePlayerState()
const { checkWelcomeBanner } = useEventWelcomeCheck()

const rewardType = computed(() => {
  return (playerState.value!.hotrecordEvent?.rewardCurrency ||
    playerState.value!.hotrecordEventReward?.reward?.currency) ??
    'hard'
})

const rewardAmount = computed(() => {
  const reward = playerState.value!.hotrecordEventReward?.reward?.amount ?? 0
  return getCurrencyRealAmount(reward, rewardType.value)
})

const rewardMaxAmount = computed(() => {
  const reward = playerState.value!.hotrecordEvent?.rewardMaxAmount ?? 0
  return getCurrencyRealAmount(reward, rewardType.value)
})

const openWelcomeBanner = () => {
  isOpenWelcomeBanner.value = true
  openWelcomeWindowInQueue()
}

const closeWelcomeBanner = () => {
  // we close welcome banner in queue after closing leaderboard window
  isOpenWelcomeBanner.value = false
  emit('go-to-event')
}

const { onPlayerRecievedHotrecordReward } = usePlayerReceivedEventReward()
const closeRewardBanner = () => {
  closeRewardWindowInQueue()
  const highestScore = playerState.value!.hotrecordEventReward?.highestScore ?? 0

  sendAnalyticsEvent('event_end', {
    event: 'hotrecord',
    total_points: highestScore,
    reward_type: rewardType.value,
    reward_amount: rewardAmount.value
  })
  onPlayerRecievedHotrecordReward()
}
watch(
  () => playerState.value?.hotrecordEventReward?.reward?.amount,
  newReward => {
    const reward = newReward ?? 0
    if (reward > 0) {
      openRewardWindowInQueue()
    }
  },
  { immediate: true }
)

onMounted(async () => {
  const isOpenWelcomeBanner = await checkWelcomeBanner(
    HAS_SEEN_EVENT_BANNER_KEY,
    playerState.value!.hotrecordEvent !== undefined,
    leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'hotRecordEvent')
  )
  if (isOpenWelcomeBanner) {
    openWelcomeBanner()
  }
})
</script>

<template>
  <div>
    <!-- Reward banner -->
    <EventBanner
      class="hotrecord-banner"
      :isOpen="isOpenRewardBanner"
      :reward="rewardAmount"
      :buttons="[{ text: t('actions.got'), type: 'accent' }]"
      :banner="eventBanner"
      :image="eventImage"
      :instruction-type="HOT_RECORD_INSTRUCTION"
      :reward-type="rewardType"
      @close="closeRewardBanner"
    >
      <i18n-t
        class="text-[12px] leading-[16px] text-white text-center"
        tag="p"
        keypath="hotrecord.eventEndDescription"
      >
        <template v-slot:highScore>
          <span class="text-[#FFE134]">
            {{ formatNumberToShortString(playerState!.hotrecordEventReward?.highestScore ?? 0) }}
          </span>
        </template>
      </i18n-t>
    </EventBanner>

    <!-- Welcome banner -->
    <EventBanner
      class="hotrecord-banner"
      :isOpen="isOpenWelcomeBanner && isOpenWelcomeBannerInQueue"
      :reward="rewardMaxAmount"
      :buttons="[{ text: 'Let\'s go!', type: 'success' }]"
      :banner="eventBanner"
      :image="eventImage"
      :instruction-type="HOT_RECORD_INSTRUCTION"
      :reward-type="rewardType"
      @close="closeWelcomeBanner"
    >
      <p class="text-[12px] leading-[16px] text-white text-center">
        {{ t('hotrecord.description', { rewardType: t(`reward.${rewardType}`).toUpperCase() }) }}
      </p>
    </EventBanner>
  </div>
</template>

<style lang="scss">
.hotrecord-banner {
  top: 0;
  --event-background: linear-gradient(360deg, #bd0c0c 0%, #e54739 92.65%);
}
</style>
