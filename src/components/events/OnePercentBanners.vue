<script setup lang="ts">
import { useReferralLink } from '@/composables/useReferralLink.ts'
import { formatNumberToShortString } from '@/utils/number'
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

import { useEventWelcomeCheck } from '@/composables/useEventWelcomeCheck'
import { usePlayerReceivedEventReward } from '@/services/client/usePlayerFlag'
import { usePlayerState } from '@/services/client/usePlayerState'
import { sendAnalyticsEvent } from '@/utils/analytics'

import eventBanner from '@/assets/images/temp/onepercent/banner.png'
import eventImage from '@/assets/images/temp/onepercent/image.png'
import CountdownTimer from '@/components/UI/CountdownTimer.vue'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { ONE_PERCENT_INSTRUCTION } from '@/constants/instructions.ts'
import leaguesService from '@/services/local/leagues'
import { useI18n } from 'vue-i18n'
import EventBanner from './EventBanner.vue'

const HAS_SEEN_EVENT_BANNER_KEY = 'hasSeenOnepercentEventBanner'
const MIN_BLOCKED_EVENT_REWARD = 300

const {
  isOpen: isOpenRewardBanner,
  openWindowInQueue: openRewardWindowInQueue,
  closeWindowInQueue: closeRewardWindowInQueue
} = useWindowQueue('onepercent-reward-window')
const { isOpen: isOpenWelcomeBannerInQueue, openWindowInQueue: openWelcomeWindowInQueue } =
  useWindowQueue('onepercent-welcome-window')
const isOpenWelcomeBanner = ref(false)

const { t } = useI18n()

const { isOpen: isOpenBlockedBanner, onepercentTimeLeftInSeconds } = defineProps<{
  isOpen: boolean
  onepercentTimeLeftInSeconds: number
}>()

const emit = defineEmits(['close', 'go-to-event'])

const router = useRouter()
const { forwardRefLink } = useReferralLink()
const { playerState } = usePlayerState()
const { checkWelcomeBanner } = useEventWelcomeCheck()

const targetTotalScore = computed(() => {
  return playerState!.value!.onepercentEvent?.targetTotalScore ?? 0
})

const hasMetRequirement = computed(() => {
  return playerState!.value!.onepercentEvent?.hasMetRequirement ?? false
})

const rewardType = computed(() => {
  return (playerState.value!.onepercentEvent?.rewardCurrency ||
    playerState.value!.onepercentEventReward?.reward?.currency) ??
    'hard'
})

const rewardAmount = computed(() => {
  const reward = playerState.value!.onepercentEventReward?.reward?.amount ?? 0
  return getCurrencyRealAmount(reward, rewardType.value)
})

const rewardMaxAmount = computed(() => {
  const reward = playerState.value!.onepercentEvent?.rewardMaxAmount ?? 0
  return getCurrencyRealAmount(reward, rewardType.value)
})

const openWelcomeBanner = () => {
  isOpenWelcomeBanner.value = true
  openWelcomeWindowInQueue()
}

const closeWelcomeBanner = () => {
  // we close welcome banner in queue after closing leaderboard window
  isOpenWelcomeBanner.value = false
  if (hasMetRequirement.value) {
    emit('go-to-event')
  }
}

const { onPlayerRecievedOnepercentReward } = usePlayerReceivedEventReward()
const closeRewardBanner = () => {
  closeRewardWindowInQueue()
  const totalScore = playerState.value!.onepercentEventReward?.totalScore ?? 0

  sendAnalyticsEvent('event_end', {
    event: 'onepercent',
    total_points: totalScore,
    reward_type: rewardType.value,
    reward_amount: rewardAmount.value
  })
  onPlayerRecievedOnepercentReward()
}

const closeBlockedBanner = () => {
  if (isOpenBlockedBanner) {
    emit('close')
  } else {
    closeWelcomeBanner()
  }
}

watch(
  () => playerState.value?.onepercentEventReward?.reward?.amount,
  newReward => {
    if (newReward) {
      openRewardWindowInQueue()
    }
  },
  { immediate: true }
)

onMounted(async () => {
  const isOpenWelcomeBanner = await checkWelcomeBanner(
    HAS_SEEN_EVENT_BANNER_KEY,
    playerState.value!.onepercentEvent !== undefined,
    leaguesService.hasAccess(playerState.value!.leagueLevel ?? 1, 'onePercentEvent')
  )
  if (isOpenWelcomeBanner) {
    openWelcomeBanner()
  }
})
</script>

<template>
  <div>
    <!-- Reward banner -->
    <EventBanner
      class="onepercent-banner"
      :isOpen="isOpenRewardBanner"
      :reward="rewardAmount"
      :buttons="[{ text: t('actions.got'), type: 'accent' }]"
      :banner="eventBanner"
      :image="eventImage"
      :instruction-type="ONE_PERCENT_INSTRUCTION"
      :reward-type="rewardType"
      @close="closeRewardBanner"
    >
      <i18n-t
        class="text-[12px] leading-[16px] text-white text-center"
        tag="p"
        keypath="onepercent.eventEndDescription"
      >
        <template v-slot:targetScore>
          <span class="text-[#FFE134]">
            {{ formatNumberToShortString(playerState!.onepercentEventReward?.totalScore ?? 0) }}
          </span>
        </template>
      </i18n-t>
    </EventBanner>

    <!-- Welcome banner -->
    <EventBanner
      class="onepercent-banner"
      :isOpen="isOpenWelcomeBanner && isOpenWelcomeBannerInQueue && hasMetRequirement"
      :reward="rewardMaxAmount"
      :buttons="[{ text: 'Let\'s go!', type: 'success' }]"
      :banner="eventBanner"
      :image="eventImage"
      :instruction-type="ONE_PERCENT_INSTRUCTION"
      :reward-type="rewardType"
      @close="closeWelcomeBanner"
    >
      <i18n-t
        class="text-[12px] leading-[16px] text-white text-center"
        tag="p"
        keypath="onepercent.eventStartDescription"
      >
        <template v-slot:targetScore>
          <span class="text-[#FFE134]">
            {{ formatNumberToShortString(targetTotalScore) }}
          </span>
        </template>
      </i18n-t>
    </EventBanner>

    <!-- Blocked banner -->
    <EventBanner
      class="onepercent-banner"
      :isOpen="isOpenBlockedBanner || (isOpenWelcomeBanner && !hasMetRequirement)"
      :reward="MIN_BLOCKED_EVENT_REWARD"
      :buttons="[
        { text: t('actions.inviteFriend'), type: 'success', fontSize: 20, onClick: forwardRefLink },
        {
          text: t('actions.goToShop'),
          type: 'accent',
          fontSize: 22,
          onClick: () => {
            closeBlockedBanner()
            router.push({ name: 'shop' })
          }
        }
      ]"
      :banner="eventBanner"
      :image="eventImage"
      :instruction-type="ONE_PERCENT_INSTRUCTION"
      :reward-type="rewardType"
      @close="closeBlockedBanner"
    >
      <div v-if="onepercentTimeLeftInSeconds > 0" class="timer-wrapper">
        <CountdownTimer
          timer-id="onepercentEventBanner"
          class="text-[12px] leading-[17px] text-[#FFFFFF]"
          :total-seconds="onepercentTimeLeftInSeconds"
        />
      </div>
      <p class="text-[12px] leading-[16px] text-white text-wrap text-center pt-3">
        {{ t('onepercent.eventBlockedDescription') }}
      </p>
    </EventBanner>
  </div>
</template>

<style lang="scss">
.onepercent-banner {
  top: 0;
  --event-background: linear-gradient(360deg, #9a4fff 0%, #be67e4 92.65%);

  .timer-wrapper {
    position: relative;
    display: flex;
    justify-content: center;

    .countdown-timer {
      position: absolute;
      top: -25px;
      padding: 5px 10px;
      border-radius: 5px;
      background: #07070773;
    }
  }
}
</style>
