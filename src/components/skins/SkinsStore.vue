<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'

import checkIconSimple from '@/assets/images/temp/check-icon-simple.svg'
import checkIcon from '@/assets/images/temp/check-icon.png'
import lockImage from '@/assets/images/temp/lock.svg'
import {
  DEFAULT_SKIN_ID,
  SKIN_ID_TO_IMAGE,
  SKIN_ID_TO_ORDER,
  SKIN_ID_TO_QUALITY,
  type SkinQuality,
  TON_SKIN_ID
} from '@/constants/skins'

import VButton from '../UI/VButton.vue'
import SkinItem from './SkinItem.vue'

import { useIsWalletConnected } from '@/composables/useWallet.ts'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { CURRENCY_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import { useClaimSkin } from '@/services/client/useClaimSkin.ts'
import { usePlayerState } from '@/services/client/usePlayerState'
import { usePurchaseSkin, useSelectSkin, useSkinsList } from '@/services/client/useSkins'
import type { Price, Skin } from '@/services/openapi'
import { hapticsService } from '@/shared/haptics/hapticsService.ts'
import { cloudStorageService } from '@/shared/storage/cloudStorageService.ts'
import { useHasUserSeenSkins } from '@/stores/hasUserSeenStore'
import { useSkinReward } from '@/stores/rewardStore.ts'
import { useToast } from '@/stores/toastStore'
import type { ShopScrollTarget } from '@/types'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { useRoute, useRouter } from 'vue-router'
import MultiplierWithPlus from '../UI/MultiplierWithPlus.vue'
import NewBadge from '../UI/NewBadge.vue'
import TonPurchaseButton from '@/components/TonPurchaseButton.vue'
import { usePurchase } from '@/composables/usePurchase'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const { showToast } = useToast()

const IS_TON_SKIN_PURCHASE_IN_PROGRESS = 'isTonSkinPurchaseInProgressNew'

type SkinListItem = Skin & { image: string; quality: SkinQuality }

const skinStore = useSkinReward()
const { playerState } = usePlayerState()
const { isWalletConnectedEverywhere } = useIsWalletConnected()
const { claimSkin } = useClaimSkin()
const { isLoading: isLoadingSkins, skinsList, isTonSkinReadyToClaim } = useSkinsList()
const {
  purchaseSkin: purchaseSkinMutation,
  onSkinPurchased,
  isPending: isPurchasing
} = usePurchaseSkin()
const { purchaseInvoice } = usePurchase()
const { selectSkin } = useSelectSkin()

const purchaseSkin = (skinId: number, multiplier: number, price?: Price) => {
  const currentMultiplier = playerMultiplier.value
  purchaseSkinMutation(skinId, multiplier, price).then(() => {
    skinStore.showReward([
      {
        skinId: skinId,
        multiplier: currentMultiplier,
        plusMultiplier: multiplier
      }
    ])
  })
}
const purchaseSkinWithExternalCurrency = (
  itemId: number,
  skinId: number,
  price: Price,
  multiplier: number
) => {
  purchaseInvoice('skin', itemId, skinId, price, multiplier)
    .then(currency => {
      if (currency === 'ton') {
        cloudStorageService.save(IS_TON_SKIN_PURCHASE_IN_PROGRESS, true)
        isTonSkinPurchaseInProgress.value = true
      }
    })
}

const onTonSkinClaim = (skin: SkinListItem) => {
  if (route.path === '/game') return

  claimSkin(skin.id).then(result => {
    if (result) {
      skinStore.showReward([
        {
          skinId: skin.id,
          multiplier: playerMultiplier.value,
          plusMultiplier: skin.multiplier
        }
      ])

      onSkinPurchased(skin.id, skin.multiplier)
      cloudStorageService.delete(IS_TON_SKIN_PURCHASE_IN_PROGRESS)
      isTonSkinPurchaseInProgress.value = false
    } else {
      showToast('Something went wrong', 'warning')
    }
  })
}

const isTonSkinPurchaseInProgress = ref(false)

const checkTonSkinProgress = () => {
  cloudStorageService.load<boolean>(IS_TON_SKIN_PURCHASE_IN_PROGRESS).then(inProgress => {
    isTonSkinPurchaseInProgress.value = !!inProgress
  })
}

const playerMultiplier = computed(() => playerState.value!.multiplier ?? 1)
const playerSkinId = computed(() => playerState.value!.skin ?? DEFAULT_SKIN_ID)
const playerBalance = computed(() => ({
  soft: playerState.value!.soft ?? 0,
  hard: playerState.value!.hard ?? 0
}))
const skinFromRouteQuery = route.query.skin && +route.query.skin
const previewedSkinRef = ref(skinFromRouteQuery || playerState.value!.skin!)

const defaultPlayerSkin: SkinListItem = {
  id: DEFAULT_SKIN_ID,
  locked: false,
  multiplier: 0,
  purchased: true,
  requiresRefs: 0,
  requiresTransaction: false,
  requiresWallet: false,
  requiresDailyReward: false,
  image: SKIN_ID_TO_IMAGE[DEFAULT_SKIN_ID],
  quality: 'common'
}

const finalSkinsList = computed<SkinListItem[]>(() => {
  const array: SkinListItem[] = Array.from(skinsList.value, skin => {
    return {
      ...skin,
      image: SKIN_ID_TO_IMAGE[skin.id],
      quality: SKIN_ID_TO_QUALITY[skin.id]
    }
  }).filter(skin => !!SKIN_ID_TO_IMAGE[skin.id])

  array.push(defaultPlayerSkin)

  return array.sort((a, b) => {
    return SKIN_ID_TO_ORDER.indexOf(a.id) - SKIN_ID_TO_ORDER.indexOf(b.id)
  })
})

const purchasedSkinsAmount = computed(
  () => finalSkinsList.value.filter(skin => skin.purchased).length
)
const previewedSkin = computed<SkinListItem>(() => {
  if (isLoadingSkins.value) {
    return {
      ...defaultPlayerSkin,
      id: playerSkinId.value
    }
  }
  return finalSkinsList.value.find(skin => skin.id === previewedSkinRef.value) || defaultPlayerSkin
})
const isPreviewedSkinSelected = computed(() => playerSkinId.value === previewedSkin.value.id)

const requirenment = computed(() => {
  if (previewedSkin.value.requiresRefs) {
    return t('skins.requirenments.inviteFriend', {
      amount: previewedSkin.value.requiresRefs - (playerState.value!.refs ?? 0)
    })
  }
  if (previewedSkin.value.requiresTransaction) {
    return t('skins.requirenments.transaction')
  }
  if (previewedSkin.value.requiresWallet) {
    return t('skins.requirenments.wallet')
  }
  if (previewedSkin.value.requiresBox) {
    return t('skins.requirenments.box', {
      boxType: t(`lootboxes.${previewedSkin.value.requiresBox}`)
    })
  }
  if (previewedSkin.value.requiresDailyReward) {
    return t('skins.requirenments.dailyReward', {
      days: 28 - (playerState.value!.dailyRewards?.currentDay || 1)
    })
  }
  return ''
})

const price = computed(() => {
  if (previewedSkin.value.price?.currency === 'hard') {
    return {
      image: CURRENCY_TO_IMAGE_CLASS.hard,
      text: previewedSkin.value.price.amount
    }
  } else if (previewedSkin.value.price?.currency === 'soft') {
    return {
      image: CURRENCY_TO_IMAGE_CLASS.soft,
      text: previewedSkin.value.price.amount
    }
  } else if (previewedSkin.value.price?.currency === 'stars') {
    return {
      image: CURRENCY_TO_IMAGE_CLASS.stars,
      text: previewedSkin.value.price.amount
    }
  } else if (previewedSkin.value.price?.currency === 'ton') {
    return {
      image: CURRENCY_TO_IMAGE_CLASS.ton,
      text: getCurrencyRealAmount(previewedSkin.value.price.amount, 'ton')
    }
  } else {
    return {
      image: '',
      text: t('free')
    }
  }
})

const hasEnoghBalance = computed(() => {
  if (previewedSkin.value.price?.currency === 'hard') {
    return playerBalance.value.hard >= previewedSkin.value.price.amount
  } else if (previewedSkin.value.price?.currency === 'soft') {
    return playerBalance.value.soft >= previewedSkin.value.price.amount
  } else {
    return true
  }
})

const hasUserSeenStore = useHasUserSeenSkins()

const onSkinClick = (id: number) => {
  hapticsService.triggerImpactHapticEvent('light')
  previewSkin(id)
}

const previewSkin = (id: number) => {
  previewedSkinRef.value = id
  if (hasUserSeenStore.newSkins.includes(id)) {
    hasUserSeenStore.markSkinAsSeen(id)
  }
}

const goToShop = (target: ShopScrollTarget) => {
  sendAnalyticsEvent('go_to_shop', { from: 'skin-store' })
  router.push({ name: 'shop', query: { scrollTo: target } })
}

const tryGoToShop = (target?: string) => {
  if (target === 'hard' || target === 'soft') {
    goToShop(target)
  }
}

onMounted(() => {
  checkTonSkinProgress()
  if (!isTonSkinReadyToClaim.value) return

  const tonSkin = finalSkinsList.value.find(skin => skin.id === TON_SKIN_ID)
  if (!tonSkin) return

  onTonSkinClaim(tonSkin)
})
</script>

<template>
  <div class="skin-store flex flex-col">
    <header class="shrink-0 text-center space-y-[2px] pb-[42px] tracking-normal">
      <p
        class="font-default font-extrabold text-[15px] leading-[22px] text-[#6DB0ED] max-[398px]:max-w-[340px]"
      >
        {{ t('skins.description') }}
      </p>
    </header>
    <div class="shrink-0 basis-[200px] skin-store__upper-block skin-preview">
      <MultiplierWithPlus
        class="absolute -top-[15px] left-1/2 -translate-x-1/2"
        :multiplier="playerMultiplier"
        :plus="previewedSkin.purchased ? '' : previewedSkin.multiplier"
      />
      <div class="skin-preview__image">
        <SkinItem
          class="w-full"
          animated
          :src="previewedSkin.image"
          :locked="previewedSkin.locked"
        />
      </div>
      <div class="skin-preview__description">
        <div class="space-y-2">
          <p class="text-[26px] leading-[35px] text-shadow">
            {{ t(`skins.list.${previewedSkin.id}.title`) }}
          </p>

          <p class="text-[#1D3161] text-[12px] leading-[16px]">
            {{ t(`skins.list.${previewedSkin.id}.description`) }}
          </p>
        </div>

        <div class="skin-preview__button">
          <p v-if="previewedSkin.locked" class="text-[13px] text-[#6DB0ED] font-extrabold z-10">
            {{ requirenment }}
            <img :src="lockImage" class="w-[20px] absolute -top-[9px] -right-[8px]" alt="lock" />
          </p>
          <p
            v-else-if="isPreviewedSkinSelected"
            class="text-[20px] text-[#6DB0ED] font-extrabold z-10"
          >
            {{ t('selected') }}
          </p>
          <VButton
            class="!w-full !text-[18px]"
            v-else-if="previewedSkin.requiresBox && !previewedSkin.purchased"
            type="success"
            size="medium"
            :text="requirenment"
            @click="() => goToShop('lootbox')"
          />
          <TonPurchaseButton
            v-else-if="!previewedSkin.purchased && previewedSkin.price?.currency === 'ton'"
            class="!w-full"
            :class="{ '!text-[18px]': !isWalletConnectedEverywhere }"
            type="success"
            size="medium"
            :text="price.text"
            :image-class="price.image"
            :isTransactionInProgress="isTonSkinPurchaseInProgress"
            @purchasse="() =>
              purchaseSkinWithExternalCurrency(
                +previewedSkin.shopItem!,
                previewedSkin.id,
                previewedSkin.price!,
                previewedSkin.multiplier
              )
            "
          />
          <VButton
            class="!w-full"
            v-else-if="!previewedSkin.purchased"
            type="success"
            size="medium"
            :text="price.text"
            :image-class="price.image"
            :disabled="isPurchasing"
            @click="
              () =>
                !hasEnoghBalance
                  ? tryGoToShop(previewedSkin?.price?.currency)
                  : previewedSkin.shopItem
                    ? purchaseSkinWithExternalCurrency(
                        +previewedSkin.shopItem,
                        previewedSkin.id,
                        previewedSkin.price!,
                        previewedSkin.multiplier
                      )
                    : purchaseSkin(
                        previewedSkin.id,
                        previewedSkin.multiplier,
                        previewedSkin.price ?? undefined
                      )
            "
          />
          <VButton
            class="!w-full"
            v-else
            type="success"
            size="medium"
            :text="t('actions.select')"
            :disabled="isPreviewedSkinSelected"
            @click="selectSkin(previewedSkin.id)"
          />
        </div>
      </div>
    </div>
    <div class="skin-store__lower-block" v-if="!isLoadingSkins">
      <div class="skin-list">
        <p class="skin-list__amount">
          {{
            t('skins.yourSkins', {
              amount: purchasedSkinsAmount,
              total: finalSkinsList.length
            })
          }}
        </p>
        <div
          v-for="skin in finalSkinsList"
          :key="skin.id"
          class="skin-list__item-wrapper"
          :class="`skin-list__item-wrapper_style-${skin.quality}`"
          @click="
            () => {
              onSkinClick(skin.id)
            }
          "
        >
          <NewBadge
            v-if="hasUserSeenStore.newSkins.includes(skin.id)"
            class="-top-[5px] -left-[1px] z-20"
            size="large"
          />
          <div
            class="skin-list__item skin-list__item_style"
            :class="{
              'skin-list__item_active': skin.id === previewedSkin.id
            }"
          >
            <SkinItem class="w-full" :src="skin.image" />
            <img
              class="skin-list__item_purchased"
              v-if="skin.purchased"
              :src="checkIconSimple"
              alt="purchased check"
            />
            <img
              class="skin-list__item_selected"
              v-if="playerSkinId === skin.id"
              :src="checkIcon"
              alt="selected check"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.skin-store {
  height: inherit;
  width: inherit;
  overflow: visible;

  &__upper-block {
    width: 100%;
  }

  &__lower-block {
    position: relative;
    overflow-y: auto;
    overflow-x: visible;
    padding-bottom: 15px;
  }
}

.skin-preview {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: end;
  column-gap: 24px;
  padding: 27px 24px 20px 15px;
  background: #00eeff33;
  border-radius: 9px;

  &__image {
    flex: 0 0 113px;
  }

  &__description {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__button {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 42px;

    &::before {
      transform: skew(-10deg);
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      border-radius: 6px;
      width: 100%;
      height: 100%;
      background-color: #0826739c;
      box-shadow: inset #00000012 0 -12px;
    }
  }
}

.skin-list {
  overflow: visible;
  display: grid;
  grid-template-columns: 23% 23% 23% 23%;
  grid-auto-rows: min-content;
  row-gap: 12px;
  justify-content: space-between;
  padding-top: 9px;

  &__amount {
    grid-column: span 4;
    font-size: 16px;
    line-height: 22px;
    font-weight: 800;
  }

  &__item-wrapper {
    position: relative;

    &_style {
      &-common {
        --skin-border-color: #91ff70;
        --skin-background: linear-gradient(180deg, rgba(145, 255, 112, 0.32) 0%, #54de2a 100%);
        --decorative-color: #ccff7f;
        .skin {
          --shadow-color-1: #136200 !important;
          --shadow-color-2: rgba(26, 91, 0, 0) !important;
        }
      }

      &-rare {
        --skin-border-color: #7bd3ff;
        --skin-background: linear-gradient(180deg, rgba(14, 206, 246, 0.3) 0%, #00a6ff 100%);
        --decorative-color: #9ff9ff;
        .skin {
          --shadow-color-1: #251a74;
          --shadow-color-2: rgba(37, 26, 116, 0);
        }
      }

      &-epic {
        --skin-border-color: #ff7ef3;
        --skin-background: linear-gradient(180deg, rgba(255, 44, 227, 0.3) 0%, #ff2ce3 100%);
        --decorative-color: #ffb9e7;
        .skin {
          --shadow-color-1: #741a6a;
          --shadow-color-2: rgba(116, 26, 116, 0);
        }
      }
    }

    &::before {
      content: '';
      position: absolute;
      width: 36px;
      height: 4px;
      top: 0;
      right: 20%;
      background: linear-gradient(to right, var(--decorative-color) 60%, white 60%);
      z-index: 2;
    }

    &::after {
      content: '';
      position: absolute;
      width: 36px;
      height: 4px;
      bottom: 0;
      left: 20%;
      background: linear-gradient(to right, white 40%, var(--decorative-color) 40%);
      z-index: 2;
    }
  }

  &__item {
    position: relative;
    padding: 2px 0 10px 0;
    z-index: 1;

    background: var(--skin-background);
    border: 4px solid var(--skin-border-color);
    border-radius: 8px;

    .skin {
      position: relative;
      left: -2px;
    }

    &_active {
      outline: 3px solid white;

      &::before {
        content: '';
        position: absolute;
        top: -13px;
        left: 0;
        width: 100%;
        height: 20px;
        background: radial-gradient(50% 50% at 50% 50%, #ffffff 17.5%, rgba(255, 255, 255, 0) 100%);
        z-index: 1;
        opacity: 0.7;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -13px;
        left: 0;
        width: 100%;
        height: 20px;
        background: radial-gradient(50% 50% at 50% 50%, #ffffff 17.5%, rgba(255, 255, 255, 0) 100%);
        z-index: 1;
        opacity: 0.7;
      }
    }

    &_purchased {
      width: 15px;
      position: absolute;
      top: 2px;
      left: 2px;
    }

    &_selected {
      width: 28px;
      position: absolute;
      top: -8px;
      left: -4px;
    }
  }
}
</style>
