<script setup lang="ts">
import { ref, computed, onMounted, nextTick, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import ModalWindow from '@/components/UI/ModalWindow.vue'
import VButton from './UI/VButton.vue'
import { usePurchase } from '@/composables/usePurchase'
import type { Price, RewardType, StackableBoosterType, ShopItemPrice } from '@/services/openapi'
import { useShopItems } from '@/services/client/useShopItems'
import PurchaseCurrencyWindow from './shop/PurchaseCurrencyWindow.vue'
import { REWARD_TO_IMAGE_CLASS } from '@/composables/useIconImage'
import PurchaseItemWindow from '@/components/shop/PurchaseItemWindow.vue'

const { t } = useI18n()

const emit = defineEmits(['close', 'purchased'])
const props = defineProps<{
  boosterType: StackableBoosterType
}>()

const {
  purchaseItem: purchaseItemMutation,
  isPurchasingItem
} = usePurchase()
const { shopItems, isLoading, refetch: refetchShopItems } = useShopItems()

const purchaseStarsWindowRef = useTemplateRef('purchaseStarsWindowRef')
const purchaseItemWindowRef = useTemplateRef('purchaseItemWindowRef')
const shopItem = computed(() => shopItems.value!.stackableBoosters.find(item => item.type === props.boosterType)!)

const tryPurchaseItem = (type: RewardType, id: number, value: number, price: ShopItemPrice) => {
  if (price.prices.length > 1) {
    purchaseItemWindowRef.value?.openWindow(type, { id, value, price })
  } else {
    purchaseItem(type, id, value, price.prices[0] ?? price.displayPrice)
  }
}

const purchaseItem = (type: RewardType, itemId: number, value: number, price: Price) => {
  purchaseItemMutation(type, itemId, value, price)
    .then(() => emit('purchased', shopItem.value.type))
    .catch(reason => {
      if (reason.message === 'NOT_ENOUGH_FUNDS') {
        purchaseStarsWindowRef.value?.openWindow(shopItems.value?.hard ?? [], 'hard', reason.payload)
      }
    })
}

const isOpen = ref(false)

const closeWindow = () => {
  isOpen.value = false
  setTimeout(() => {
    emit('close')
  }, 200)
}

onMounted(() => {
  nextTick(() => {
    isOpen.value = true
  })
})
</script>

<template>
  <ModalWindow
    class="booster-selector-dialog"
    :title="t('boosters.' + boosterType + '.fullName')"
    :is-open="isOpen"
    :is-loading="isLoading || isPurchasingItem"
    @close="closeWindow"
  >
    <div class="bg-[#2397D529] rounded-[9px] pt-[20px] pb-[10px] mb-[9px]">
      <div class="relative icon-bg !w-[100px] !h-[100px] mx-auto mb-[20px]" :class="[REWARD_TO_IMAGE_CLASS[boosterType]]">
        <div class="absolute -bottom-[10px] -right-[20px] text-[40px] text-shadow text-shadow_black">
          x{{ shopItem.value }}
        </div>
      </div>
      <p class="text-[16px] text-[#1E4073] font-extrabold text-center max-w-[311px] mx-auto">
        {{ t('boosters.' + boosterType + '.description') }}
      </p>
    </div>
    <VButton
      class="mx-auto min-w-[244px]"
      type="success"
      :text="shopItem.price.displayPrice.amount"
      :image-class="'hard-coin-bg'"
      @click="() => tryPurchaseItem(shopItem.type, shopItem.id, shopItem.value, shopItem.price)"
    >
    </VButton>
  </ModalWindow>
  <PurchaseItemWindow
    :class="{ 'opacity-0': isPurchasingItem }"
    ref="purchaseItemWindowRef"
    @purchase="(
      type: RewardType,
      itemId: number,
      value: number,
      price: Price
    ) => purchaseItem(type, itemId, value, price)"
  />
  <PurchaseCurrencyWindow
    ref="purchaseStarsWindowRef"
    @purchased="() => {
      // we have to refetch offers because cheapest hard currency offer is limited
      // and should dissapear after meeting the condition on server
      refetchShopItems()
    }"
  />
</template>

<style lang="scss">
.booster-selector-dialog {
  padding: 11px;
  background: linear-gradient(360deg, #1EADEA 0%, #98E0FF 92.65%);
  
  .modal-window__title {
    font-size: 32px;
  }

  .booster-item {
    position: relative;
    width: 90px;
    height: 90px;

    background-color: #00EEFF66;
    border: 2px solid #26508280;
    border-radius: 9px;

    &_active {
      background-color: #6CE701;
      border-color: #02510080;
    }

    &__image {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -45%);
      width: 50px;
      height: 50px;
    }

    &__badge {
      position: absolute;
      right: -3px;
      bottom: -3px;
      width: 28px;
      height: 28px;
      z-index: 1;
      border-radius: 50%;
      font-size: 12px;

      display: flex;
      align-items: center;
      justify-content: center;

      background: linear-gradient(180deg, #FF9B30 0%, #FFD900 100%);
      border: 1px solid #9A4001;
      box-shadow: inset 0 -3px #F7781E;
      
      &_green {
        background: linear-gradient(180deg, #A7F856 0%, #04D400 100%);
        border-color: #02510080;
        box-shadow: inset 0 -3px #03AA00;
      }
    }

    &__timer {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 80%;
      height: 20px;

      display: flex;
      align-items: center;
      justify-content: center;

      text-align: center;
      background: #1E4073;
      color: #fff;
      font-size: 12px;
      border-radius: 4px;
    }
  }
}
</style>
