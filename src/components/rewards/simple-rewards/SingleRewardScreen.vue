<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import {
  type RewardScreenItemType,
  isNumeralReward,
  isTimeReward,
  isSimpleReward,
  type Reward
} from '@/types'
import { customTruncate, formatNumberWithSeparator } from '@/utils/number'
import BalanceItem from '@/components/UI/BalanceItem.vue';
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import { useRewardCounter } from '@/composables/useRewardCounter';
import { usePlayerState } from '@/services/client/usePlayerState';
import { useLifesStore } from '@/stores/livesStore.ts'
import {
  useMagneticFieldStore,
  useJumperStore,
  useAimBotStore
} from '@/stores/boostersStore';
import { useIconImage } from '@/composables/useIconImage';

const props = defineProps<{
  reward: Reward
}>()
const emit = defineEmits(['close'])

const { playerState } = usePlayerState()

const COLLECT_ANIMATION_DELAY = 1000;

const { getImageClass } = useIconImage()
const getRewardName = (type: RewardScreenItemType) => {
  if (type === 'dynamicCoins') {
    return playerState.value?.progressiveOffers?.find(offer => offer.usageDynamicCoins)?.id ===
      10000
      ? t(`reward.dynamicCoins_1`)
      : t(`reward.dynamicCoins_2`)
  }
  return t(`reward.${type}`)
}

const IMAGE_CLASS: Record<RewardScreenItemType, string> = {
  tickets: 'reward__icon_lg relative -top-[5px]',
  hard: 'reward__icon_md',
  soft: 'reward__icon_md',
  refsFake: 'reward__icon_lg',
  ton: 'reward__icon_md',
  dynamicCoins: 'reward__icon_md',
  stackableMagneticField: 'reward__icon_lg',
  stackableAimbot: 'reward__icon_lg',
  stackableJumper: 'reward__icon_lg',
  magicHorns: 'reward__icon_md',
  timeBoundMagneticField: 'reward__icon_lg',
  unlimitedLives: 'reward__icon_lg',
  fullLives: 'reward__icon_xl',
  timeBoundAimbot: 'reward__icon_lg',
  timeBoundJumper: 'reward__icon_lg',
  lives: 'reward__icon_md',
  customCoin: 'reward__icon_md',
  wheelSpins: 'reward__icon_md',
}

const { t } = useI18n();

const livesStore = useLifesStore()
const magneticFieldStore = useMagneticFieldStore()
const jumperStore = useJumperStore()
const aimBotStore = useAimBotStore()
const timerStore = computed(() => {
  if (props.reward.type === 'unlimitedLives') return livesStore
  if (props.reward.type === 'timeBoundMagneticField') return magneticFieldStore
  if (props.reward.type === 'timeBoundJumper') return jumperStore
  if (props.reward.type === 'timeBoundAimbot') return aimBotStore
  return null
})

const rewardHours = ref(0)
const rewardMinutes = ref(0)
const {
  targetValue,
  sourceValue,
  isCollecting,
  charge,
  setValues
} = useRewardCounter(() => {
  setTimeout(() => {
    emit('close')
  }, COLLECT_ANIMATION_DELAY)
})

const startAnimation = ref(false)

onMounted(() => {
  if (isNumeralReward(props.reward)) {
    setValues(props.reward.prevValue, props.reward.value)
  } else if (isTimeReward(props.reward)) {
    rewardHours.value = Math.floor(props.reward.duration / 3600)
    rewardMinutes.value = Math.floor((props.reward.duration % 3600) / 60)
  }
  setTimeout(() => startAnimation.value = true)
})

const collect = () => {
  if (isNumeralReward(props.reward)) {
    charge()
  } else {
    emit('close')
  }
}

defineExpose({
  collect
})
</script>

<template>
  <div class="text-center tracking-normal space-y-3">
    <p class="text-[32px] leading-[46x] text-shadow">
      {{ t('reward.youGot') }}
    </p>
    <p
      class="text-[40px] leading-[55x] text-[#FFE657] text-shadow"
    >
      {{ getRewardName(props.reward.type) }}:
    </p>
  </div>

  <div
    class="reward__item"
    :class="{
      'reward__item_shown': startAnimation
    }"
  >
    <div class="icon-bg" :class="[getImageClass(props.reward.type), IMAGE_CLASS[props.reward.type]]"></div>
    <div v-if="isSimpleReward(props.reward)" class="text-white text-[32px] absolute">Full</div>
    <p
      v-else
      class="text-[48px] text-shadow"
    >
      {{
        isNumeralReward(props.reward)
          ? '+' + formatNumberWithSeparator(customTruncate(sourceValue))
          : '+' + (rewardHours || rewardMinutes) + t(`${rewardHours ? 'hours' : 'minutes'}`)
      }}
    </p>
  </div>

  <div class="flex flex-col items-center gap-y-[70px]">
    <div
      v-show="!isSimpleReward(props.reward)"
      class="reward__previous-balance z-10"
      :class="{
        'reward__previous-balance_shown': startAnimation,
        'reward__previous-balance_active': !isCollecting
      }"
    >
      <BalanceItem
        class="z-10"
        image-class="!w-[50px] !h-[50px]"
        bar-class="!h-[32px] !pl-[33px]"
        balance-class="!text-[24px] text-shadow text-shadow_black text-shadow_thin"
        :iconName="getImageClass(props.reward.type)"
      >
        <template v-if="isNumeralReward(props.reward)">
          {{ formatNumberWithSeparator(customTruncate(targetValue))  }}
        </template>
        <template v-else-if="timerStore">
          <CountdownTimerManual
            :days="timerStore.days"
            :hours="timerStore.hours"
            :minutes="timerStore.minutes"
            :seconds="timerStore.seconds"
            digital
          />
        </template>
      </BalanceItem>
    </div>
    <p class="text-[24px] font-extrabold text-shadow text-shadow_black text-shadow_thin tracking-normal">
      {{ t('actions.tapToCollect') }}
    </p>
  </div>
</template>

<style lang="scss">
.reward {
  &__item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    column-gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;

    &_shown {
      animation: reward-bounce 0.5s ease-in-out;
      animation-delay: 0.1s;
      opacity: 1;
    }
  }

  &__icon {
    &_md {
      width: 60px !important;
      height: 60px !important;
    }
    &_lg {
      width: 70px !important;
      height: 70px !important;
    }
    &_xl {
      width: 100px !important;
      height: 100px !important;
    }
  }

  &__previous-balance {
    --animation-delay: 0;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;

    &_shown {
      opacity: 1;
    }

    &_active {
      animation: reward-bounce 0.5s ease-in-out;
    }
  }
}
</style>
