<script setup lang="ts">
import { usePlayerState } from '@/services/client/usePlayerState';
import { onMounted, useTemplateRef, watch } from 'vue'

import { useHasUserSeenLeagues } from '@/stores/hasUserSeenStore';
import LeagueRewardScreen from './LeagueRewardScreen.vue';

const { playerState } = usePlayerState()
const hasUserSeenLeaguesStore = useHasUserSeenLeagues()

const leagueRewardScreenRef = useTemplateRef('leagueRewardScreenRef')

const validateLeaguesToClaim = (leaguesToClaim: number[]) => {
  if (!leaguesToClaim.length) return
  hasUserSeenLeaguesStore.hasNewLeagueInfo = true
  leagueRewardScreenRef.value?.open(leaguesToClaim)
}

watch(() => playerState.value?.leaguesToClaim, (newVal, oldValue) => {
  // dont do anything if newVal is empty
  // or the state was mutated but the last league is the same that measn
  // leagues to claim are the same too
  if (!newVal?.length || newVal.at(-1) === oldValue?.at(-1)) return
  validateLeaguesToClaim(newVal)
})

// using { immediate: true } on Vue's watch will work but leagueRewardScreenRef.value will be null
onMounted(() => {
  validateLeaguesToClaim(playerState.value?.leaguesToClaim || [])
})
</script>

<template>
  <LeagueRewardScreen ref="leagueRewardScreenRef" />
</template>

<style lang="scss">
</style>
