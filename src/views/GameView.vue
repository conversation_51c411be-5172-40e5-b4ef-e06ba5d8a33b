<script setup lang="ts">
import { formatNumberToShortString, formatNumberWithSeparator } from '@/utils/number'
import { onBackButtonClick } from '@telegram-apps/sdk'
import { computed, onMounted, onUnmounted, ref } from 'vue'

import ConnectionDialog from '@/components/ConnectionDialog.vue'
import HeaderMenu from '@/components/HeaderMenu.vue'
import BalanceItem from '@/components/UI/BalanceItem.vue'
import VButton from '@/components/UI/VButton.vue'
import GameDebugPanel from '@/components/debug/GameDebugPanel.vue'
import PauseDialog from '@/components/game/PauseDialog.vue'
import PauseDialogOnBack from '@/components/game/PauseDialogOnBack.vue'
import ReviveWindowWrapper from '@/components/game/ReviveWindowWrapper.vue'
import PauseIcon from '@/components/icons/PauseIcon.vue'
import { useGameAdapter } from '@/composables/useGameAdapter'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { usePlayerState } from '@/services/client/usePlayerState'
import type { PlayerBalanceRewardType, Reward } from '@/types'
import { useRewardStore } from '@/stores/rewardStore.ts'
import { keepScreenOn } from '@/utils/keepScreenOn'
import { isFullscreen, isTMA } from '@telegram-apps/sdk'
import { useRoute, useRouter } from 'vue-router'
import GameOverWindowWrapper from '@/components/game/GameOverWindowWrapper.vue'
import { cloudStorageService } from '@/shared/storage/cloudStorageService'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { NOT_FIRST_VISIT_GAME_VIEW } from '@/shared/constants/storedPlayerData'
import type { StackableBoosterType } from '@/services/openapi'
import VOverlay from '@/components/VOverlay.vue'
import { REWARD_TO_IMAGE_CLASS } from '@/composables/useIconImage'

const rewardStore = useRewardStore()
const isFullscreenMode = isFullscreen()
const router = useRouter()
const { playerState } = usePlayerState()
const multiplier = computed(() => playerState.value?.multiplier ?? 1)

const route = useRoute()
const isFirstTutorial = !!route.query.tutorial
const activeBoostersFromRouteQuery = ((route.query.boosters as string).length
  ? (route.query.boosters as string).split(',') : []) as StackableBoosterType[]

const {
  sessionState,
  tickets,
  ton,
  customCoin,
  dynamicCoin,
  nextTon,
  nextCustomCoin,
  isSessionUpdated,
  isRevive,
  isGameOver,
  isTonLimitEnded,
  mobsKillCount,
  boostersRef,
  startGame,
  pauseGame,
  unpauseGame,
  continueGame,
  goToGameOver,
  restartGame,
  destroyGame
} = useGameAdapter({
  canRevive: !isFirstTutorial,
  onGameOver: () => {
    rewardCheck()
  }
})

// used only in gameover dialog so should not be reactive
let sessionCount = (0)

const startSession = async (startSessionFunc: Function) => {
  startSessionFunc()
  sessionCount++
  await cloudStorageService.save('game-session-count', sessionCount)
}

const goToMainMenu = () => {
  destroyGame(() => {
    router.push('/')
  })
}

const backButtonRemoveListener = ref<Function | null>(null)
const isPaused = ref(false)
const isPausedOnBackButton = ref(false)
const isPausedOnConnectionLost = ref(false)

const isGamePaused = computed(
  () =>
    isPaused.value ||
    isPausedOnBackButton.value ||
    isPausedOnConnectionLost.value
)

const backButtonClicked = () => {
  if (isRevive.value || isGameOver.value) {
    return
  } else {
    handlePauseOnBackButton()
  }
}

const handlePauseGame = () => {
  if (isRevive.value || isGamePaused.value || isGameOver.value) return
  isPaused.value = true
  pauseGame()
}

const handlePauseOnBackButton = () => {
  if (isPausedOnConnectionLost.value) return
  if (isPaused.value) {
    isPaused.value = false
  } else {
    pauseGame()
  }
  isPausedOnBackButton.value = true
}

const handlePauseOnConnectionLost = () => {
  if (isRevive.value) return
  isPausedOnBackButton.value = false
  isPaused.value = false
  isPausedOnConnectionLost.value = true
  pauseGame()
}

const handleUnpause = () => {
  isPaused.value = false
  isPausedOnBackButton.value = false
  isPausedOnConnectionLost.value = false
  unpauseGame()
}

const handleOnGameOver = () => {
  goToGameOver()
  handleUnpause()
}

const coinRewardCheck = (
  type: PlayerBalanceRewardType,
  collectedAmount: number,
  formatter: (v: number) => number = (v: number) => v
): Reward | null => {
  if (collectedAmount) {
    const reward: Reward = {
      type,
      value: formatter(collectedAmount),
      prevValue: formatter(playerState.value?.[type] ?? 0),
    }
    return reward
  }
  return null
}

const rewardCheck = () => {
  const rewards: Reward[] = []
  const tonReward = coinRewardCheck('ton', ton.value, (v: number) => getCurrencyRealAmount(v, 'ton'))
  if (tonReward) {
    rewards.push(tonReward)
  }
  const customCoinReward = coinRewardCheck('customCoin', +customCoin.value)
  if (customCoinReward) {
    rewards.push(customCoinReward)
  }
  const dynamicCoinReward = coinRewardCheck('dynamicCoins', +dynamicCoin.value)
  if (dynamicCoinReward) {
    rewards.push(dynamicCoinReward)
  }
  rewardStore.showReward(rewards)
}

const isPortrait = ref(window.matchMedia('(orientation: portrait)').matches)

const handleOrientationChange = (e: MediaQueryListEvent) => {
  isPortrait.value = e.matches
}

const handleVisibilityChange = () => {
  if (document.hidden) {
    handlePauseGame()
  } else {
    keepScreenOn()
  }
}

onMounted(async () => {
  sessionCount = await cloudStorageService.load<number>('game-session-count') ?? 0
  startSession(() => startGame(activeBoostersFromRouteQuery))

  const isNotFirstTimeVisit = await cloudStorageService.load(NOT_FIRST_VISIT_GAME_VIEW)
  if (!isNotFirstTimeVisit) {
    sendAnalyticsEvent('new_user', { step: 'game_view' })
    await cloudStorageService.save(NOT_FIRST_VISIT_GAME_VIEW, true)
  }

  keepScreenOn()
  window.matchMedia('(orientation: portrait)').addEventListener('change', handleOrientationChange)
  document.addEventListener('visibilitychange', handleVisibilityChange)
  if (await isTMA()) {
    backButtonRemoveListener.value = onBackButtonClick(backButtonClicked)
  }
})

onUnmounted(() => {
  window
    .matchMedia('(orientation: portrait)')
    .removeEventListener('change', handleOrientationChange)
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  if (backButtonRemoveListener.value !== null) {
    backButtonRemoveListener.value()
  }
})

const isDev = __DEV__
</script>

<template>
  <div
    class="game-view__stats pointer-events-none"
    :class="{ 'game-view__stats_fullscreen': isFullscreenMode }"
  >
    <p class="text-[28px] text-[#F5C34A] text-shadow">
      {{ formatNumberWithSeparator(sessionState.score) }}
    </p>
    <div class="flex items-center">
      <BalanceItem
        class="game-view__ticket z-10"
        image-class="game-view__ticket-image"
        bar-class="game-view__ticket-bar"
        balance-class="game-view__ticket-balance text-shadow text-shadow_black text-shadow_thin"
        iconName="ticket-bg"
      >
        {{ formatNumberToShortString(+tickets) }}
      </BalanceItem>
      <BalanceItem
        class="game-view__multiplier z-10"
        image-class="game-view__multiplier-image"
        balance-class="game-view__multiplier-balance"
        bar-class="game-view__multiplier-bar"
        iconName="multiplier-bg"
        gold
      >
        {{ formatNumberToShortString(multiplier) }}
      </BalanceItem>
    </div>
  </div>
  <HeaderMenu class="pointer-events-none">
    <template #left>
      <BalanceItem
        v-if="playerState?.tonOnPlatformEvent !== undefined && !isRevive && !isGameOver"
        class="game-view__ton z-10"
        image-class="game-view__ton-image"
        bar-class="game-view__ton-bar"
        balance-class="game-view__ton-balance text-shadow text-shadow_black text-shadow_thin"
        iconName="ton-bg"
      >
        {{ getCurrencyRealAmount(ton, 'ton') }}
      </BalanceItem>
    </template>

    <template #right>
      <VButton class="pointer-events-auto" size="small" type="accent" @click="handlePauseGame">
        <template #content>
          <PauseIcon />
        </template>
      </VButton>
    </template>
    <div
      v-if="!isRevive && !isGameOver"
      class="absolute right-4 -bottom-[28px] translate-y-full space-y-[10px]"
    >
      <div
        v-for="booster in boostersRef"
        :key="booster"
        class="flex items-center justify-center bg-[#5AD3F9B2] rounded-[9px] w-[49px] h-[49px]"
      >
        <div class="icon-bg !w-[32px] !h-[32px]" :class="[REWARD_TO_IMAGE_CLASS[booster]]"></div>
      </div>
    </div>
  </HeaderMenu>
  <VOverlay
    :isOpen="isRevive || isGameOver"
    class="overlay_gradient"
  >
    <ReviveWindowWrapper
      v-if="isRevive"
      :sessionId="sessionState.sessionId"
      :score="sessionState.score"
      :high-score="sessionState.highScore ?? 0"
      :high-score-event="sessionState.highScoreEvent ?? 0"
      :total-score-event="sessionState.totalScoreEvent ?? 0"
      :nextTon="nextTon"
      :nextCustomCoin="nextCustomCoin"
      :mobsKillCount="mobsKillCount"
      @revive="continueGame"
      @game-over="handleOnGameOver"
    >
    </ReviveWindowWrapper>
    <GameOverWindowWrapper
      v-if="isGameOver"
      :score="sessionState.score"
      :tickets="tickets"
      :isSessionUpdated="isSessionUpdated"
      :isTonLimitEnded="isTonLimitEnded"
      :sessionCount="sessionCount"
      @restart="(activeBoosters: StackableBoosterType[]) => startSession(() => restartGame(activeBoosters))"
      @go-to-main-menu="goToMainMenu"
    />
  </VOverlay>
  <PauseDialogOnBack
    :open="isPausedOnBackButton"
    @continue="handleUnpause"
    @end="isPausedOnBackButton = false, goToGameOver()"
  />
  <PauseDialog
    :open="isPaused && isPortrait"
    :score="sessionState.score"
    :high-score="sessionState.highScore ?? 0"
    @continue="handleUnpause"
  />
  <ConnectionDialog
    @connection-lost="handlePauseOnConnectionLost"
    @connection-restored="handleUnpause"
  />
  <GameDebugPanel
    v-if="isDev"
    @pause="pauseGame"
    @unpause="unpauseGame"
    @restart="() => startSession(() => restartGame(activeBoostersFromRouteQuery))"
  />
</template>

<style lang="scss">
.game-view {
  &__stats {
    position: absolute;
    top: calc(var(--safe-area-inset-top) + 10px);

    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    // used in combination with [&-score] height calculation
    row-gap: 12px;

    &_fullscreen {
      row-gap: calc(
        ((var(--header-height-fixed) - 21px) / 2) +
          (var(--content-safe-area-inset-top) - 28px - 10px)
      );
    }
  }

  &__ticket {
    &-image {
      width: 30px;
      height: 30px;
    }

    &-bar {
      height: 21px;
      padding: 0 14px 0 20px;
      justify-content: center;

      &:after {
        border-radius: 0;
      }
    }

    &-balance {
      font-size: 13px;
    }
  }

  &__ton {
    min-width: 60px;
    left: 15px;

    &-image {
      width: 30px;
      height: 30px;
    }

    &-bar {
      height: 21px;
      padding: 0 14px 0 20px;
      justify-content: center;
    }

    &-balance {
      font-size: 13px;
    }
  }

  &__multiplier {
    &-image {
      left: 5px;
      width: 25px;
      height: 25px;
    }

    &-bar {
      height: 21px;
      padding: 0 10px 0 22px;
    }

    &-balance {
      font-size: 14px;
    }
  }
}
</style>
