<script setup lang="ts">
import { computed, nextTick, watch } from 'vue'

import eventBanner from '@/assets/images/temp/hotrecord/banner.png'
import EventWindow from '@/components/events/EventWindow.vue'
import { useWindowQueue } from '@/composables/useWindowQueue'
import { getCurrencyRealAmount } from '@/constants/currency'
import { useClanEventLeaders, useUserClanEventInfo } from '@/services/client/useGameEvent'
import { useHotrecordRankStore } from '@/stores/eventRankStore'
import { useToast } from '@/stores/toastStore'
import { sendAnalyticsEvent } from '@/utils/analytics'
import { formatNumberToShortString } from '@/utils/number'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

const { t } = useI18n()

const { showToast } = useToast()
const router = useRouter()

const { leaderboard, isLoading: isLoadingLeaderboard } = useClanEventLeaders(() => {
  showToast('Event has ended', 'warning')
  close()
})

const {
  userInfo,
  isLoading: isLoadingUserInfo,
  isFetching: isFetchingUserInfo
} = useUserClanEventInfo()

const { closeWindowInQueue } = useWindowQueue('clan-event-welcome-window')
const rankStore = useHotrecordRankStore()

// on each event page open
const unwatch = watch(
  [userInfo, isFetchingUserInfo],
  ([newUserInfo, newIsFetching]) => {
    if (newUserInfo?.rank && !newIsFetching) {
      const rank = newUserInfo.rank
      const totalScore = newUserInfo.ticketsCollected
      rankStore.updateLastRank(rank)
      sendAnalyticsEvent('event_view', {
        event: 'clan',
        current_position: rank,
        total_points: totalScore ?? 0
      })
      nextTick(() => {
        unwatch()
      })
    }
  },
  { immediate: true }
)

const userData = computed(() => {
  return {
    rank: userInfo.value?.rank ?? 0,
    score: formatNumberToShortString(userInfo.value?.ticketsCollected ?? 0),
    balance: userInfo.value?.reward
      ? getCurrencyRealAmount(userInfo.value.reward.amount, userInfo.value.reward.currency)
      : 0,
    currency: userInfo.value?.reward?.currency ?? 'hard',
    league: userInfo.value?.leagueLevel ?? 1
  }
})

const close = () => {
  closeWindowInQueue()
  router.back()
}
</script>

<template>
  <EventWindow
    class="clan-event"
    id="clanEventWindow"
    :leaderboard="leaderboard"
    :isLoading="isLoadingLeaderboard || isLoadingUserInfo"
    :userInfo="userData"
    :eventBanner="eventBanner"
    @close="close"
  >
    <template #description>
      <p class="text-[12px] leading-[16px] text-white text-center">
        {{ t('clans.event.description') }}
      </p>
    </template>
  </EventWindow>
</template>

<style lang="scss">
.clan-event {
  top: 0;
  --event-background: linear-gradient(360deg, #bd0c0c 0%, #e54739 92.65%);
  --event-list-top-shadow: linear-gradient(180deg, #e14134 14.82%, rgba(225, 65, 52, 0) 68.56%);
  --event-list-bottom-shadow: linear-gradient(360deg, #c11110 14.82%, rgba(193, 17, 16, 0) 68.56%);

  .event-view {
    .close-button {
      --close-btn-background-color: #6c0b0a;
    }
  }
}
</style>
