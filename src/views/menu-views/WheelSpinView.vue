<script setup>
import ticket2 from '@/assets/images/temp/big-icons/ticket-2.png'
import ticket3 from '@/assets/images/temp/big-icons/ticket-3.png'
import ton from '@/assets/images/temp/big-icons/ton-1.png'
import ton2 from '@/assets/images/temp/big-icons/ton-2.png'
import ton3 from '@/assets/images/temp/big-icons/ton-3.png'
import stars from '@/assets/images/temp/stars.png'
import banner from '@/assets/images/temp/wheel-spin/banner.png'
import cloud1 from '@/assets/images/temp/wheel-spin/cloud-1.png'
import cloud2 from '@/assets/images/temp/wheel-spin/cloud-2.png'
import cloud3 from '@/assets/images/temp/wheel-spin/cloud-3.png'
import cloud4 from '@/assets/images/temp/wheel-spin/cloud-4.png'
import cloud5 from '@/assets/images/temp/wheel-spin/cloud-5.png'
import cloud6 from '@/assets/images/temp/wheel-spin/cloud-6.png'
import hard1 from '@/assets/images/temp/wheel-spin/hard-1.png'
import hard2 from '@/assets/images/temp/wheel-spin/hard-2.png'
import lightsOn from '@/assets/images/temp/wheel-spin/lights-on.png'
import lights from '@/assets/images/temp/wheel-spin/lights.png'
import shine from '@/assets/images/temp/wheel-spin/shine.png'
import wheelSpin from '@/assets/images/temp/wheel-spin/spin.png'
import tonShine from '@/assets/images/temp/wheel-spin/ton-shine.png'
import CountdownTimerManual from '@/components/UI/CountdownTimerManual.vue'
import VButton from '@/components/UI/VButton.vue'
import { useIconImage } from '@/composables/useIconImage.js'
import { useWheelSpinTimer } from '@/composables/useWheelSpinTimer.ts'
import { getCurrencyRealAmount } from '@/constants/currency.ts'
import { addToPlayerState, usePlayerState } from '@/services/client/usePlayerState.ts'
import {
  useGetFreeWheelSpin,
  useGetWheelSpin,
  useWheelSpinConfig
} from '@/services/client/useWheelSpins.js'
import { hapticsService } from '@/shared/haptics/hapticsService.js'
import { useToast } from '@/stores/toastStore.js'
import { formatNumberToShortString } from '@/utils/number.js'
import { hideBackButton, showBackButton } from '@telegram-apps/sdk'
import { computed, onUnmounted, ref, useTemplateRef } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { Roulette } from 'vue3-roulette'

const HOLD_DURATION = 1500

const HAPTIC_DURATION = 1500
const HAPTIC_FREQUENCY = 50

const SPINNING_DURATION = 1.6
const ANIMATION_DURATION = 2000
const AWARD_DELAY = 2000
const AUTOSPIN_DELAY = ANIMATION_DURATION + AWARD_DELAY

const SECTOR_TO_IMAGE = {
  1: ticket2,
  2: ton3,
  3: hard1,
  4: wheelSpin,
  5: ton,
  6: ticket3,
  7: ton2,
  8: hard2
}

const { getImageClass } = useIconImage()
const { showToast } = useToast()
const router = useRouter()
const { t } = useI18n()

const wheelElemRef = useTemplateRef('wheel')
const resultIndex = ref(null)
const firstItemIndex = ref({ value: 0 })
const rouletteKey = ref(0)
const isWheelStarted = ref(false)
const wheelSpinReward = ref(null)
const isHeld = ref(false)
let timer = null

const { playerState } = usePlayerState()
const { updatePlayerState } = addToPlayerState()

const { isFreeSpinBlocked, hours, minutes, seconds } = useWheelSpinTimer()
const { sectors } = useWheelSpinConfig()
const { getFreeWheelSpin } = useGetFreeWheelSpin()
const { getWheelSpin } = useGetWheelSpin()

const wheelSpinsAmount = computed(() => {
  return playerState.value?.wheelSpins?.amount ?? 0
})

const formattedSectors = computed(() => {
  return sectors.value.map(({ sector, displayText }) => {
    return {
      id: sector,
      background: 'transparent',
      htmlContent: `<div class="relative flex flex-col justify-center items-center top-4">
                      <img
                      class="w-[90px] h-[90px] absolute ${sector === 2 ? 'block' : 'hidden'}"
                      src="${tonShine}"
                      alt="top prize" />
                      <img class="w-[50px] h-[50px] z-10" src="${SECTOR_TO_IMAGE[sector]}" alt="prize" />
                      <p class="absolute bottom-0.5 text-[14px] text-shadow z-10">${displayText}</p>
                    </div>`
    }
  })
})

const launchWheel = sector => {
  resultIndex.value = { value: sector - 1 }
  setTimeout(() => wheelElemRef.value.launchWheel(), 0)
}

const resetWheel = () => {
  firstItemIndex.value = resultIndex.value
  rouletteKey.value += 1
  resultIndex.value = null
  // setTimeout(() => wheelElemRef.value.reset(), 0)
}

const onWheelStart = () => {
  hapticsService.triggerHapticEventForDuration(
    () => hapticsService.triggerNotificationHapticEvent('success'),
    HAPTIC_DURATION,
    HAPTIC_FREQUENCY
  )
}

const showWheelSpinReward = (reward, isFree = false) => {
  setTimeout(() => {
    wheelSpinReward.value = reward

    setTimeout(() => {
      if (!isFree) {
        updatePlayerState(reward.type, reward.value)
      }

      wheelSpinReward.value = null
      resetWheel()

      if (!isHeld.value || (!wheelSpinsAmount.value && isFreeSpinBlocked.value)) {
        isWheelStarted.value = false
        showBackButton()
      }
    }, ANIMATION_DURATION)
  }, AWARD_DELAY)
}

const goToShop = () => {
  router.push({ name: 'shop', query: { scrollTo: 'wheelSpins' } })
}

const runFreeSpin = async () => {
  isWheelStarted.value = true
  hideBackButton()

  await getFreeWheelSpin()
    .then(({ sector, reward }) => {
      launchWheel(sector)
      showWheelSpinReward(reward, true)
    })
    .catch(() => {
      isWheelStarted.value = false
      showBackButton()
      showToast('Something went wrong', 'warning')
    })
}

const runSpin = async () => {
  isWheelStarted.value = true
  hideBackButton()
  await getWheelSpin()
    .then(({ sector, reward }) => {
      launchWheel(sector)
      showWheelSpinReward(reward)

      setTimeout(() => {
        if (isHeld.value) {
          if (wheelSpinsAmount.value) {
            runSpin()
          } else if (!isFreeSpinBlocked.value) {
            runFreeSpin()
          } else {
            isWheelStarted.value = false
            isHeld.value = false
          }
        }
      }, AUTOSPIN_DELAY)
    })
    .catch(() => {
      isHeld.value = false
      isWheelStarted.value = false
      showBackButton()
      showToast('Something went wrong', 'warning')
    })
}

const onPointerDown = () => {
  if ((isFreeSpinBlocked.value && wheelSpinsAmount.value < 2) || !wheelSpinsAmount.value) return
  isHeld.value = false
  timer = setTimeout(() => {
    isHeld.value = true
    hapticsService.triggerImpactHapticEvent('heavy')
    runSpin()
  }, HOLD_DURATION)
}

const onPointerUp = () => {
  if (timer) clearTimeout(timer)
  if (!isHeld.value) {
    if (isFreeSpinBlocked.value) {
      runSpin()
    } else {
      runFreeSpin()
    }
  }
}

onUnmounted(() => {
  if (isHeld.value) {
    isHeld.value = false
  }
  if (isWheelStarted.value) {
    isWheelStarted.value = false
  }
})
</script>

<template>
  <div class="wheel-spin-container w-full h-full flex flex-col items-center justify-around">
    <!-- Background clouds images -->
    <img class="absolute left-0 top-0 h-[12%]" :src="cloud1" alt="cloud" />
    <img class="absolute right-0 top-0 h-[13%]" :src="cloud2" alt="cloud" />
    <img class="absolute left-0 bottom-48 h-[7%]" :src="cloud3" alt="cloud" />
    <img class="absolute right-5 bottom-44 h-[7%]" :src="cloud4" alt="cloud" />
    <img class="absolute left-0 bottom-24 h-[10%]" :src="cloud5" alt="cloud" />
    <img class="absolute right-0 bottom-24 h-[10%]" :src="cloud6" alt="cloud" />

    <!-- Banner image  -->
    <div class="h-1/3 flex items-center justify-center">
      <img class="h-[40%] z-10" :src="banner" alt="banner" />
    </div>
    <!-- Wheel Spin container -->
    <div v-if="formattedSectors.length" class="h-1/3 flex items-center justify-center relative">
      <!-- Shine/lights images -->
      <img :src="shine" class="absolute w-[500px]" alt="shine" />
      <img :src="lights" class="absolute w-[330px]" alt="lights" />
      <img v-if="isWheelStarted" :src="lightsOn" class="lights absolute w-[330px]" alt="lights" />

      <!-- Wheel Spin -->
      <Roulette
        ref="wheel"
        easing="bounce"
        indicator-position="top"
        centered-indicator
        display-indicator
        :key="rouletteKey"
        :wheel-result-index="resultIndex"
        :items="formattedSectors"
        :first-item-index="firstItemIndex"
        :duration="SPINNING_DURATION"
        @wheel-start="onWheelStart"
      />

      <!-- Reward -->
      <div
        v-if="wheelSpinReward"
        class="wheel-spin-reward absolute w-[400px] h-[400px] flex items-center justify-center"
        :style="{
          '--appear-duration': `${ANIMATION_DURATION}ms`
        }"
      >
        <div class="flex items-center justify-center gap-x-1">
          <div class="icon-bg !w-[50px] !h-[50px]" :class="getImageClass(wheelSpinReward.type)" />
          <p class="text-[40px] text-shadow text-shadow_black text-shadow_thin">
            +{{
              formatNumberToShortString(
                getCurrencyRealAmount(wheelSpinReward.value ?? 0, wheelSpinReward.type)
              )
            }}
          </p>
        </div>
        <img :src="stars" class="absolute w-[330px]" alt="stars" />
      </div>
    </div>

    <!-- Spin button container -->
    <div class="h-1/3 w-full flex flex-col items-center justify-center">
      <!-- Go to shop button  -->
      <div
        v-if="!isWheelStarted && isFreeSpinBlocked && !wheelSpinsAmount"
        class="!w-full max-w-[250px] relative"
      >
        <div class="absolute z-10 top-[15px] left-[15px] flex items-center justify-center">
          <div class="absolute -z-10 icon-bg store-bg !w-[45px] !h-[45px]"></div>
        </div>
        <VButton class="!w-full !h-[55px]" type="accent" @click="goToShop">
          <template #content>
            <div class="w-full flex items-center justify-between gap-[5px]">
              <p class="text-[26px] leading-[28px] text-shadow text-shadow_thin text-shadow_black">
                {{ t('actions.goToShop') }}
              </p>
            </div>

            <!-- Free spin timer -->
            <div
              v-if="isFreeSpinBlocked"
              class="absolute left-1/2 -translate-x-1/2 -bottom-12 flex items-center justify-center py-1 px-5 rounded-[5px] z-10 bg-[#295699]"
            >
              <p class="text-[16px] leading-[22px] mr-1">Free spin in</p>
              <CountdownTimerManual
                class="text-[14px] leading-[22px] text-[#FFFFFF]"
                :hours="hours"
                :minutes="minutes"
                :seconds="seconds"
              />
            </div>
          </template>
        </VButton>
      </div>

      <!-- Stop button  -->
      <VButton
        v-else-if="isHeld && (wheelSpinsAmount || !isFreeSpinBlocked)"
        type="default"
        size="medium"
        class="!w-full max-w-[250px] !h-[55px]"
        @click="isHeld = false"
      >
        <template #content>
          <div class="w-full flex items-center justify-between gap-[5px]">
            <p class="text-[26px] leading-[28px] text-shadow text-shadow_thin text-shadow_black">
              Stop
              <span class="-ml-[0px]">[</span>
              <span class="mx-[3px]">{{
                isFreeSpinBlocked ? wheelSpinsAmount : wheelSpinsAmount + 1
              }}</span>
              <span class="-mr-[3px]">]</span>
            </p>
          </div>
        </template>
      </VButton>

      <!-- Spin button  -->
      <VButton
        v-else-if="!isWheelStarted"
        :type="'success'"
        size="medium"
        class="!w-full max-w-[250px] !h-[55px]"
        @pointerdown="onPointerDown"
        @pointerup="onPointerUp"
      >
        <template #content>
          <div class="w-full flex flex-col items-center justify-between gap-[5px]">
            <template v-if="(!isFreeSpinBlocked && wheelSpinsAmount) || wheelSpinsAmount > 1">
              <p class="text-[26px] text-shadow text-shadow_thin text-shadow_black">
                {{ t('actions.spin') }}
                <span class="-ml-[0px]">[</span>
                <span class="mx-[3px]">{{
                  isFreeSpinBlocked ? wheelSpinsAmount : wheelSpinsAmount + 1
                }}</span>
                <span class="-mr-[3px]">]</span>
              </p>
              <p class="text-[11px] text-[#025100] uppercase">
                {{ t('actions.hold') }}
              </p>
            </template>
            <template v-else>
              <p class="text-[26px] leading-[28px] text-shadow text-shadow_thin text-shadow_black">
                Spin
              </p>
            </template>
          </div>

          <!-- Free spin timer -->
          <div
            v-if="isFreeSpinBlocked"
            class="absolute left-1/2 -translate-x-1/2 -bottom-11 flex items-center justify-center py-1 px-5 rounded-[5px] z-10 bg-[#295699]"
          >
            <p class="text-[16px] leading-[22px] mr-1">Free spin in</p>
            <CountdownTimerManual
              class="text-[14px] leading-[22px] text-[#FFFFFF]"
              :hours="hours"
              :minutes="minutes"
              :seconds="seconds"
            />
          </div>
        </template>
      </VButton>
    </div>
  </div>
</template>

<style lang="scss">
.wheel-spin-container {
  background-image: url('@/assets/images/temp/background.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  .lights {
    animation: shine-pulse 1s linear infinite;
  }

  .wheel-container {
    overflow: inherit !important;

    &::before {
      left: 49.5%;
      top: -20px;
      border: none !important;
      width: 200px !important;
      height: 200px !important;
      background-image: url('@/assets/images/temp/wheel-spin/indicator.png');
      background-size: contain;
      background-position: center;
      background-repeat: no-repeat;
    }

    .wheel {
      background-color: transparent;
      background-image: url('@/assets/images/temp/wheel-spin/base.png');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;

      .wheel-item {
        border: none !important;
      }
    }
  }

  .wheel-spin-reward {
    border-radius: 100%;
    animation: appear var(--appear-duration) forwards;
    background: radial-gradient(50% 50% at 50% 50%, #10d4ff 17.5%, #10d4ff00 100%);
  }
}

@keyframes appear {
  0%,
  100% {
    opacity: 0;
  }
  40%,
  70% {
    opacity: 1;
  }
}

@keyframes shine-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.1;
  }
  50% {
    transform: scale(1.01);
    opacity: 1;
  }
}
</style>
