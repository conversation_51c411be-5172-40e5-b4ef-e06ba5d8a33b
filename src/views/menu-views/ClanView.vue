<script setup lang="ts">
import VTabs from '@/components/UI/VTabs.vue'
import { usePlayerState } from '@/services/client/usePlayerState';
import ClansList from '@/components/clans/ClansList.vue'
import MyClan from '@/components/clans/MyClan.vue';
import ClanWindow from '@/components/clans/ClanWindow.vue';
import { ref } from 'vue';

const { playerState } = usePlayerState()

const isOpenClanDialog = ref(false)
const clanIdToOpen = ref<number>(0)
</script>

<template>
  <div v-if="playerState!.clanId" class="view-container">
    <VTabs
      :tabs="[
        { name: 'clans.myClan', id: 'my-clan' },
        { name: 'clans.topClans', id: 'top-clans' },
      ]"
    >
      <template #my-clan><MyClan class="clans-view" :user-clan="playerState!.clanId" /></template>
      <template #top-clans>
        <ClansList
          class="clans-view"
          :user-clan="playerState!.clanId"
          @open-clan="(id: number) => (isOpenClanDialog = true, clanIdToOpen = id)"
        />
      </template>
    </VTabs>
  </div>
  <ClansList
    v-else
    class="clans-view"
    :user-clan="playerState!.clanId"
    @open-clan="(id: number) => (isOpenClanDialog = true, clanIdToOpen = id, console.log('id:', id))"
  />
  <ClanWindow
    class="clans-view"
    :is-open="isOpenClanDialog"
    :clan-id="clanIdToOpen"
    :user-clan="playerState!.clanId"
    @close="isOpenClanDialog = false"
  />
</template>

<style lang="scss">
.clans-view {
  .scoreboard-item {
    &__reward {
      &-bar {
        min-width: auto;
        width: auto;
        padding-left: 20px;
        padding-right: 9px;
        justify-content: center;

        &::after {
          width: 100%;
        }
      }
    }
  }

  &__shadow-gradient {
    position: sticky;
    z-index: 1;
    left: 0;
    width: 100%;
    height: 28px;

    &:first-child {
      top: 0;
      background: linear-gradient(180deg, var(--menu-color) 40%, rgba(41, 87, 154, 0) 100%);
      transform: translateY(-1px);
    }

    &:last-child {
      bottom: 0;
      background: linear-gradient(360deg, var(--menu-color) 40%, rgba(41, 87, 154, 0) 100%);
      transform: translateY(1px);
    }
  }
}
</style>
