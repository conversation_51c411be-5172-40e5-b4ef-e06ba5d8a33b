<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n';
import { useIntersectionObserver } from '@vueuse/core'
import { useToast } from '@/stores/toastStore';
import { hapticsService } from '@/shared/haptics/hapticsService.ts'

import copyIcon from '@/assets/images/temp/copy.svg';
import tgLogoPremium from '@/assets/images/temp/premium-star.png';
import tgLogoPremiumStroke from '@/assets/images/temp/premium-star-stroke.png';
import starImage from '@/assets/images/temp/currency/hard-coin.png'
import friendsBanner from '@/assets/images/temp/banners/friends-banner.png'

import VButton from '@/components/UI/VButton.vue';
import LoaderText from '@/components/LoaderText.vue';

import { useReferralsList }  from '@/services/client/useReferralsList'
import { usePlayerState } from '@/services/client/usePlayerState';
import { useClaimReferralReward } from '@/services/client/useClaimReferralReward';
import { useRefsAchievement } from '@/composables/useAchievementsCheck.ts'
import { useReferralLink } from '@/composables/useReferralLink'
import FriendItem from '@/components/FriendItem.vue';
import { usePlayerProfileStore } from '@/stores/playerProfileStore';
import { useRewardStore } from '@/stores/rewardStore';

const { t } = useI18n()

const { showToast } = useToast()
const { checkRefsAchievement } = useRefsAchievement()
const profileStore = usePlayerProfileStore()
const rewardStore = useRewardStore()

const { playerState } = usePlayerState()

const {
  referralsList,
  isFetching: isFetchingReferrals,
  hasNextPage,
  fetchNextPage
} = useReferralsList()
const { claimReferralReward } = useClaimReferralReward((ticketsClaimed) => {
  rewardStore.showReward([{
    type: 'tickets',
    value: ticketsClaimed,
    prevValue: playerState.value?.tickets ?? 0
  }])
})

const claimReward = () => {
  claimReferralReward()
}

const {
  copyRefToClipboard,
  forwardRefLink
} = useReferralLink()

const copyRef = () => {
  copyRefToClipboard().then(() => {
    showToast(t('linkCopied'), 'info')
    hapticsService.triggerNotificationHapticEvent('success')
  })
}

const referralsAmount = computed(() => playerState.value?.refs ?? 0)

const nextPageTarget = ref(null)
useIntersectionObserver(
  nextPageTarget,
  ([{ isIntersecting }]) => {
    if (isIntersecting) {
      fetchNextPage()
    }
  },
)

onMounted(() => {
  checkRefsAchievement()
})
</script>

<template>
  <div class="view-container menu-item menu-item_one">
    <header class="text-center space-y-[2px] pb-[23px] tracking-normal">
      <h1 class="text-[30px] leading-[40px] text-shadow">
        {{ t('friends.inviteFriends') }}
      </h1>
      <p class="font-extrabold text-[15px] leading-[22px] text-[#6DB0ED]">
        {{ t('friends.description') }}
      </p>
    </header>
    <section class="referral-bonus mb-[18px]">
      <article class="referral-bonus__item">
        <div class="white-spot w-[32px] h-[6.5px] left-[22px] top-px"></div>
        <div class="referral-bonus__reward">
          <img :src="starImage" class="referral-bonus__reward-image" alt="star" />
          <p class="text-[30px] text-shadow">
            +1
          </p>
        </div>
        <div class="referral-bonus__description text-[#1E4073]">
          {{ t('friends.freeInviteDesc') }}
        </div>
      </article>
      <article class="referral-bonus__item">
        <div class="white-spot w-[32px] h-[6.5px] left-[22px] top-px"></div>
        <img class="w-[38px] absolute -right-[14px] -top-[15px]" :src="tgLogoPremium" alt="telegram logo premium" />
        <img class="w-[38px] absolute right-0 top-[4px]" :src="tgLogoPremiumStroke" alt="telegram logo stroke" />
        <div class="referral-bonus__reward">
          <img :src="starImage" class="referral-bonus__reward-image" alt="star" />
          <p class="text-[30px] text-shadow">
            +5
          </p>
        </div>
        <div class="referral-bonus__description text-[#332B85]">
          {{ t('friends.premiumInviteDesc') }}
        </div>
      </article>
      <article class="referral-bonus__item">
        <img :src="friendsBanner" class="w-full" alt="friends banner"/>
      </article>
    </section>
    <div class="invite-menu mb-[35px]">
      <VButton
        type="success"
        :text="t('friends.inviteFriends')"
        class="!w-full"
        @click="forwardRefLink"
      />
      <VButton
        type="success"
        :image="copyIcon"
        @click="copyRef"
        :haptic="false"
      />
    </div>
    <section>
      <div class="flex justify-between items-end mb-[10px] h-[32px]">
        <p class="text-[16px] leading-[22px] font-extrabold text-[#6DB0ED]">
          {{ t('friends.invitedCount', { count: referralsAmount }) }}
        </p>
        <VButton
          v-if="!!playerState?.ticketsUnclaimed"
          class="min-w-[121px]"
          :text="t('actions.collect')"
          type="accent"
          size="small"
          @click="claimReward"
        />
      </div>
      <div
        v-if="referralsList.length"
        class="space-y-[6px]"
      >
        <FriendItem
          v-for="referral, index in referralsList"
          :key="index"
          :username="referral.name"
          :tickets-unclaimed="referral.ticketsUnclaimed ?? 0"
          :tickets-claimed="referral.ticketsClaimed ?? 0"
          :league="referral.leagueLevel"
          @click="() => profileStore.openProfile(referral.id)"
        />
      </div>
      <div
        v-else-if="!isFetchingReferrals"
        class="bg-[#0F4589] rounded-[5px] py-2 font-extrabold text-[#6DB0ED] text-[16px] leading-[22px] text-center mt-[5px] whitespace-pre"
      >
        {{ t('friends.emptyRefs') }}
      </div>
      <div v-if="hasNextPage && !isFetchingReferrals" ref="nextPageTarget" class="w-full h-[16px] mt-[5px]"></div>

      <LoaderText
        class="font-extrabold text-[#6DB0ED] text-[16px] mt-8 pb-3 text-center"
        :class="{ 'pt-5': referralsAmount <= 0 }"
        :isLoading="isFetchingReferrals"
      />
    </section>
  </div>
</template>

<style lang="scss" scoped>
.referral-bonus {
  display: grid;
  grid-template-columns: 4fr 6fr;
  grid-template-rows: 78px auto;
  gap: 16px;

  &__item {
    position: relative;
    border-radius: 9px;
    background: #DBEAFF;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    row-gap: 3px;
    box-shadow: #00000033 0 2px, inset white 0 4px;

    &:first-child {
      background: linear-gradient(360deg, #72C3E6 0%, #B7E9FF 92.65%);
    }

    &:nth-child(2) {
      background: linear-gradient(360deg, #98CDFF 0%, #FFD6F4 92.65%);
    }

    &:last-child {
      grid-column: span 2;
      overflow: hidden;
      background-color: transparent;
      box-shadow: none;
      padding-top: 4px;

      img {
       border-radius: 9px;
      }
    }
  }

  &__reward {
    display: flex;
    justify-content: center;
    align-items: center;
    column-gap: 7.5px;
    font-size: 30px;
    font-weight: 900;

    &-image {
      width: 40px;
    }
  }

  &__description {
    text-align: center;
    font-size: 12px;
  }
}

.invite-menu {
  width: 100%;
  display: flex;
  gap: 12px;
  padding: 0 15px;
}
</style>
