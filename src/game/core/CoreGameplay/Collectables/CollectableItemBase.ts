import { DepthOrder } from '@/game/core/CoreGameplay/Constants/DephOrdering'
import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { ChunkInfo } from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import { InteractionType } from '@/game/core/MapGen/GameSessionManager'
import { ENTITIES_SIZES, EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { Logger, logger } from '@/shared/Logger'
import { Vector2 } from '@esotericsoftware/spine-phaser'
import Phaser from 'phaser'
import { getRandomNumberInRange } from '../../HelperFunctions'
import { CollectableConsts } from '../Constants/BoostersConsts'
import type { CollectableOptions } from './CollectableOptions'
import Sprite = Phaser.GameObjects.Sprite

export class CollectableItemBase {
  protected readonly sprite: Sprite
  protected readonly collectableGroup: Phaser.Physics.Arcade.StaticGroup
  protected collectableType: EntityType
  protected logger: Logger = logger
  protected readonly body: Phaser.Physics.Arcade.Sprite
  protected readonly staticBody: Phaser.Physics.Arcade.StaticBody
  protected scene: GameScene | null = null
  public isCollected: boolean = false
  protected gameInterface: GameInterface
  protected chunkInfo: ChunkInfo
  private readonly textureString: string
  private currentIndex: number = 0
  private amount: number
  protected platformOffsetX: number
  private basePosition: Vector2 = new Vector2(0, 0)

  public isActive!: boolean
  public nextFree: this | null = null

  constructor(options: CollectableOptions) {
    const { collectableGroup, scene, position, entityType, gameInterface, chunkInfo, amount } =
      options
    this.collectableGroup = collectableGroup
    this.scene = scene
    this.collectableType = entityType
    this.gameInterface = gameInterface
    this.chunkInfo = chunkInfo
    this.amount = amount

    const { x, y } = position
    this.basePosition = new Vector2(x, y)
    this.textureString = entityType.toString()
    if (entityType == EntityType.CollectibleDynamicCoin) {
      this.textureString = EntityType.CollectibleCustomCoin.toString()
    }
    this.sprite = collectableGroup.create(x, y, AtlasNames.ENV, `${this.textureString}.png`)
    this.sprite.setDepth(DepthOrder.Collectables)

    const [w, h] = ENTITIES_SIZES[this.collectableType]
    this.sprite.setDisplaySize(w, h).setOrigin(0, 0)

    if (__DEV__)
      this.logger.log('CollectableItemBase', `Created collectable: ${this.textureString}`)
    this.body = this.sprite as Phaser.Physics.Arcade.Sprite
    this.body.setImmovable(true).setOrigin(0, 0)
    this.body.refreshBody()
    scene.physics.add.existing(this.sprite)
    this.staticBody = this.sprite.body as Phaser.Physics.Arcade.StaticBody
    this.platformOffsetX = getRandomNumberInRange(CollectableConsts.PLATFORM_X_OFFSET)
  }

  reset(options: CollectableOptions): void {
    const { collectableGroup, position, entityType, chunkInfo, gameInterface, amount } = options
    this.collectableType = entityType
    this.gameInterface = gameInterface
    this.chunkInfo = chunkInfo
    this.amount = amount

    const { x, y } = position
    let texture = entityType.toString()
    //todo: fix when sprite will be added
    if (entityType == EntityType.CollectibleDynamicCoin) {
      texture = EntityType.CollectibleCustomCoin.toString()
    }

    if (this.sprite.texture.key !== texture) {
      this.sprite.setTexture(AtlasNames.ENV, `${texture}.png`)
    }
    this.sprite.setPosition(x, y)
    this.basePosition = new Vector2(x, y)
    this.setVisible(true)
    this.setActive(true)
    this.isCollected = false

    const [w, h] = ENTITIES_SIZES[this.collectableType]
    if (this.body && this.body.body) {
      this.body.setDisplaySize(w, h).setOrigin(0, 0)
      this.body.body.enable = true
      this.body.body.checkCollision.none = false
      this.body.refreshBody()
    }

    this.platformOffsetX = getRandomNumberInRange(CollectableConsts.PLATFORM_X_OFFSET)
    collectableGroup.add(this.sprite)
    this.sprite.data?.reset()
  }

  changeVisuals() {
    const textures: string[] = ['1', '2']
    this.currentIndex = (this.currentIndex + 1) % textures.length
    if (this.currentIndex > textures.length) {
      this.currentIndex = 0
    }
    this.sprite.setTexture(this.getType().toString() + textures[this.currentIndex])
  }

  returnToPool(): void {
    this.sprite.data?.reset()
    this.collectableGroup.remove(this.sprite)
    this.setVisible(false)
  }

  collect(playerPos: Vector2): void {
    console.log('CollectableItemBase', this.basePosition)
    this.isCollected = true

    this.gameInterface.registerInteraction(
      this.chunkInfo,
      InteractionType.CollectableCollide,
      this.basePosition.x,
      this.basePosition.y
    )
  }

  returnToPoolWithDelay() {
    this.disableCollision()
    this.scene?.time.delayedCall(500, () => {
      this.returnToPool()
    })
  }

  syncPosition(x: number) {
    if (this.sprite.getData('isBeingAttracted')) return
    if (!this.staticBody) return

    this.sprite.x = x + this.platformOffsetX
    this.staticBody.x = x + this.platformOffsetX
  }

  syncSpineAndColliderPosition() {}

  destroyCollectable(): void {
    if (__DEV__) this.logger.log('CollectableItemBase', 'Destroying collectable')
    this.collectableGroup.remove(this.sprite)
    this.sprite.removeAllListeners()

    if (this.staticBody) {
      this.staticBody.destroy()
    }
    if (this.body) {
      this.body.destroy(true)
    }
    this.sprite.destroy(true)
    this.scene = null
  }

  setActive(active: boolean): this {
    this.sprite.setActive(active)
    return this
  }

  setVisible(visible: boolean): this {
    this.sprite.setVisible(visible)
    return this
  }

  getSprite(): Sprite {
    return this.sprite
  }

  getType(): EntityType {
    return this.collectableType
  }

  disableCollision() {
    this.body.body!.enable = false
  }

  getAmount(): number {
    return this.amount
  }
}
