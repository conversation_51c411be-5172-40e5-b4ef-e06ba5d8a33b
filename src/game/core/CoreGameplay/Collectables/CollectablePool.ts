import type { GameInterface } from '@/game/core/CoreGameplay/GameInterface'
import type { EntityType } from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import Phaser from 'phaser'
import { CollectableItemBase } from './CollectableItemBase'
import type { CollectableOptions } from './CollectableOptions'

export class CollectablePool<
  T extends CollectableItemBase & { isActive: boolean; nextFree: T | null }
> {
  private readonly pool: Set<T> = new Set()
  private freeListHead: T | null = null
  private freeListCount: number = 0

  private readonly freePoolLimit: number

  private readonly scene: Phaser.Scene
  private readonly CollectableClass: new (options: CollectableOptions) => T
  private readonly gameInterface: GameInterface

  constructor(
    scene: Phaser.Scene,
    CollectableClass: new (options: CollectableOptions) => T,
    gameInterface: GameInterface,
    freePoolLimit: number
  ) {
    this.scene = scene
    this.CollectableClass = CollectableClass
    this.gameInterface = gameInterface
    this.freePoolLimit = freePoolLimit
  }

  async preload(
    count: number,
    entityType: EntityType,
    collectableGroup: Phaser.Physics.Arcade.StaticGroup
  ): Promise<void> {
    for (let i = 0; i < count; i++) {
      const options: CollectableOptions = {
        collectableGroup,
        scene: this.scene as GameScene,
        position: { x: -1000, y: -1000 },
        entityType,
        gameInterface: this.gameInterface,
        chunkInfo: {
          index: 0,
          id: 0,
          platformIndex: 0,
          length: 0,
          prevChunkCumulativeLenght: 0,
          generation: 0
        },
        amount: 1
      }
      const collectable = new this.CollectableClass(options)
      collectable.returnToPool()
      collectable.isActive = false
      collectable.nextFree = null
      this.pool.add(collectable)

      collectable.nextFree = this.freeListHead
      this.freeListHead = collectable
      this.freeListCount++
    }
  }

  get(collectableOptions: CollectableOptions): T {
    let collectable: T
    if (this.freeListHead) {
      collectable = this.freeListHead
      this.freeListHead = collectable.nextFree
      collectable.nextFree = null
      this.freeListCount--
      collectable.isActive = true
      collectable.reset(collectableOptions)
    } else {
      collectable = new this.CollectableClass(collectableOptions)
      collectable.isActive = true
      collectable.nextFree = null
      this.pool.add(collectable)
    }
    return collectable
  }

  return(collectable: T): void {
    collectable.returnToPool()
    collectable.isActive = false

    if (this.freeListCount < this.freePoolLimit) {
      collectable.nextFree = this.freeListHead
      this.freeListHead = collectable
      this.freeListCount++
    } else {
      this.pool.delete(collectable)
      collectable.destroyCollectable()
    }
  }

  clear(): void {
    this.pool.forEach(collectable => {
      collectable.destroyCollectable()
    })
    this.pool.clear()
    this.freeListHead = null
    this.freeListCount = 0
  }
}
