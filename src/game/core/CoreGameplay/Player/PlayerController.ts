import type { <PERSON><PERSON>anager } from '@/game/core/CoreGameplay/CameraManager'
import type { SpawnedTokenData } from '@/game/core/CoreGameplay/Collectables/TokenCollectable.ts'
import { AtlasNames } from '@/game/core/CoreGameplay/Constants/GameViewConsts.ts'
import { PlayerConsts } from '@/game/core/CoreGameplay/Constants/PlayerConsts'
import { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import type { Interactables } from '@/game/core/CoreGameplay/Interactables.ts'
import { EntityType } from '@/game/core/MapGen/MapGenerator.ts'
import { GAME_EVENTS } from '@/shared/constants/uiEvents'
import { cachedPlayerState } from '@/shared/storage/cachedPlayerState.ts'
import type { EventBus } from '@/shared/types'
import { SpineGameObject, Vector2 } from '@esotericsoftware/spine-phaser'
import { removeEntity } from 'bitecs'
import { getGameWorld } from '../../ecs/GameWorld'
import { bulletSystem } from '../../ecs/Systems/BulletSystem'
import { GameScene } from '../../scenes/GameScene'
import type { BaseBooster } from '../Boosters/BaseBooster'
import type { CollectableItemBase } from '../Collectables/CollectableItemBase'
import { DepthOrder } from '../Constants/DephOrdering'
import type { BasePlatform } from '../Platforms/BasePlatform'
import {
  Attribute,
  createPlayerEntity,
  getPlayerAttribute,
  setPlayerAttribute
} from './PlayerAttributeContainer'
import { PlayerCollisionDetector } from './PlayerCollisionDetector'
import { PlayerDeathHandler } from './PlayerDeathHandler'
import { PlayerEnemyInteractions } from './PlayerEnemyInteractions'
import { PlayerInteractions } from './PlayerInteractions'
import { PlayerMovement } from './PlayerMovement'
import {
  PlayerShootingHandler,
  bulletHandler,
  initializeBulletHandler
} from './PlayerShootingHandler'
import { PlayerSpineSkins } from './PlayerSpineSkins'
import { PlayerStateMachine } from './PlayerStates/PlayerStateMachine'
import { PlayerStates } from './PlayerStates/States/PlayerStates'
import { MobDataSetKeys } from './PlayerStates/States/SpineAnimations'
import { StartersManager } from './Starters/StartersManager'

export class PlayerController {
  private readonly scene: GameScene
  private uiEventBus: EventBus
  private collisionDetector!: PlayerCollisionDetector
  private playerEnemyInteractions!: PlayerEnemyInteractions
  private playerShootingHandler!: PlayerShootingHandler
  private playerStateMachine!: PlayerStateMachine
  private playerSkins!: PlayerSpineSkins
  private playerDeathHandler!: PlayerDeathHandler
  private playerInteractions!: PlayerInteractions
  private playerMovement!: PlayerMovement
  private startersManager!: StartersManager

  private playerSpine!: SpineGameObject
  private playerSprite!: Phaser.Physics.Arcade.Sprite
  private playerBody!: Phaser.Physics.Arcade.Body
  private interactables: Interactables
  private cameraManager: CameraManager

  private cachedTweens: { [key: string]: Phaser.Tweens.Tween } = {}
  private initialScaleX: number = 1
  private initialScaleY: number = 1
  private accumulatedTime: number = 0
  private playerEntity!: number
  private spineScale!: number

  public ticketsScore: number = 0
  public ticketsMultiplier: number = 1
  public playerMaxHeight: number = 0
  public customCoinScore: number = 0
  public dynamicCoinScore: number = 0
  public tonScore: number = 0

  constructor(
    scene: GameScene,
    interactables: Interactables,
    uiEventBus: EventBus,
    cameraManager: CameraManager
  ) {
    this.scene = scene
    this.uiEventBus = uiEventBus
    this.interactables = interactables
    this.cameraManager = cameraManager
    this.playerEntity = createPlayerEntity(getGameWorld())

    this.initializeCollisionDetector(scene, interactables)
    this.scene.input.keyboard!.createCursorKeys()
  }

  create(
    startPosition: Vector2 | undefined,
    ticketsMultiplier: number
  ): Phaser.Physics.Arcade.Sprite {
    const playerPos = this.getPlayerStartPosition(startPosition)
    this.ticketsMultiplier = ticketsMultiplier

    this.createPlayerSprite(playerPos)
    this.setupPlayerBody()
    this.createSpineBasedOnCachedSkin()
    this.setupPlayerComponents()
    this.registerEventHandlers()

    this.playerMovement.updateSpine(this.playerSpine)
    this.playerDeathHandler.updateSpine(this.playerSpine)

    this.jump(PlayerConsts.JUMP_VELOCITY * PlayerConsts.INIT_JUMP_VELOCITY)

    setPlayerAttribute(Attribute.PlayerCreated, this.playerEntity, true)
    const hasPassedFTUE = !cachedPlayerState?.playerState?.tutorial
    setPlayerAttribute(Attribute.IsPlayerPassedFTUE, this.playerEntity, hasPassedFTUE)
    setPlayerAttribute(Attribute.CanShoot, this.playerEntity, true)

    this.uiEventBus.emit(GAME_EVENTS.UPDATE_TICKETS_SCORE, this.ticketsScore)

    return this.playerSprite
  }

  private createSpineBasedOnCachedSkin(): void {
    const skinId = cachedPlayerState.skinId ?? -1

    const spineDataKey = MobDataSetKeys.UNICORN_NEW_DATA
    const atlasKey = MobDataSetKeys.UNICORN_NEW_ATLAS
    const scaleMultiplier = PlayerConsts.NEW_SPINE_SCALE_MULTIPLIER

    if (this.playerSpine) {
      this.playerSpine?.destroy()
    }

    this.playerSpine = this.scene.add.spine(
      this.playerSprite.x,
      this.playerSprite.y,
      spineDataKey,
      atlasKey
    )
    this.playerSpine.scale = this.playerSprite.scale * scaleMultiplier
    this.spineScale = this.playerSpine.scale
    this.playerSpine?.setOrigin(0.5, 0.5)
    this.playerSpine?.setDepth(DepthOrder.Player)

    this.playerSkins = new PlayerSpineSkins(this.playerSpine, this.uiEventBus)
    this.playerSkins.setSkin(skinId)

    this.playerStateMachine = new PlayerStateMachine(this.playerSpine, this.scene)

    this.playerSkins.triggerAdditionalAnimationForSkin()
  }

  update(time: number, delta: number): void {
    if (!getPlayerAttribute(Attribute.PlayerCreated, this.playerEntity)) return

    const deltaTime = delta / 1000
    this.accumulatedTime += deltaTime

    while (this.accumulatedTime >= PlayerConsts.FIXED_TIME_STEP) {
      this.accumulatedTime -= PlayerConsts.FIXED_TIME_STEP
    }

    if (!this.playerSprite || !this.playerBody) return

    this.handleMovementAndShooting(deltaTime, delta)

    if (bulletHandler) {
      bulletHandler.update()
    }
    bulletSystem(getGameWorld())

    if (this.collisionDetector) {
      this.collisionDetector.processAttractedCollectables(delta)
    }

    this.playerStateMachine!.update(this.playerMovement.getPlayerFacingDirection())
    this.startersManager.update()
  }

  private handleMovementAndShooting(deltaTime: number, delta: number): void {
    this.playerMovement.applyCustomAcceleration(deltaTime)

    if (getPlayerAttribute(Attribute.PlayerCollisionsEnabled, this.playerEntity)) {
      this.playerMovement.handleJumpingAndFalling(deltaTime)
    }
    this.playerMovement.updateTimeSinceLastScoreUpdate(delta)
    this.playerMovement.clampVerticalVelocity()
    this.playerMovement.checkLanding()

    this.playerMovement.updatePlayerYVelocity()
    this.playerMovement.handlePlayerXMovement(deltaTime)
    this.playerMovement.wrapPlayer()
    this.playerMovement.updateHighestYPosition()

    this.playerShootingHandler.update()
    this.playerShootingHandler.checkForCollision()
    this.playerMovement.syncPlayerVisualWithPhysicalBody()
  }

  private initializeCollisionDetector(scene: GameScene, interactables: Interactables): void {
    this.collisionDetector = new PlayerCollisionDetector(
      scene,
      interactables,
      this.onPlatformInteraction.bind(this),
      this.onBoosterInteraction.bind(this),
      this.onEnemyCollision.bind(this),
      this.onCollisionWithCollectable.bind(this)
    )
  }

  private getPlayerStartPosition(startPosition: Vector2 | undefined): Vector2 {
    const playerPos = new Vector2(0, 0)
    if (startPosition == undefined) {
      const screenHeight = this.scene.cameras.main.height
      playerPos.y = screenHeight - PlayerConsts.START_POSITION.y
    } else {
      playerPos.set(startPosition.x, startPosition.y)
    }
    playerPos.x = this.scene.cameras.main.displayWidth / 2
    return playerPos
  }

  private createPlayerSprite(playerPos: Vector2): void {
    this.playerSprite = this.scene.physics.add
      .sprite(playerPos.x, playerPos.y + PlayerConsts.SPRITE_OFFSET_Y, AtlasNames.ENV, 'uni8.png')
      .setDepth(DepthOrder.Player)
      .setOrigin(0.5, 0.5)
      .setVisible(true)
      .setDisplaySize(PlayerConsts.DISPLAY_SIZE.x, PlayerConsts.DISPLAY_SIZE.y)
      .setAlpha(0)

    this.playerSprite.name = 'player'
  }

  private setupPlayerBody(): void {
    const colliderWidth = this.playerSprite.width / PlayerConsts.COLLIDER_WIDTH
    const colliderHeight = this.playerSprite.height * PlayerConsts.COLLIDER_HEIGHT

    this.playerBody = this.playerSprite.body as Phaser.Physics.Arcade.Body
    this.playerBody
      .setGravityY(PlayerConsts.GRAVITY)
      .setSize(colliderWidth, colliderHeight)
      .setOffset(
        this.playerBody.offset.x - PlayerConsts.BODY_COLLIDER_OFFSET_X,
        this.playerBody.offset.y
      )
    this.playerBody.debugShowBody = true
  }

  private setupPlayerComponents(): void {
    initializeBulletHandler(
      this.scene,
      this.playerSprite,
      this.interactables,
      this.playerEntity,
      this.uiEventBus
    )
    this.playerShootingHandler = bulletHandler

    this.setupStarters()

    this.collisionDetector.setupCollision(
      this.playerSprite,
      this.startersManager && this.startersManager.magnetField
        ? this.startersManager.getMagnetField()
        : undefined,
      this.playerEntity
    )

    this.playerEnemyInteractions = new PlayerEnemyInteractions(
      this.scene,
      this,
      this.startersManager.bubbleManager!
    )
    this.playerSprite.setVisible(true)

    this.playerDeathHandler = new PlayerDeathHandler(
      this.scene,
      this.uiEventBus,
      this,
      this.playerEnemyInteractions
    )

    this.playerInteractions = new PlayerInteractions(this.scene, this, this.uiEventBus)

    this.playerMovement = new PlayerMovement(this.scene, this.uiEventBus, this)

    // Connect shooting handler with input handler for multi-touch support
    this.playerShootingHandler.setInputHandler(this.playerMovement.getInputHandler())

    this.initialScaleX = this.playerSprite.scaleX
    this.initialScaleY = this.playerSprite.scaleY
  }

  private setupStarters(): void {
    this.startersManager = new StartersManager(
      this.scene,
      this.playerSprite,
      this.playerEntity,
      bulletHandler
    )
    this.startersManager.setupStarters()
    this.startersManager.subscribeEvents()
  }

  private registerEventHandlers() {
    this.uiEventBus.on(GAME_EVENTS.PLAYER_RESSURECTED, this.onSceneFadeOut.bind(this))
    this.uiEventBus.on(
      GAME_EVENTS.PLAYER_BELOW_SCREEN_BOTTOM,
      this.onPlayerBelowScreenBottom.bind(this)
    )
    this.uiEventBus.on(GAME_EVENTS.PLAYER_DIED, this.onDeathAnimationPlayed.bind(this))

    this.scene.events.on('jumppad-animation-complete', () => {
      setPlayerAttribute(Attribute.IsBigJumpBoosterActive, this.playerEntity, false)
      setPlayerAttribute(Attribute.CanShoot, this.playerEntity, true)
    })

    this.scene.events.on('portal-opened', () => {
      this.onRevive()
    })

    this.scene.events.on('booster-animation-finished', () => {
      this.playerSkins!.triggerAdditionalAnimationForSkin()
      setPlayerAttribute(Attribute.IsBoosterActive, this.playerEntity, false)
      setPlayerAttribute(Attribute.CanShoot, this.playerEntity, true)

      setPlayerAttribute(Attribute.IsJumping, this.playerEntity, false)
    })
  }

  private onEnemyCollision(enemy: BaseEnemy): void {
    this.playerEnemyInteractions.onEnemyCollision(enemy)
  }

  private onCollisionWithCollectable(
    collectable: CollectableItemBase,
    pos: Vector2 | undefined
  ): void {
    this.playerInteractions.onCollisionWithCollectable(collectable, pos)
  }

  private onPlatformInteraction(platform: BasePlatform): void {
    this.playerInteractions.onPlatformInteraction(platform)
  }

  private onBoosterInteraction(booster: BaseBooster, collisionPos: Vector2 | undefined): void {
    this.playerInteractions.onBoosterInteraction(booster, collisionPos)
  }

  private onRevive() {
    this.playerMovement.resetVelocity()
    this.playerDeathHandler.revive()
  }

  private onDeathAnimationPlayed() {
    this.playerDeathHandler.playDeathAnimation()
  }

  private onSceneFadeOut() {
    this.playerDeathHandler.fadeOutScene()
  }

  changeSkin() {
    this.playerSkins!.moveNextSkin()
  }

  triggerSkinAnimation() {
    this.playerSkins!.triggerAdditionalAnimationForSkin()
  }

  enablePlayerCollision(isEnabled: boolean): void {
    if (!this.playerSprite || !this.playerSprite.body) {
      console.warn('Player sprite or body is missing, cannot update collision.')
      return
    }
    setPlayerAttribute(Attribute.PlayerCollisionsEnabled, this.playerEntity, isEnabled)
    this.playerSprite.body.checkCollision.none = !isEnabled
  }

  enablePlayer() {
    if (!this.playerSprite.body) {
      this.scene.physics.world.enable(this.playerSprite)
      this.setupPlayerBody()
    }
    this.playerSprite.body!.immovable = false
    this.playerSprite.setActive(true)
    this.playerSprite.setVisible(true)
    this.playerSprite.setVelocityX(0)
    this.playerSprite.setScale(this.initialScaleX, this.initialScaleY)
    this.playerSpine?.setScale(this.playerSprite.scale * PlayerConsts.SPINE_SCALE_MULTIPLIER)
    setPlayerAttribute(Attribute.StopBodyAndSpineSync, this.playerEntity, false)
    this.playerSpine.rotation = 0
  }

  jump(velocity: number): void {
    setPlayerAttribute(Attribute.IsJumping, this.playerEntity, true)
    if (getPlayerAttribute(Attribute.IsBoosterActive, this.playerEntity)) {
      this.playerSprite.refreshBody()
      return
    }

    if (!this.playerSprite) return

    this.playerSprite.setVelocityY(velocity)
    this.startersManager.playBootsAnimation()
  }

  onPlayerBelowScreenBottom() {
    this.showShield(false)
    this.showBootsEffect(false)
    this.enablePlayerCollision(false)
    this.turnOnMagnet(false)
    setPlayerAttribute(Attribute.IsDead, this.playerEntity, true)
    this.playerEnemyInteractions.unsubscribeEvents()
    setPlayerAttribute(Attribute.StopBodyAndSpineSync, this.playerEntity, true)
    this.playerSpine?.setVisible(false)
    this.playerSpine?.animationState.clearTracks()
  }

  freezePlayerInPlace() {
    this.playerSprite.setGravity(0)
  }

  fadeInScene(animatedDeath: boolean) {
    this.playerDeathHandler.fadeInScene(animatedDeath)
  }

  changeAnimationState(playerState: PlayerStates) {
    this.playerStateMachine!.updateStateBasedOnInput(playerState)
  }

  animatePlayerDeath() {
    this.playerEnemyInteractions.animatePlayersDeath()
  }

  showShield(showShield: boolean) {
    this.playerInteractions.showShield(showShield)
  }

  showBootsEffect(showBoots: boolean) {
    this.startersManager?.showBootsEffect(showBoots)
  }

  unsubscribe() {
    this.uiEventBus.offAll(GAME_EVENTS.PLAYER_RESSURECTED)
    this.uiEventBus.offAll(GAME_EVENTS.PLAYER_DIED)
    this.uiEventBus.offAll(GAME_EVENTS.PLAYER_BELOW_SCREEN_BOTTOM)
    this.playerEnemyInteractions.unsubscribeEvents()
    this.playerInteractions.clearEventSubscriptions()
    this.playerShootingHandler.clearBulletData()
    this.playerShootingHandler.removeListener()
    this.scene.events.off('portal-opened')
    this.scene.events.off('booster-animation-finished')
    this.scene.events.off('jumppad-animation-complete')
    this.scene.events.off('update')
    this.playerSpine?.destroy(true)
    this.startersManager?.unsubscribeEvents()

    removeEntity(getGameWorld(), this.playerEntity)
  }

  getNextTokenInfo(tokenType: EntityType): SpawnedTokenData | undefined {
    let spawnedTokenData: SpawnedTokenData | undefined = undefined
    for (let i = 0; i < this.interactables.Collectables.length; i++) {
      const collectable = this.interactables.Collectables[i]
      if (collectable.getType() != tokenType) {
        continue
      }
      const height = collectable.getSprite().y
      if (height >= this.playerMaxHeight) {
        continue
      } else {
        const tonHeight = Math.abs(height - this.playerMaxHeight)
        spawnedTokenData = {
          height: (tonHeight * PlayerConsts.POINTS_INDEX).toFixed(0),
          amount: collectable.getAmount()
        }
        break
      }
    }
    return spawnedTokenData
  }

  checkForTonLimitReached(): boolean {
    try {
      const tonLimitLeft: number =
        this.scene.getActiveSessionInfo()?.remainingBeginnerTonAllocation ?? 0
      console.log('ton limit left', tonLimitLeft)

      if (tonLimitLeft <= 0) {
        return false
      }
      console.log('ton score', this.tonScore)
      console.log('should show ton limit popup:', this.tonScore >= tonLimitLeft)
      return this.tonScore >= tonLimitLeft
    } catch (error: any) {
      console.error('Error in checkForTonLimitReached', error)
      return false
    }
  }

  getPlayerEntity(): number {
    return this.playerEntity
  }

  getPlayer(): Phaser.Physics.Arcade.Sprite {
    return this.playerSprite
  }

  getPlayerSpine(): SpineGameObject {
    return this.playerSpine
  }

  getPlayerStateMachine(): PlayerStateMachine {
    return this.playerStateMachine!
  }

  getCameraManager(): CameraManager {
    return this.cameraManager
  }

  getCachedTweens(): { [key: string]: Phaser.Tweens.Tween } {
    return this.cachedTweens
  }

  getPlayerBody(): Phaser.Physics.Arcade.Body {
    return this.playerBody
  }

  getPlayerVelocityY(): number {
    return this.playerMovement?.getPlayerVelocityY()
  }

  getCurrentScore(): number {
    return this.playerMovement?.currentScore ?? 0
  }

  getInitialSpineScale(): number {
    return this.spineScale
  }

  public getSelectedJumpVelocity(defaultVelocity: number): number {
    return this.startersManager?.getSelectedJumpVelocity(defaultVelocity)
  }

  public showBubble(show: boolean) {
    this.startersManager?.showBubble(show)
  }

  public reEnableBubble() {
    this.startersManager?.reEnableBubble()
  }

  public turnOnMagnet(status: boolean) {
    if (this.startersManager.magnetField) {
      this.startersManager?.magnetField?.turnOnMagnet(status)
    }
  }

  public checkForMagnetActivation() {
    if (this.startersManager.magnetField) {
      this.startersManager?.magnetField?.checkForMagnetActivation()
    }
  }
}
