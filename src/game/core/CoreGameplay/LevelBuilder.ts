import type { BoosterOptions } from '@/game/core/CoreGameplay/Boosters/BoosterOptions.ts'
import type { BoostersFactory } from '@/game/core/CoreGameplay/Boosters/BoostersFactory'
import type { CollectableOptions } from '@/game/core/CoreGameplay/Collectables/CollectableOptions.ts'
import type { CollectablesFactory } from '@/game/core/CoreGameplay/Collectables/CollectablesFactory'
import type { DecorationsBuilder } from '@/game/core/CoreGameplay/Decorations/DecorationsBuilder.ts'
import type { BaseEnemy } from '@/game/core/CoreGameplay/Enemies/BaseEnemy'
import type { EnemiesFactory } from '@/game/core/CoreGameplay/Enemies/EnemiesFactory'
import type { EnemyOptions } from '@/game/core/CoreGameplay/Enemies/EnemyOptions.ts'
import { Interactables } from '@/game/core/CoreGameplay/Interactables.ts'
import { BasePlatform } from '@/game/core/CoreGameplay/Platforms/BasePlatform.ts'
import type {
  BasePlatformOptions,
  ChunkInfo,
  SpriteConfig
} from '@/game/core/CoreGameplay/Platforms/BasePlatformOptions'
import type { CustomParameters } from '@/game/core/CoreGameplay/Platforms/CustomParameters'
import { PlatformFactory } from '@/game/core/CoreGameplay/Platforms/PlatformFactory'
import {
  ENTITIES_SIZES,
  EntityType,
  GenerationType,
  type Chunk
} from '@/game/core/MapGen/MapGenerator'
import type { GameScene } from '@/game/core/scenes/GameScene'
import { Vector2 } from '@esotericsoftware/spine-phaser'
import type { GameInterface } from './GameInterface'
import TutorialAnalyticsDataCollector from './Player/TutorialAnalyticsDataCollector'

export interface ChunkRange {
  start: number
  end: number
  generation: GenerationType
}

export class LevelBuilder {
  private readonly scene: GameScene
  private readonly platformFactory: PlatformFactory
  private readonly gameInterface: GameInterface
  private boostersFactory: BoostersFactory
  private mobFactory: EnemiesFactory
  private collectablesFactory: CollectablesFactory
  private decorationsBuilder: DecorationsBuilder
  private interactables: Interactables
  private lastChunkStartY: number = 0
  private lastChunkEndY: number = 0
  public lastChunkIndex: number = 0
  public currentPresetName: number = 0
  public firstPlatform: Vector2 | undefined = undefined
  private debugText!: Phaser.GameObjects.Text
  private chunksPositions: ChunkRange[] = []
  private tutorialAnalytics: TutorialAnalyticsDataCollector

  constructor(
    scene: GameScene,
    gameInterface: GameInterface,
    platformFactory: PlatformFactory,
    boostersFactory: BoostersFactory,
    mobFactory: EnemiesFactory,
    collectablesFactory: CollectablesFactory,
    decorationsBuilder: DecorationsBuilder
  ) {
    this.scene = scene
    this.gameInterface = gameInterface
    this.platformFactory = platformFactory
    this.boostersFactory = boostersFactory
    this.mobFactory = mobFactory
    this.collectablesFactory = collectablesFactory
    this.interactables = new Interactables(scene)
    this.decorationsBuilder = decorationsBuilder
    this.tutorialAnalytics = TutorialAnalyticsDataCollector.getInstance()

    this.lastChunkStartY = 0
    this.lastChunkEndY = 0
    if (__DEV__) {
      this.debugText = this.scene.add
        .text(window.screen.width / 2, -50, '', {
          font: '18px Arial',
          color: '#ffffff',
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          padding: { x: 10, y: 5 }
        })
        .setScrollFactor(0)
        .setDepth(999999)
    }
  }

  public async preparePool(): Promise<void> {
    await Promise.all([
      this.platformFactory.preloadPlatforms(),
      this.boostersFactory.preloadBoosters(this.interactables.BoostersGroup),
      this.mobFactory.preloadMobs(),
      this.collectablesFactory.preloadCollectables(this.interactables.CollectablesGroup)
    ])
    this.lastChunkEndY = 0
  }

  public clearAllPools() {
    this.mobFactory?.clearEnemyPools()
    this.platformFactory?.clearPlatformPools()
    this.collectablesFactory?.clearCollectablePools()
    this.boostersFactory?.clearBoosterPools()
    if (__DEV__) console.log('Level Builder', ' : pools cleared')
  }

  public generateContent(chunks: Chunk[]) {
    if (__DEV__) console.log('Level Builder', `${chunks.length} - generated chunks`)

    try {
      for (let i = 0; i < chunks.length; i++) {
        this.lastChunkStartY = this.lastChunkEndY

        const chunk = chunks[i]
        this.lastChunkIndex = chunk.index
        this.generatePlatforms(chunk)

        if (__DEV__) {
          console.log(
            `---[VALIDATION]--- generated chunk ${chunk.id} index ${chunk.index} length ${chunk.length}. Start position: ${this.lastChunkStartY}, end: ${this.lastChunkEndY}`
          )
        }
      }
    } catch (e) {
      console.error('LevelBuilder', 'generateContent', e)
    }
  }

  private generatePlatforms(chunk: Chunk): void {
    if (!this.interactables) {
      this.interactables = new Interactables(this.scene)
    }

    let startChunkPosition = 0
    let endChunkPosition = 0
    const chunkPlatforms: BasePlatform[] = []

    for (let i = 0; i < chunk.components.length; i++) {
      const platformData = chunk.components[i]
      const { type, x, y } = platformData

      const currentY = -(y + this.lastChunkEndY)
      const customParameters: CustomParameters = {}
      const { movement, visibility, explosive } = platformData
      if (movement) {
        customParameters.duration = movement.duration
        customParameters.distance = movement.range
      }
      if (visibility) {
        customParameters.isVisible = visibility.visible
      }
      if (explosive) {
        customParameters.isExplosiveAsDecoration = explosive.decoration
      }

      if (this.firstPlatform == undefined) {
        this.firstPlatform = new Vector2(x, currentY)
      }

      const spriteConfig: SpriteConfig = {
        position: { x, y: currentY },
        texture: type.toString()
      }

      if (platformData.index == 0) {
        startChunkPosition = currentY
      }
      if (platformData.index == chunk.components.length - 1 && chunk.index == this.lastChunkIndex) {
        endChunkPosition = startChunkPosition - chunk.length
      }
      const chunkInfo: ChunkInfo = {
        generation: chunk.generation,
        id: chunk.id,
        index: chunk.index,
        length: chunk.length,
        prevChunkCumulativeLenght: chunk.prevChunkCumulativeLenght,
        platformIndex: platformData.index
      }

      const platformOptions: BasePlatformOptions = {
        factory: this.platformFactory,
        entityType: type,
        platformGroup: this.interactables.PlatformsGroup,
        scene: this.scene,
        spriteConfig,
        customParams: customParameters,
        chunkInfo,
        gameInterface: this.gameInterface
      }

      const platform = this.platformFactory.createPlatform(platformOptions)
      this.interactables.addPlatform(platform)
      chunkPlatforms.push(platform)

      if (!platformData.clone && platformData.booster) {
        const boosterData = platformData.booster
        const boosterOptions: BoosterOptions = {
          boosterGroup: this.interactables.BoostersGroup,
          scene: this.scene,
          position: { x: boosterData.x, y: -(boosterData.y + this.lastChunkStartY) },
          entityType: boosterData.type,
          chunkInfo,
          gameInterface: this.gameInterface
        }

        const booster = this.boostersFactory.createBooster(boosterOptions)
        this.interactables.addBooster(booster)
        platform.addBooster(booster)
      }

      if (!platformData.clone && platformData.mob) {
        const mobData = platformData.mob
        const mobOptions: EnemyOptions = {
          mobGroup: this.interactables.MobsGroup,
          scene: this.scene,
          position: { x: mobData.x, y: -(mobData.y + this.lastChunkStartY) },
          entityType: mobData.type,
          chunkInfo,
          gameInterface: this.gameInterface,
          customParameters: {
            mobHalfScreenMovement: mobData.mobMovement?.moveHalfScreen || false
          }
        }

        const mob = this.mobFactory.createMob(mobOptions)
        this.interactables.addMob(mob)
        platform.addMob(mob)
      }

      if (platformData.ticket) {
        const ticketData = platformData.ticket
        const collectableOptions: CollectableOptions = {
          collectableGroup: this.interactables.CollectablesGroup,
          scene: this.scene,
          position: { x: ticketData.x, y: -(ticketData.y + this.lastChunkStartY) },
          entityType: ticketData.type,
          gameInterface: this.gameInterface,
          chunkInfo,
          amount: ticketData.collectible?.amount || 1
        }

        const ticket = this.collectablesFactory.createCollectable(collectableOptions)

        this.interactables.addCollectable(ticket)
        platform.addCollectable(ticket)
      }

      if (platformData.decoration) {
        const decorationData = platformData.decoration
        const decorationOptions: SpriteConfig = {
          position: { x: decorationData.x, y: -(decorationData.y + this.lastChunkStartY) },
          texture: decorationData.key
        }

        const decoration = this.decorationsBuilder.createDecoration(
          platformData.decoration.type,
          decorationOptions
        )
        platform.addDecoration(decoration)
      }

      if (platformData.token) {
        const tokenData = platformData.token
        const tokenPosition = { x: tokenData.x, y: -(tokenData.y + this.lastChunkStartY) }
        const amount =
          tokenData.type == EntityType.CollectibleTon
            ? (tokenData.collectible?.amount || 5) / 1000000000
            : tokenData.collectible?.amount
        const tokenOptions: CollectableOptions = {
          collectableGroup: this.interactables.CollectablesGroup,
          scene: this.scene,
          position: tokenPosition,
          entityType: tokenData.type,
          gameInterface: this.gameInterface,
          chunkInfo,
          amount: amount || 1
        }
        const token = this.collectablesFactory.createCollectable(tokenOptions)
        this.interactables.addCollectable(token)
        platform.addCollectable(token)
      }

      if (!this.tutorialAnalytics.getTutorailPassed()) {
        this.tutorialAnalytics.addGeneratedPreset(__GAME_CHUNK_NAMES__[platform.chunkInfo.id]!)
      }
    }

    chunkPlatforms.forEach(platform => {
      platform.setChunkPositions(startChunkPosition, endChunkPosition)
    })
    this.chunksPositions.push({
      start: startChunkPosition,
      end: endChunkPosition,
      generation: chunk.generation
    })
    this.lastChunkEndY += chunk.length
  }

  destroyOffscreenObjects(): void {
    const camera = this.scene.cameras.main
    const offset = -3
    const offscreenStartingPoint = camera.worldView.y + camera.worldView.height + offset
    this.destroyOffscreenPlatforms(offscreenStartingPoint)
  }

  private _offscreenMobs: BaseEnemy[] = []

  private destroyOffscreenPlatforms(destroyingPoint: number): void {
    this.decorationsBuilder.tryToDestroyDecoration(destroyingPoint)

    let i = this.interactables.Platforms.length

    while (i--) {
      const platform = this.interactables.Platforms[i]
      if (!platform || !platform.getSprite()) {
        this.interactables.Platforms.splice(i, 1)
        continue
      }

      const platformY = platform.getSprite().y
      if (platformY > destroyingPoint) {
        this.currentPresetName = platform.chunkInfo.id
        if (!this.tutorialAnalytics.getTutorailPassed()) {
          this.tutorialAnalytics.setLastPreset(this.currentPresetName)
        }
        if (__DEV__) {
          this.debugText.text = `Chunk index: ${
            platform.chunkInfo.index || 'unknown index'
          }, name: ${__GAME_CHUNK_NAMES__[platform.chunkInfo.id] || 'Unknown Chunk'}`
        }

        const booster = platform.getBooster()
        if (booster) {
          this.interactables.filterBoosters(b => b !== booster)
          this.boostersFactory.returnBoosterToPool(booster)
        }
        const mob = platform.getMob()
        if (mob) {
          this._offscreenMobs.push(mob)
        }

        const collectable = platform.getCollectable()
        if (collectable) {
          this.interactables.filterCollectables(c => c !== collectable)
          if (collectable.getSprite().getData('isBeingAttracted')) return
          this.collectablesFactory.returnCollectableToPool(collectable)
        }

        this.platformFactory.returnPlatformToPool(platform)
        this.interactables.Platforms.splice(i, 1)
      }
    }
  }

  async setupAfterReviving(lastPlayerPos: number): Promise<number> {
    let revivingPoint = lastPlayerPos
    for (let i = 0; i < this.chunksPositions.length; i++) {
      const chunk = this.chunksPositions[i]
      if (lastPlayerPos <= chunk.start && lastPlayerPos >= chunk.end) {
        if (
          chunk.generation === GenerationType.Procedural ||
          chunk.generation === GenerationType.Composition
        ) {
          revivingPoint = chunk.end
          this.destroyPlatformsLowerThanY(revivingPoint + 300)
          this.checkOffscreenMobs(revivingPoint)

          const revivingPlatformY = chunk.end
          await this.generateRevivingPlatfoms(revivingPlatformY - ENTITIES_SIZES['0'][0])
        } else {
          console.error('[ERROR] Invalid chunk generation type')
        }
      }
    }

    return revivingPoint
  }

  destroyPlatformsLowerThanY(y: number): void {
    this.destroyOffscreenPlatforms(y)
  }

  checkOffscreenMobs(offscreenStartingPoint: number): void {
    for (let i = this._offscreenMobs.length - 1; i >= 0; i--) {
      const mob = this._offscreenMobs[i]
      const mobSprite = mob.getSprite()
      const mobTopY = mobSprite.getBounds().top

      if (mobTopY > offscreenStartingPoint) {
        this.mobFactory.returnMobToPool(mob)
        this._offscreenMobs.splice(i, 1)
      }
    }
  }

  get Interactables() {
    return this.interactables
  }

  async generateRevivingPlatfoms(revivingPoint: number): Promise<void> {
    const centerX = this.scene.cameras.main.worldView.centerX

    const rangeY = ENTITIES_SIZES['1'][0] / 2
    this.interactables.Platforms.forEach(platform => {
      if (
        platform.getSprite().y > revivingPoint - rangeY &&
        platform.getSprite().y <= revivingPoint + rangeY
      ) {
        const booster = platform.getBooster()
        if (booster) {
          this.boostersFactory.returnBoosterToPool(booster)
        }
        platform.returnToPool()
      }
    })

    for (let i = 0; i < 2; i++) {
      let posX = centerX
      if (i === 1) {
        posX -= 150
      }

      const platform = this.platformFactory.createRevivingPlatform(
        { x: posX, y: revivingPoint },
        this.interactables.PlatformsGroup
      )

      this.interactables.addPlatform(platform)
      platform.dontSendPlatformInteraction()

      await new Promise(resolve => setTimeout(resolve, 50))
    }
  }
}
