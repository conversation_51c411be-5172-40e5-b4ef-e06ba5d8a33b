import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import { claimSkinMutation } from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation } from '@tanstack/vue-query'

export function useClaimSkin() {
  const { mutateAsync, isPending } = useMutation({
    ...claimSkinMutation(),
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {}
  })

  const claimSkin = (id: number) => {
    return mutateAsync({
      body: {
        skinId: id
      }
    }).then(data => {
      return data.ok
    })
  }

  return { claimSkin, isPending }
}
