import { useDefaultError<PERSON>and<PERSON>, usePurchaseErrorHandling } from '@/composables/useErrorHandling'
import {
  getPlayerStateQueryKey,
  getShopItemsQueryKey,
  purchaseMutation,
  purchaseProgressiveMutation,
  purchaseRansomMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import type { InGamePrice } from '@/types'
import { useMutation, useQueryClient } from '@tanstack/vue-query'
import type { GetShopItemsResponse, PlayerStateResponse } from '../openapi'
import { updatePlayerStateDataProp } from './utils'

export function usePurchase() {
  const queryClient = useQueryClient()
  let priceToSpend: InGamePrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchaseMutation(),
    onSuccess: () => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData || !priceToSpend) return oldData
        const currency = priceToSpend.currency
        const amount = priceToSpend.amount
        const newData: PlayerStateResponse = {
          ...oldData,
          [currency]: updatePlayerStateDataProp(oldData[currency], -amount)
        }
        return newData
      })
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
    }
  })

  const purchase = (itemId: number, price: InGamePrice) => {
    priceToSpend = price
    return mutateAsync({
      body: { itemId, currency: price.currency }
    })
  }

  return { purchase, isPending }
}

export function usePurchaseProgressive() {
  const queryClient = useQueryClient()
  let priceToSpend: InGamePrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchaseProgressiveMutation(),
    onSuccess: () => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData || !priceToSpend) return oldData
        const currency = priceToSpend.currency
        const amount = priceToSpend.amount
        console.log('onSuccess', priceToSpend)
        const newData: PlayerStateResponse = {
          ...oldData,
          [currency]: updatePlayerStateDataProp(oldData[currency], -amount)
        }
        return newData
      })
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
    }
  })

  const purchase = (offerId: number, price: InGamePrice | null = null) => {
    priceToSpend = price
    return mutateAsync({
      body: { offerId }
    })
  }

  // This is not in onSuccess callback because of purchasing with TG invoice
  const onProgressivePurchased = (offerId: number) => {
    queryClient.setQueryData(getShopItemsQueryKey(), (oldData: GetShopItemsResponse) => {
      const newData = { ...oldData }
      if (!newData.progressiveOffers) return oldData
      const offer = newData.progressiveOffers.find(offer => offer.id === offerId)
      if (!offer) return oldData
      offer.items.sort((a, b) => a.idx - b.idx)
      const offerIndex = offer.items.findIndex(offer => offer.isAvailable)
      if (offerIndex < 0) return
      offer.items[offerIndex].isPurchased = true
      offer.items[offerIndex].isAvailable = false

      const hasNoMoreOffers = offer.items.every(offer => offer.isPurchased)
      if (hasNoMoreOffers) {
        queryClient.invalidateQueries({ queryKey: getShopItemsQueryKey() }).then(() => {
          const data = queryClient.getQueryData<GetShopItemsResponse>(getShopItemsQueryKey())
          if (
            !data?.progressiveOffers ||
            data.progressiveOffers.find(offer => offer.id === offerId)?.isCompleted
          ) {
            queryClient.setQueryData(
              getPlayerStateQueryKey(),
              (oldPlayerStateData: PlayerStateResponse) => {
                if (!oldPlayerStateData) return oldPlayerStateData
                const newPlayerStateData: PlayerStateResponse = {
                  ...oldPlayerStateData,
                  progressiveOffers: oldPlayerStateData.progressiveOffers.map(offer =>
                    offer.id === offerId ? { ...offer, isCompleted: true } : offer
                  )
                }
                return newPlayerStateData
              }
            )
          }
        })
      } else {
        offer.items[offerIndex + 1].isAvailable = true
      }

      return newData
    })
  }

  return { purchase, isPending, onProgressivePurchased }
}

export function usePurchaseRansom() {
  const queryClient = useQueryClient()
  let priceToSpend: InGamePrice | null = null

  const { mutateAsync, isPending } = useMutation({
    ...purchaseRansomMutation(),
    onSuccess: () => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData || !priceToSpend) return oldData
        const currency = priceToSpend.currency
        const amount = priceToSpend.amount
        const newData: PlayerStateResponse = {
          ...oldData,
          [currency]: updatePlayerStateDataProp(oldData[currency], -amount)
        }
        return newData
      })
    },
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
      usePurchaseErrorHandling(error.error)
    }
  })

  const purchaseRansom = (price: InGamePrice) => {
    priceToSpend = price
    return mutateAsync({})
  }

  return { purchaseRansom, isPending }
}
