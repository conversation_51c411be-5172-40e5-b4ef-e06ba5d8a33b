import {
  useDefaultError<PERSON><PERSON><PERSON>,
  useStartClanEventErrorHandling
} from '@/composables/useErrorHandling'
import { usersInClanRating } from '@/services/openapi'
import {
  clansRatingOptions,
  getPlayerStateQueryKey,
  startClanEventMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { computed, type MaybeRefOrGetter, toValue } from 'vue'

export function useClansList() {
  const { data, isLoading } = useQuery({
    ...clansRatingOptions()
  })

  const clans = computed(() => data.value?.list ?? [])

  return {
    isLoading,
    clans
  }
}

export function useClanInfo(
  clanId: MaybeRefOrGetter<number>,
  isOpen: MaybeRefOrGetter<boolean> = true
) {
  const { data, isLoading } = useQuery({
    queryFn: async ({ queryKey }) => {
      const { data } = await usersInClanRating({
        path: { clan_id: toValue(queryKey[1]) },
        throwOnError: true
      })
      return data
    },
    queryKey: ['usersInClanRating', clanId] as const,
    enabled: isOpen
  })

  return {
    isLoading,
    clanInfo: data
  }
}

export function useStartClanEvent() {
  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    ...startClanEventMutation(),
    onError: (error: any) => {
      useDefaultErrorHandler(error.error)
      useStartClanEventErrorHandling(error.error)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
    }
  })

  const startClanEvent = () => {
    return mutateAsync({})
  }

  return {
    isPending,
    startClanEvent
  }
}
