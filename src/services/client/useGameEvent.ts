import {
  getClanEventLeadersOptions,
  getClanEventUserInfoOptions,
  getCustomCoinEventUserInfoOptions,
  getCustomCoinsLeadersOptions,
  getHotrecordLeadersOptions,
  getHotrecordUserInfoOptions,
  getOnepercentLeadersOptions,
  getOnepercentUserInfoOptions
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useQuery } from '@tanstack/vue-query'
import { computed, watch } from 'vue'

export function useOnepercentLeaderboard(onError?: Function) {
  const { data, isLoading, error } = useQuery({
    ...getOnepercentLeadersOptions(),
    retry: false
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  watch(error, () => {
    if (error.value && onError) {
      onError(error.value)
    }
  })

  return {
    isLoading,
    leaderboard
  }
}

export function useOnepercentUserInfo() {
  const { data, isLoading, isFetching } = useQuery({
    ...getOnepercentUserInfoOptions()
  })

  return {
    isLoading,
    isFetching,
    userInfo: data
  }
}

export function useHotrecordLeaderboard(onError?: Function) {
  const { data, isLoading, error } = useQuery({
    ...getHotrecordLeadersOptions(),
    retry: false
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  watch(error, () => {
    if (error.value && onError) {
      onError(error.value)
    }
  })

  return {
    isLoading,
    leaderboard
  }
}

export function useHotrecordUserInfo() {
  const { data, isLoading, isFetching } = useQuery({
    ...getHotrecordUserInfoOptions()
  })

  return {
    isLoading,
    isFetching,
    userInfo: data
  }
}

export function useCustomCoinLeaderboard(onError?: Function) {
  const { data, isLoading, error } = useQuery({
    ...getCustomCoinsLeadersOptions(),
    retry: false
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  watch(error, () => {
    if (error.value && onError) {
      onError(error.value)
    }
  })

  return {
    isLoading,
    leaderboard
  }
}

export function useCustomCoinUserInfo(enabled = true) {
  const { data, isLoading, isFetching, refetch } = useQuery({
    ...getCustomCoinEventUserInfoOptions(),
    enabled
  })

  return {
    isLoading,
    isFetching,
    userInfo: data,
    refetchUserInfo: refetch
  }
}

export function useClanEventLeaders(onError?: Function) {
  const { data, isLoading, error } = useQuery({
    ...getClanEventLeadersOptions(),
    retry: false
  })

  const leaderboard = computed(() => {
    return data.value?.list ?? []
  })

  watch(error, () => {
    if (error.value && onError) {
      onError(error.value)
    }
  })

  return {
    isLoading,
    leaderboard
  }
}

export function useUserClanEventInfo() {
  const { data, isLoading, isFetching } = useQuery({
    ...getClanEventUserInfoOptions()
  })

  return {
    isLoading,
    isFetching,
    userInfo: data
  }
}
