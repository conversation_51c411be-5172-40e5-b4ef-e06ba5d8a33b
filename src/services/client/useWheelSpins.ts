import { useDefaultErrorHandler } from '@/composables/useErrorHandling'
import type { PlayerStateResponse } from '@/services/openapi'
import {
  freeSpinWheelMutation,
  getFortuneWheelConfigOptions,
  getPlayerStateQueryKey,
  spinWheelMutation
} from '@/services/openapi/@tanstack/vue-query.gen'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'
import { computed } from 'vue'

export function useWheelSpinConfig() {
  const { data, isLoading } = useQuery({
    ...getFortuneWheelConfigOptions()
  })

  const sectors = computed(() => data.value?.sectors ?? [])

  return {
    sectors,
    isLoading
  }
}

export function useGetFreeWheelSpin() {
  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    ...freeSpinWheelMutation(),
    retry: 3,
    retryDelay: 1000,
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
    }
  })

  const getFreeWheelSpin = async () => {
    return mutateAsync({
      body: {}
    }).then(data => {
      return data
    })
  }

  return { getFreeWheelSpin, isPending }
}

export function useGetWheelSpin() {
  const queryClient = useQueryClient()

  const { mutateAsync, isPending } = useMutation({
    ...spinWheelMutation(),
    retry: 3,
    retryDelay: 1000,
    onError: (error: { error: string }) => {
      useDefaultErrorHandler(error.error)
    },
    onSuccess: () => {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData

        const newData = {
          ...oldData,
          wheelSpins: {
            ...oldData.wheelSpins,
            amount: oldData.wheelSpins?.amount ? oldData.wheelSpins?.amount - 1 : 0
          }
        }
        return newData
      })
    }
  })

  const getWheelSpin = async () => {
    return mutateAsync({
      body: {}
    }).then(data => {
      return data
    })
  }

  return { getWheelSpin, isPending }
}
