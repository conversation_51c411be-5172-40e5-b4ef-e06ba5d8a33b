import {
  getPlayerStateOptions,
  getPlayerStateQueryKey
} from '@/services/openapi/@tanstack/vue-query.gen'
import { isObjectRewardType, isStackableRewardType, type NumeralRewardType } from '@/types'
import { useQuery, useQueryClient } from '@tanstack/vue-query'
import { type Ref } from 'vue'
import type { BoostersView, PlayerStateResponse } from '../openapi'
import { updatePlayerStateDataProp } from './utils'

export function usePlayerState(enabled: Ref<boolean> | undefined = undefined) {
  const { data, isLoading, isRefetching, refetch } = useQuery({
    ...getPlayerStateOptions(),
    staleTime: 1000 * 60 * 5,
    enabled: enabled !== undefined ? enabled : true
  })

  return {
    isLoading,
    isRefetching,
    playerState: data,
    refetchPlayerState: refetch
  }
}

export function addToPlayerState() {
  const queryClient = useQueryClient()

  const updatePlayerState = (key: NumeralRewardType, value: number) => {
    if (key === 'refsFake') {
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: getPlayerStateQueryKey() })
      }, 1000)
    } else if (isStackableRewardType(key)) {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          boostersView: {
            ...(oldData.boostersView ?? ({} as BoostersView)),
            [key]: updatePlayerStateDataProp(oldData.boostersView?.[key], value)
          }
        }
        return newData
      })
    } else if (isObjectRewardType(key)) {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          [key]: {
            ...(oldData[key] ?? ({} as any)),
            amount: updatePlayerStateDataProp(oldData[key]?.amount, value)
          }
        }
        return newData
      })
    } else {
      queryClient.setQueryData(getPlayerStateQueryKey(), (oldData: PlayerStateResponse) => {
        if (!oldData) return oldData
        const newData: PlayerStateResponse = {
          ...oldData,
          [key]: updatePlayerStateDataProp(oldData[key], value)
        }
        return newData
      })
    }
  }

  return { updatePlayerState }
}

// export function addStackableBoosterToPlayerState() {
//   const queryClient = useQueryClient()

//   return
// }
