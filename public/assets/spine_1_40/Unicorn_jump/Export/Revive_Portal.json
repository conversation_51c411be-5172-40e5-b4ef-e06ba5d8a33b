{"skeleton": {"hash": "n8S8KEOLQ68", "spine": "4.2.38", "x": -790, "y": -268.92, "width": 1580, "height": 4019, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "cntr", "parent": "root"}, {"name": "Portal_scale", "parent": "cntr", "scaleY": 0.3}, {"name": "1", "parent": "Portal_scale", "y": 32.25}, {"name": "2", "parent": "Portal_scale", "y": 32.25}, {"name": "4", "parent": "Portal_scale", "y": 32.25}, {"name": "3", "parent": "Portal_scale", "rotation": -65.42, "y": 32.25}, {"name": "5", "parent": "Portal_scale", "rotation": -49.69, "y": 32.25}, {"name": "Stars", "parent": "cntr"}, {"name": "Star", "parent": "Stars"}, {"name": "Star_inner", "parent": "Star"}, {"name": "Star2", "parent": "Stars"}, {"name": "Star_inner2", "parent": "Star2"}, {"name": "Star3", "parent": "Stars"}, {"name": "Star_inner3", "parent": "Star3"}, {"name": "Star4", "parent": "Stars"}, {"name": "Star_inner4", "parent": "Star4"}, {"name": "Star5", "parent": "Stars"}, {"name": "Star_inner5", "parent": "Star5"}, {"name": "Star6", "parent": "Stars"}, {"name": "Star_inner6", "parent": "Star6"}, {"name": "Star7", "parent": "Stars"}, {"name": "Star_inner7", "parent": "Star7"}, {"name": "Star8", "parent": "Stars"}, {"name": "Star_inner8", "parent": "Star8"}, {"name": "Lines", "parent": "cntr"}, {"name": "Line", "parent": "Lines", "x": -8.35, "y": 187.81}, {"name": "Line2", "parent": "Lines", "x": 326.68, "y": 131.55, "scaleX": 0.7246, "scaleY": 0.7246}, {"name": "Line3", "parent": "Lines", "x": -356.16, "y": -103.74}, {"name": "Line4", "parent": "Lines", "x": -535.19, "y": -1.44, "scaleX": 0.5935, "scaleY": 0.5935}, {"name": "Line5", "parent": "Lines", "x": 554.3, "y": 11.35, "scaleX": 0.4869, "scaleY": 0.4869}, {"name": "Line6", "parent": "Lines", "x": -279.44, "y": -129.31, "scaleX": 0.5662, "scaleY": 0.5662}, {"name": "Line7", "parent": "Lines", "x": 272.98, "y": -142.1, "scaleX": 1.1697, "scaleY": 1.1697}, {"name": "Line8", "parent": "Lines", "x": -67.17, "y": -157.44, "scaleX": 0.982, "scaleY": 0.982}, {"name": "Line9", "parent": "Lines", "x": -432.89, "y": 49.71, "scaleX": 0.4095, "scaleY": 0.4095}, {"name": "Line10", "parent": "Lines", "x": 469.9, "y": 70.17, "scaleX": 0.7246, "scaleY": 0.7246}, {"name": "Black_puddle", "parent": "cntr", "y": 10, "icon": "eye"}, {"name": "<PERSON>_<PERSON>uddle_Edge", "parent": "Black_puddle", "y": 43.33, "icon": "arrowUp"}, {"name": "Black_sphere", "parent": "Black_puddle", "y": 9.5, "icon": "circle"}, {"name": "Black_droplet", "parent": "Black_sphere", "scaleY": 0.3}, {"name": "Star_start", "parent": "root"}, {"name": "Puddle_ripple", "parent": "cntr", "y": 9.67, "scaleX": 0.2269, "scaleY": 0.0681}, {"name": "Rainbow_portal", "parent": "cntr"}, {"name": "Base_circles", "parent": "Rainbow_portal", "y": -93.53, "icon": "straightLine"}, {"name": "Rainbow_BG", "parent": "Rainbow_portal", "y": 37.66}, {"name": "Rainbow", "parent": "Rainbow_portal", "y": -42.88}, {"name": "Circle", "parent": "Rainbow_portal", "y": 718.28}, {"name": "Circle2", "parent": "Rainbow_portal", "y": 1072.95}, {"name": "Circle3", "parent": "Rainbow_portal", "y": 1457.7}, {"name": "Circle4", "parent": "Rainbow_portal", "y": 1877.28}], "slots": [{"name": "Glow", "bone": "Portal_scale", "attachment": "Images/Revive_portal/Glow"}, {"name": "Portal_bg", "bone": "Portal_scale", "attachment": "Images/Revive_portal/Portal_bg"}, {"name": "1", "bone": "1", "attachment": "Images/Revive_portal/1"}, {"name": "3", "bone": "3", "attachment": "Images/Revive_portal/1"}, {"name": "2", "bone": "2", "attachment": "Images/Revive_portal/2"}, {"name": "5", "bone": "5", "attachment": "Images/Revive_portal/2"}, {"name": "4", "bone": "4", "attachment": "Images/Revive_portal/4"}, {"name": "Line9", "bone": "Line9", "attachment": "Images/Revive_portal/Line"}, {"name": "Line", "bone": "Line", "attachment": "Images/Revive_portal/Line"}, {"name": "Line2", "bone": "Line2", "attachment": "Images/Revive_portal/Line"}, {"name": "Line10", "bone": "Line10", "attachment": "Images/Revive_portal/Line"}, {"name": "Puddle_ripple", "bone": "Puddle_ripple", "attachment": "Images/Revive_portal/4"}, {"name": "Black_puddle", "bone": "Black_puddle", "attachment": "Images/Revive_portal/Black_puddle"}, {"name": "Black_droplet", "bone": "Black_droplet", "attachment": "Images/Revive_portal/Black_droplet"}, {"name": "Black_sphere", "bone": "Black_sphere", "attachment": "Images/Revive_portal/Black_sphere"}, {"name": "Rainbow_BG", "bone": "Rainbow_BG", "attachment": "Images/Revive_portal/bg_light"}, {"name": "Rainbow", "bone": "Rainbow", "attachment": "Images/Revive_portal/Rainbow"}, {"name": "Base_circles", "bone": "Base_circles", "attachment": "Images/Revive_portal/Base_circles"}, {"name": "circle1", "bone": "Circle", "attachment": "Images/Revive_portal/circle1"}, {"name": "circle2", "bone": "Circle2", "attachment": "Images/Revive_portal/circle3"}, {"name": "circle3", "bone": "Circle3", "attachment": "Images/Revive_portal/circle1"}, {"name": "circle4", "bone": "Circle4", "attachment": "Images/Revive_portal/circle1"}, {"name": "Star", "bone": "Star_inner", "attachment": "Images/Revive_portal/Star"}, {"name": "Star6", "bone": "Star_inner6", "attachment": "Images/Revive_portal/Star"}, {"name": "Star8", "bone": "Star_inner8", "attachment": "Images/Revive_portal/Star"}, {"name": "Star5", "bone": "Star_inner5", "attachment": "Images/Revive_portal/Star"}, {"name": "Star4", "bone": "Star_inner4", "attachment": "Images/Revive_portal/Star"}, {"name": "Star3", "bone": "Star_inner3", "attachment": "Images/Revive_portal/Star"}, {"name": "Star7", "bone": "Star_inner7", "attachment": "Images/Revive_portal/Star"}, {"name": "Star2", "bone": "Star_inner2", "attachment": "Images/Revive_portal/Star"}, {"name": "Line3", "bone": "Line3", "attachment": "Images/Revive_portal/Line"}, {"name": "Line8", "bone": "Line8", "attachment": "Images/Revive_portal/Line"}, {"name": "Line7", "bone": "Line7", "attachment": "Images/Revive_portal/Line"}, {"name": "Line6", "bone": "Line6", "attachment": "Images/Revive_portal/Line"}, {"name": "Line5", "bone": "Line5", "attachment": "Images/Revive_portal/Line"}, {"name": "Line4", "bone": "Line4", "attachment": "Images/Revive_portal/Line"}, {"name": "Star_start", "bone": "Star_start", "color": "130318ff", "attachment": "Images/Revive_portal/Star"}], "skins": [{"name": "default", "attachments": {"1": {"Images/Revive_portal/1": {"y": -13.82, "width": 1139, "height": 1139}}, "2": {"Images/Revive_portal/2": {"width": 874, "height": 874}}, "3": {"Images/Revive_portal/1": {"y": -13.82, "width": 1139, "height": 1139}}, "4": {"Images/Revive_portal/4": {"width": 1288, "height": 1288}}, "5": {"Images/Revive_portal/2": {"width": 874, "height": 874}}, "Base_circles": {"Images/Revive_portal/Base_circles": {"y": 324.83, "width": 819, "height": 859}}, "Black_droplet": {"Images/Revive_portal/Black_droplet": {"x": -1.24, "y": -35.89, "rotation": -179.32, "width": 58, "height": 130}}, "Black_puddle": {"Images/Revive_portal/Black_puddle": {"type": "mesh", "uvs": [0.4208, 0, 0.49364, 0, 0.56649, 0, 0.63933, 0, 0.72793, 0.03655, 0.80915, 0.09096, 0.89442, 0.16943, 0.96933, 0.30094, 0.99993, 0.42382, 1, 0.43961, 1, 0.60801, 0.94407, 0.754, 0.82164, 0.91754, 0.62966, 1, 0.36356, 0.99994, 0.17474, 0.91135, 0.03853, 0.73263, 0, 0.57703, 7e-05, 0.41279, 0.05563, 0.24478, 0.14453, 0.12725, 0.25418, 0.04831, 0.34796, 0], "triangles": [1, 2, 13, 14, 0, 1, 9, 7, 8, 19, 17, 18, 16, 17, 19, 11, 6, 7, 9, 11, 7, 10, 11, 9, 15, 20, 21, 16, 19, 20, 15, 16, 20, 12, 5, 6, 12, 6, 11, 14, 22, 0, 21, 22, 14, 15, 21, 14, 13, 2, 3, 13, 3, 4, 12, 13, 4, 12, 4, 5, 13, 14, 1], "vertices": [2, 36, -27.8, 43.5, 0.28277, 37, -27.8, 0.17, 0.71723, 2, 36, -2.23, 43.5, 0.00022, 37, -2.23, 0.17, 0.99978, 2, 36, 23.34, 43.5, 0.24644, 37, 23.34, 0.17, 0.75356, 2, 36, 48.9, 43.5, 0.49716, 37, 48.9, 0.17, 0.50284, 2, 36, 80, 40.32, 0.6746, 37, 80, -3.01, 0.3254, 2, 36, 108.51, 35.59, 0.77317, 37, 108.51, -7.74, 0.22683, 2, 36, 138.44, 28.76, 0.83514, 37, 138.44, -14.57, 0.16486, 2, 36, 164.74, 17.32, 0.87889, 37, 164.74, -26.01, 0.12111, 2, 36, 175.48, 6.63, 0.90457, 37, 175.48, -36.7, 0.09543, 2, 36, 175.5, 5.25, 0.95714, 37, 175.5, -38.07, 0.04286, 1, 36, 175.5, -9.4, 1, 1, 36, 155.87, -22.1, 1, 1, 36, 112.9, -36.33, 1, 1, 36, 45.51, -43.5, 1, 1, 36, -47.89, -43.49, 1, 1, 36, -114.17, -35.79, 1, 1, 36, -161.97, -20.24, 1, 1, 36, -175.5, -6.7, 1, 2, 36, -175.48, 7.59, 0.9278, 37, -175.48, -35.74, 0.0722, 2, 36, -155.98, 22.2, 0.91312, 37, -155.98, -21.12, 0.08688, 2, 36, -124.77, 32.43, 0.85571, 37, -124.77, -10.9, 0.14429, 2, 36, -86.28, 39.3, 0.72184, 37, -86.28, -4.03, 0.27816, 2, 36, -53.37, 43.5, 0.52589, 37, -53.37, 0.17, 0.47411], "hull": 23, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 28, 30, 30, 32, 32, 34, 38, 40, 26, 28, 40, 42, 42, 44, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 44, 34, 36, 36, 38], "width": 351, "height": 87}}, "Black_sphere": {"Images/Revive_portal/Black_sphere": {"width": 56, "height": 56}}, "circle1": {"Images/Revive_portal/circle1": {"width": 671, "height": 505}, "Images/Revive_portal/circle2": {"width": 671, "height": 505}, "Images/Revive_portal/circle3": {"width": 671, "height": 505}}, "circle2": {"Images/Revive_portal/circle1": {"width": 671, "height": 505}, "Images/Revive_portal/circle2": {"width": 671, "height": 505}, "Images/Revive_portal/circle3": {"width": 671, "height": 505}}, "circle3": {"Images/Revive_portal/circle1": {"width": 671, "height": 505}, "Images/Revive_portal/circle2": {"width": 671, "height": 505}, "Images/Revive_portal/circle3": {"width": 671, "height": 505}}, "circle4": {"Images/Revive_portal/circle1": {"width": 671, "height": 505}, "Images/Revive_portal/circle2": {"width": 671, "height": 505}, "Images/Revive_portal/circle3": {"width": 671, "height": 505}}, "Glow": {"Images/Revive_portal/Glow": {"y": 653.87, "scaleY": 3.5, "width": 1580, "height": 568}}, "Line": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Line2": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Line3": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Line4": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Line5": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Line6": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Line7": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Line8": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Line9": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Line10": {"Images/Revive_portal/Line": {"x": 20.87, "y": 473.61, "width": 147, "height": 1080}}, "Portal_bg": {"Images/Revive_portal/Portal_bg": {"width": 1326, "height": 1326}}, "Puddle_ripple": {"Images/Revive_portal/4": {"width": 1288, "height": 1288}}, "Rainbow": {"Images/Revive_portal/Rainbow": {"y": 1783.47, "width": 1203, "height": 4019}}, "Rainbow_BG": {"Images/Revive_portal/bg_light": {"y": 109.35, "width": 1580, "height": 568}}, "Star": {"Images/Revive_portal/Star": {"width": 60, "height": 79}}, "Star2": {"Images/Revive_portal/Star": {"width": 60, "height": 79}}, "Star3": {"Images/Revive_portal/Star": {"width": 60, "height": 79}}, "Star4": {"Images/Revive_portal/Star": {"width": 60, "height": 79}}, "Star5": {"Images/Revive_portal/Star": {"width": 60, "height": 79}}, "Star6": {"Images/Revive_portal/Star": {"width": 60, "height": 79}}, "Star7": {"Images/Revive_portal/Star": {"width": 60, "height": 79}}, "Star8": {"Images/Revive_portal/Star": {"width": 60, "height": 79}}, "Star_start": {"Images/Revive_portal/Star": {"width": 60, "height": 79}}}}], "animations": {"t0_iDLE": {"bones": {"1": {"rotate": [{}, {"time": 4, "value": 360}]}}}, "t1_iDLE": {"bones": {"2": {"rotate": [{}, {"time": 5, "value": 360}]}, "5": {"rotate": [{}, {"time": 5, "value": 360}]}}}, "t2_iDLE": {"bones": {"3": {"rotate": [{}, {"time": 6.6667, "value": 360}]}}}, "t3_IDLE_Stars": {"bones": {"Star_inner": {"scale": [{"x": 1.5, "y": 1.5, "curve": [0.444, 1.5, 0.889, 0.7, 0.444, 1.5, 0.889, 0.7]}, {"time": 1.3333, "x": 0.7, "y": 0.7, "curve": [1.667, 0.7, 2, 1.5, 1.667, 0.7, 2, 1.5]}, {"time": 2.3333, "x": 1.5, "y": 1.5}]}, "Star2": {"translatex": [{"value": -71.04, "curve": [0.179, 155.64, 0.356, 409.02]}, {"time": 0.5333, "value": 409.02, "curve": "stepped"}, {"time": 0.5667, "value": 409.02, "curve": [1.011, 409.02, 1.456, -423.63]}, {"time": 1.9, "value": -423.63, "curve": [2.045, -423.63, 2.19, -256.51]}, {"time": 2.3333, "value": -71.04}], "translatey": [{"value": 413.02, "curve": [0.257, 576.41, 0.448, 731.73]}, {"time": 0.5333, "value": 783.59, "curve": "stepped"}, {"time": 0.5667, "value": 13.55, "curve": [1.132, -269.87, 1.848, 91.09]}, {"time": 2.3333, "value": 413.02}], "scale": [{"x": 0.636, "y": 0.636, "curve": [0.22, 0.424, 0.392, 0.166, 0.22, 0.424, 0.392, 0.166]}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": [0.713, 0, 0.9, 1, 0.713, 0, 0.9, 1]}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.5667, "curve": [1.874, 1, 2.126, 0.839, 1.874, 1, 2.126, 0.839]}, {"time": 2.3333, "x": 0.636, "y": 0.636}]}, "Star_inner2": {"scale": [{"x": 1.045, "y": 1.045, "curve": [0.036, 1.03, 0.068, 1.013, 0.036, 1.03, 0.068, 1.013]}, {"time": 0.1, "curve": "stepped"}, {"time": 0.5667, "curve": [0.789, 1.167, 1.011, 1.5, 0.789, 1.167, 1.011, 1.5]}, {"time": 1.2333, "x": 1.5, "y": 1.5, "curve": [1.601, 1.5, 1.969, 1.22, 1.601, 1.5, 1.969, 1.22]}, {"time": 2.3333, "x": 1.045, "y": 1.045}]}, "Star3": {"translatex": [{"value": -382.59, "curve": [0.39, -231.66, 0.779, 361.82]}, {"time": 1.1667, "value": 361.82, "curve": [1.322, 361.82, 1.478, 171.51]}, {"time": 1.6333, "value": 85.56, "curve": "stepped"}, {"time": 1.6667, "value": -183.64, "curve": [1.833, -262.19, 2, -419.3]}, {"time": 2.1667, "value": -419.3, "curve": [2.223, -419.3, 2.279, -404.48]}, {"time": 2.3333, "value": -382.59}], "translatey": [{"value": 93.38, "curve": [0.536, 260.57, 1.206, 497.87]}, {"time": 1.6333, "value": 758.54, "curve": "stepped"}, {"time": 1.6667, "value": -99.14, "curve": [1.819, -99.14, 2.058, 7.24]}, {"time": 2.3333, "value": 93.38}], "scale": [{"curve": "stepped"}, {"time": 0.3333, "curve": [0.961, 1, 1.356, 0.326, 0.961, 1, 1.356, 0.326]}, {"time": 1.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": [1.813, 0, 2, 1, 1.813, 0, 2, 1]}, {"time": 2.1667, "curve": "stepped"}, {"time": 2.3333}]}, "Star_inner3": {"scale": [{"x": 0.932, "y": 0.932, "curve": [0.168, 0.8, 0.334, 0.7, 0.168, 0.8, 0.334, 0.7]}, {"time": 0.5, "x": 0.7, "y": 0.7, "curve": [0.889, 0.7, 1.655, 1.508, 0.889, 0.7, 1.655, 1.508]}, {"time": 1.6667, "x": 1.5, "y": 1.5, "curve": [1.89, 1.347, 2.113, 1.109, 1.89, 1.347, 2.113, 1.109]}, {"time": 2.3333, "x": 0.932, "y": 0.932}]}, "Star4": {"translatex": [{"value": 125.31, "curve": [0.155, 257.5, 0.309, 363.11]}, {"time": 0.4667, "value": 363.11, "curve": [0.671, 363.11, 0.766, 235.81]}, {"time": 0.8, "value": 200.04, "curve": "stepped"}, {"time": 0.8333, "curve": [1.019, -190.4, 1.211, -444.49]}, {"time": 1.5, "value": -444.49, "curve": [1.784, -444.49, 2.058, -113.12]}, {"time": 2.3333, "value": 125.31}], "translatey": [{"value": 369.95, "curve": [0.304, 494.77, 0.591, 630.84]}, {"time": 0.8, "value": 758.54, "curve": "stepped"}, {"time": 0.8333, "value": -105.4, "curve": [1.014, -105.4, 1.707, 108.71]}, {"time": 2.3333, "value": 369.95}], "scale": [{"x": 0.848, "y": 0.848, "curve": [0.352, 0.63, 0.605, 0.229, 0.352, 0.63, 0.605, 0.229]}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0, "curve": [0.979, 0, 1.167, 1, 0.979, 0, 1.167, 1]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.8333, "curve": [2.02, 1, 2.185, 0.94, 2.02, 1, 2.185, 0.94]}, {"time": 2.3333, "x": 0.848, "y": 0.848}]}, "Star_inner4": {"scale": [{"x": 0.766, "y": 0.766, "curve": [0.267, 0.956, 0.534, 1.5, 0.267, 0.956, 0.534, 1.5]}, {"time": 0.8, "x": 1.5, "y": 1.5, "curve": "stepped"}, {"time": 0.8333, "x": 1.5, "y": 1.5, "curve": [1.278, 1.5, 1.722, 0.7, 1.278, 1.5, 1.722, 0.7]}, {"time": 2.1667, "x": 0.7, "y": 0.7, "curve": [2.223, 0.7, 2.278, 0.727, 2.223, 0.7, 2.278, 0.727]}, {"time": 2.3333, "x": 0.766, "y": 0.766}]}, "Star5": {"translatex": [{"value": -139.82, "curve": [0.167, -9.91, 0.333, 379.8]}, {"time": 0.5, "value": 379.8, "curve": [0.783, 379.8, 1.05, -409.02]}, {"time": 1.3333, "value": -409.02, "curve": [1.656, -409.02, 2.012, 100.9]}, {"time": 2.3, "value": 434.06, "curve": "stepped"}, {"time": 2.3333, "value": -139.82}], "translatey": [{"value": 132.5, "curve": [0.267, 132.5, 1.653, 555.58]}, {"time": 2.3, "value": 950.53, "curve": "stepped"}, {"time": 2.3333, "value": 132.5}], "scale": [{"x": 0, "y": 0, "curve": [0.146, 0, 0.333, 1, 0.146, 0, 0.333, 1]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": [1.628, 1, 2.023, 0.326, 1.628, 1, 2.023, 0.326]}, {"time": 2.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0}]}, "Star_inner5": {"scale": [{"x": 0.7, "y": 0.7, "curve": [0.3, 0.7, 0.6, 1.5, 0.3, 0.7, 0.6, 1.5]}, {"time": 0.9, "x": 1.5, "y": 1.5, "curve": [1.233, 1.5, 1.567, 0.7, 1.233, 1.5, 1.567, 0.7]}, {"time": 1.9, "x": 0.7, "y": 0.7, "curve": [2.033, 0.7, 2.167, 0.9, 2.033, 0.7, 2.167, 0.9]}, {"time": 2.3, "curve": "stepped"}, {"time": 2.3333, "x": 0.7, "y": 0.7}]}, "Star6": {"translatex": [{"value": -108.9, "curve": [0.191, 105.31, 0.379, 365.19]}, {"time": 0.5667, "value": 365.19, "curve": [0.7, 365.19, 0.833, 222.51]}, {"time": 0.9667, "value": 113.36, "curve": "stepped"}, {"time": 1, "value": 217.03, "curve": [1.078, 269.9, 1.156, 375.63]}, {"time": 1.2333, "value": 375.63, "curve": [1.467, 375.63, 1.7, -390.24]}, {"time": 1.9333, "value": -390.24, "curve": [2.068, -390.24, 2.202, -261.91]}, {"time": 2.3333, "value": -108.9}], "translatey": [{"value": 245.03, "curve": [0.414, 366.31, 0.788, 517.01]}, {"time": 0.9667, "value": 593.68, "curve": "stepped"}, {"time": 1, "value": 113.71, "curve": [1.306, -4.84, 1.847, 98.78]}, {"time": 2.3333, "value": 245.03}], "scale": [{"x": 0.933, "y": 0.933, "curve": [0.44, 0.755, 0.742, 0.264, 0.44, 0.755, 0.742, 0.264]}, {"time": 0.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": [1.146, 0, 1.333, 1, 1.146, 0, 1.333, 1]}, {"time": 1.5, "curve": "stepped"}, {"time": 2, "curve": [2.119, 1, 2.23, 0.974, 2.119, 1, 2.23, 0.974]}, {"time": 2.3333, "x": 0.933, "y": 0.933}]}, "Star_inner6": {"scale": [{"x": 0.732, "y": 0.732, "curve": [0.035, 0.714, 0.067, 0.7, 0.035, 0.714, 0.067, 0.7]}, {"time": 0.1, "x": 0.7, "y": 0.7, "curve": [0.389, 0.7, 0.678, 1.233, 0.389, 0.7, 0.678, 1.233]}, {"time": 0.9667, "x": 1.5, "y": 1.5, "curve": "stepped"}, {"time": 1, "curve": [1.189, 1, 1.378, 1.5, 1.189, 1, 1.378, 1.5]}, {"time": 1.5667, "x": 1.5, "y": 1.5, "curve": [1.823, 1.5, 2.079, 0.874, 1.823, 1.5, 2.079, 0.874]}, {"time": 2.3333, "x": 0.732, "y": 0.732}]}, "Star7": {"translatex": [{"value": 338.06, "curve": [0.123, 284.3, 0.245, 152.89]}, {"time": 0.3667, "value": 85.56, "curve": "stepped"}, {"time": 0.4, "value": -183.64, "curve": [0.567, -262.19, 0.733, -419.3]}, {"time": 0.9, "value": -419.3, "curve": [1.306, -419.3, 1.845, 361.82]}, {"time": 2.2333, "value": 361.82, "curve": [2.267, 361.82, 2.301, 352.93]}, {"time": 2.3333, "value": 338.06}], "translatey": [{"value": 675.61, "curve": [0.136, 724.63, 0.247, 738.2]}, {"time": 0.3667, "value": 758.54, "curve": "stepped"}, {"time": 0.4, "value": -99.14, "curve": [1.015, -99.14, 1.921, 526.72]}, {"time": 2.3333, "value": 675.61}], "scale": [{"x": 0.458, "y": 0.458, "curve": [0.143, 0.292, 0.263, 0.122, 0.143, 0.292, 0.263, 0.122]}, {"time": 0.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": [0.546, 0, 0.733, 1, 0.546, 0, 0.733, 1]}, {"time": 0.9, "curve": "stepped"}, {"time": 1.0667, "curve": "stepped"}, {"time": 1.4, "curve": [1.794, 1, 2.096, 0.735, 1.794, 1, 2.096, 0.735]}, {"time": 2.3333, "x": 0.458, "y": 0.458}]}, "Star_inner7": {"scale": [{"x": 1.139, "y": 1.139, "curve": [0.222, 1.327, 0.395, 1.504, 0.222, 1.327, 0.395, 1.504]}, {"time": 0.4, "x": 1.5, "y": 1.5, "curve": [0.623, 1.347, 0.846, 1.109, 0.623, 1.347, 0.846, 1.109]}, {"time": 1.0667, "x": 0.932, "y": 0.932, "curve": [1.235, 0.8, 1.401, 0.7, 1.235, 0.8, 1.401, 0.7]}, {"time": 1.5667, "x": 0.7, "y": 0.7, "curve": [1.773, 0.7, 2.085, 0.927, 1.773, 0.7, 2.085, 0.927]}, {"time": 2.3333, "x": 1.139, "y": 1.139}]}, "Star8": {"translatex": [{"value": -316.13, "curve": [0.045, -360.92, 0.089, -390.24]}, {"time": 0.1333, "value": -390.24, "curve": [0.492, -390.24, 0.912, 365.19]}, {"time": 1.1, "value": 365.19, "curve": [1.233, 365.19, 1.367, 222.51]}, {"time": 1.5, "value": 113.36, "curve": "stepped"}, {"time": 1.5333, "value": 217.03, "curve": [1.611, 269.9, 1.689, 375.63]}, {"time": 1.7667, "value": 375.63, "curve": [1.956, 375.63, 2.145, -125.19]}, {"time": 2.3333, "value": -316.13}], "translatey": [{"value": 112.68, "curve": [0.373, 170.7, 1.322, 517.01]}, {"time": 1.5, "value": 593.68, "curve": "stepped"}, {"time": 1.5333, "value": 113.71, "curve": [1.729, 37.84, 2.02, 63.91]}, {"time": 2.3333, "value": 112.68}], "scale": [{"curve": "stepped"}, {"time": 0.2, "curve": [0.319, 1, 0.43, 0.974, 0.319, 1, 0.43, 0.974]}, {"time": 0.5333, "x": 0.933, "y": 0.933, "curve": [0.974, 0.755, 1.276, 0.264, 0.974, 0.755, 1.276, 0.264]}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5333, "x": 0, "y": 0, "curve": [1.679, 0, 1.867, 1, 1.679, 0, 1.867, 1]}, {"time": 2.0333, "curve": "stepped"}, {"time": 2.3333}]}, "Star_inner8": {"scale": [{"x": 1.358, "y": 1.358, "curve": [0.18, 1.164, 0.357, 0.831, 0.18, 1.164, 0.357, 0.831]}, {"time": 0.5333, "x": 0.732, "y": 0.732, "curve": [0.568, 0.714, 0.601, 0.7, 0.568, 0.714, 0.601, 0.7]}, {"time": 0.6333, "x": 0.7, "y": 0.7, "curve": [0.922, 0.7, 1.211, 1.233, 0.922, 0.7, 1.211, 1.233]}, {"time": 1.5, "x": 1.5, "y": 1.5, "curve": "stepped"}, {"time": 1.5333, "curve": [1.722, 1, 1.911, 1.5, 1.722, 1, 1.911, 1.5]}, {"time": 2.1, "x": 1.5, "y": 1.5, "curve": [2.179, 1.5, 2.257, 1.443, 2.179, 1.5, 2.257, 1.443]}, {"time": 2.3333, "x": 1.358, "y": 1.358}]}, "Star": {"translatex": [{"curve": [0.185, -190.4, 0.377, -444.49]}, {"time": 0.6667, "value": -444.49, "curve": [1.111, -444.49, 1.556, 363.11]}, {"time": 2, "value": 363.11, "curve": [2.204, 363.11, 2.299, 235.81]}, {"time": 2.3333, "value": 200.04}], "translatey": [{"value": -105.4, "curve": [0.271, -105.4, 1.677, 357.86]}, {"time": 2.3333, "value": 758.54}], "scale": [{"x": 0, "y": 0, "curve": [0.146, 0, 0.333, 1, 0.146, 0, 0.333, 1]}, {"time": 0.5, "curve": "stepped"}, {"time": 1, "curve": [1.644, 1, 2.049, 0.334, 1.644, 1, 2.049, 0.334]}, {"time": 2.3333, "x": 0, "y": 0}]}}, "drawOrder": [{"offsets": [{"slot": "Star6", "offset": -12}, {"slot": "Star5", "offset": -11}, {"slot": "Star4", "offset": -14}, {"slot": "Star2", "offset": -16}]}, {"time": 0.2333, "offsets": [{"slot": "Star", "offset": 6}, {"slot": "Star6", "offset": -12}, {"slot": "Star8", "offset": -12}, {"slot": "Star5", "offset": -11}, {"slot": "Star3", "offset": -14}, {"slot": "Star2", "offset": -14}]}, {"time": 0.5, "offsets": [{"slot": "Star", "offset": 4}, {"slot": "Star8", "offset": -13}, {"slot": "Star5", "offset": 2}, {"slot": "Star3", "offset": -15}]}, {"time": 1, "offsets": [{"slot": "Star", "offset": -9}, {"slot": "Star8", "offset": -13}, {"slot": "Star5", "offset": 3}, {"slot": "Star3", "offset": -15}, {"slot": "Star7", "offset": -14}]}, {"time": 1.4, "offsets": [{"slot": "Star", "offset": -9}, {"slot": "Star8", "offset": -13}, {"slot": "Star5", "offset": -11}, {"slot": "Star3", "offset": -15}, {"slot": "Star7", "offset": -13}]}, {"time": 1.7333, "offsets": [{"slot": "Star", "offset": -11}, {"slot": "Star6", "offset": 5}, {"slot": "Star8", "offset": 3}, {"slot": "Star5", "offset": -13}, {"slot": "Star4", "offset": -13}, {"slot": "Star7", "offset": -14}]}, {"time": 2.0333, "offsets": [{"slot": "Star", "offset": -11}, {"slot": "Star6", "offset": -8}, {"slot": "Star8", "offset": 4}, {"slot": "Star5", "offset": -13}, {"slot": "Star4", "offset": -13}, {"slot": "Star2", "offset": -15}]}]}, "t4_IDLE": {"slots": {"1": {"rgba": [{"color": "ffffff00", "curve": [0.089, 1, 0.178, 1, 0.089, 1, 0.178, 1, 0.089, 1, 0.178, 1, 0.089, 0, 0.178, 1]}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": [0.822, 1, 0.978, 1, 0.822, 1, 0.978, 1, 0.822, 1, 0.978, 1, 0.822, 1, 0.978, 0]}, {"time": 1.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00", "curve": [1.256, 1, 1.344, 1, 1.256, 1, 1.344, 1, 1.256, 1, 1.344, 1, 1.256, 0, 1.344, 1]}, {"time": 1.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff", "curve": [1.989, 1, 2.144, 1, 1.989, 1, 2.144, 1, 1.989, 1, 2.144, 1, 1.989, 1, 2.144, 0]}, {"time": 2.3, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}]}, "2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff", "curve": [0.411, 1, 0.556, 1, 0.411, 1, 0.556, 1, 0.411, 1, 0.556, 1, 0.411, 1, 0.556, 0]}, {"time": 0.7, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00", "curve": [0.811, 1, 0.889, 1, 0.811, 1, 0.889, 1, 0.811, 1, 0.889, 1, 0.811, 0, 0.889, 1]}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff", "curve": [1.578, 1, 1.722, 1, 1.578, 1, 1.722, 1, 1.578, 1, 1.722, 1, 1.578, 1, 1.722, 0]}, {"time": 1.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.9, "color": "ffffff00", "curve": [1.978, 1, 2.056, 1, 1.978, 1, 2.056, 1, 1.978, 1, 2.056, 1, 1.978, 0, 2.056, 1]}, {"time": 2.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}]}, "3": {"rgba": [{"color": "ffffffff", "curve": [0.111, 1, 0.256, 1, 0.111, 1, 0.256, 1, 0.111, 1, 0.256, 1, 0.111, 1, 0.256, 0]}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": [0.489, 1, 0.578, 1, 0.489, 1, 0.578, 1, 0.489, 1, 0.578, 1, 0.489, 0, 0.578, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff", "curve": [1.278, 1, 1.422, 1, 1.278, 1, 1.422, 1, 1.278, 1, 1.422, 1, 1.278, 1, 1.422, 0]}, {"time": 1.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00", "curve": [1.656, 1, 1.744, 1, 1.656, 1, 1.744, 1, 1.656, 1, 1.744, 1, 1.656, 0, 1.744, 1]}, {"time": 1.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}]}, "5": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5667, "color": "ffffffff", "curve": [0.656, 1, 0.744, 1, 0.656, 1, 0.744, 1, 0.656, 1, 0.744, 1, 0.656, 1, 0.744, 0]}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00", "curve": [0.944, 1, 1.022, 1, 0.944, 1, 1.022, 1, 0.944, 1, 1.022, 1, 0.944, 0, 1.022, 1]}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7333, "color": "ffffffff", "curve": [1.822, 1, 1.911, 1, 1.822, 1, 1.911, 1, 1.822, 1, 1.911, 1, 1.822, 1, 1.911, 0]}, {"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0333, "color": "ffffff00", "curve": [2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.111, 1, 2.189, 1, 2.111, 0, 2.189, 1]}, {"time": 2.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}]}, "Line": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": [1.733, 1, 1.967, 1, 1.733, 1, 1.967, 1, 1.733, 1, 1.967, 1, 1.733, 1, 1.967, 0.33]}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}]}, "Line2": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff", "curve": [1, 1, 1.233, 1, 1, 1, 1.233, 1, 1, 1, 1.233, 1, 1, 1, 1.233, 0.33]}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}]}, "Line3": {"rgba": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff", "curve": [1.333, 1, 1.567, 1, 1.333, 1, 1.567, 1, 1.333, 1, 1.567, 1, 1.333, 1, 1.567, 0.33]}, {"time": 1.8, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3333, "color": "ffffff00"}]}, "Line4": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff", "curve": [0.667, 1, 0.9, 1, 0.667, 1, 0.9, 1, 0.667, 1, 0.9, 1, 0.667, 1, 0.9, 0.33]}, {"time": 1.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}]}, "Line5": {"rgba": [{"color": "fffffffa", "curve": [0.212, 1, 0.423, 1, 0.212, 1, 0.423, 1, 0.212, 1, 0.423, 1, 0.212, 0.87, 0.423, 0.3]}, {"time": 0.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff", "curve": [2.289, 1, 2.312, 1, 2.289, 1, 2.312, 1, 2.289, 1, 2.312, 1, 2.289, 1, 2.312, 0.99]}, {"time": 2.3333, "color": "fffffffa"}]}, "Line6": {"rgba": [{"color": "fffffffa", "curve": [0.212, 1, 0.423, 1, 0.212, 1, 0.423, 1, 0.212, 1, 0.423, 1, 0.212, 0.87, 0.423, 0.3]}, {"time": 0.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff", "curve": [2.289, 1, 2.312, 1, 2.289, 1, 2.312, 1, 2.289, 1, 2.312, 1, 2.289, 1, 2.312, 0.99]}, {"time": 2.3333, "color": "fffffffa"}]}, "Line7": {"rgba": [{"color": "ffffffc3", "curve": [0.146, 1, 0.289, 1, 0.146, 1, 0.289, 1, 0.146, 1, 0.289, 1, 0.146, 0.54, 0.289, 0.21]}, {"time": 0.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff", "curve": [2.156, 1, 2.246, 1, 2.156, 1, 2.246, 1, 2.156, 1, 2.246, 1, 2.156, 1, 2.246, 0.9]}, {"time": 2.3333, "color": "ffffffc3"}]}, "Line8": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": [0.433, 1, 0.667, 1, 0.433, 1, 0.667, 1, 0.433, 1, 0.667, 1, 0.433, 1, 0.667, 0.33]}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}]}, "Line9": {"rgba": [{"color": "ffffff29", "curve": [0.035, 1, 0.067, 1, 0.035, 1, 0.067, 1, 0.035, 1, 0.067, 1, 0.035, 0.1, 0.067, 0.05]}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7333, "color": "ffffffff", "curve": [1.934, 1, 2.135, 1, 1.934, 1, 2.135, 1, 1.934, 1, 2.135, 1, 1.934, 1, 2.135, 0.51]}, {"time": 2.3333, "color": "ffffff29"}]}, "Line10": {"rgba": [{"color": "ffffff1a", "curve": [0.023, 1, 0.045, 1, 0.023, 1, 0.045, 1, 0.023, 1, 0.045, 1, 0.023, 0.07, 0.045, 0.03]}, {"time": 0.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff", "curve": [1.912, 1, 2.123, 1, 1.912, 1, 2.123, 1, 1.912, 1, 2.123, 1, 1.912, 1, 2.123, 0.45]}, {"time": 2.3333, "color": "ffffff1a"}]}}, "bones": {"1": {"scale": [{"x": 1.114, "y": 1.114, "curve": "stepped"}, {"time": 0.1333, "x": 1.114, "y": 1.114, "curve": [0.467, 1.114, 0.869, 0.958, 0.467, 1.114, 0.869, 0.958]}, {"time": 1.1333, "x": 0.8, "y": 0.8, "curve": "stepped"}, {"time": 1.1667, "x": 1.114, "y": 1.114, "curve": "stepped"}, {"time": 1.3, "x": 1.114, "y": 1.114, "curve": [1.633, 1.114, 2.035, 0.958, 1.633, 1.114, 2.035, 0.958]}, {"time": 2.3, "x": 0.8, "y": 0.8, "curve": "stepped"}, {"time": 2.3333, "x": 1.114, "y": 1.114}]}, "2": {"scale": [{"x": 1.381, "y": 1.381, "curve": [0.257, 1.3, 0.518, 1.14, 0.257, 1.3, 0.518, 1.14]}, {"time": 0.7, "curve": "stepped"}, {"time": 0.7333, "x": 1.434, "y": 1.434, "curve": "stepped"}, {"time": 0.8667, "x": 1.434, "y": 1.434, "curve": [0.931, 1.434, 0.998, 1.425, 0.931, 1.434, 0.998, 1.425]}, {"time": 1.0667, "x": 1.409, "y": 1.409, "curve": [1.1, 1.401, 1.133, 1.392, 1.1, 1.401, 1.133, 1.392]}, {"time": 1.1667, "x": 1.381, "y": 1.381, "curve": [1.424, 1.3, 1.685, 1.14, 1.424, 1.3, 1.685, 1.14]}, {"time": 1.8667, "curve": "stepped"}, {"time": 1.9, "x": 1.434, "y": 1.434, "curve": "stepped"}, {"time": 2.0333, "x": 1.434, "y": 1.434, "curve": [2.098, 1.434, 2.165, 1.425, 2.098, 1.434, 2.165, 1.425]}, {"time": 2.2333, "x": 1.409, "y": 1.409, "curve": [2.267, 1.401, 2.3, 1.392, 2.267, 1.401, 2.3, 1.392]}, {"time": 2.3333, "x": 1.381, "y": 1.381}]}, "4": {"scale": [{"curve": [0.189, 1, 0.378, 1.008, 0.189, 1, 0.378, 1.008]}, {"time": 0.5667, "x": 1.008, "y": 1.008, "curve": [0.767, 1.008, 0.967, 1, 0.767, 1.008, 0.967, 1]}, {"time": 1.1667, "curve": [1.356, 1, 1.544, 1.008, 1.356, 1, 1.544, 1.008]}, {"time": 1.7333, "x": 1.008, "y": 1.008, "curve": [1.933, 1.008, 2.133, 1, 1.933, 1.008, 2.133, 1]}, {"time": 2.3333}]}, "3": {"scale": [{"x": 0.977, "y": 0.977, "curve": [0.137, 0.925, 0.263, 0.862, 0.137, 0.925, 0.263, 0.862]}, {"time": 0.3667, "x": 0.8, "y": 0.8, "curve": "stepped"}, {"time": 0.4, "x": 1.114, "y": 1.114, "curve": "stepped"}, {"time": 0.5333, "x": 1.114, "y": 1.114, "curve": [0.736, 1.114, 0.956, 1.057, 0.736, 1.114, 0.956, 1.057]}, {"time": 1.1667, "x": 0.977, "y": 0.977, "curve": [1.304, 0.925, 1.429, 0.862, 1.304, 0.925, 1.429, 0.862]}, {"time": 1.5333, "x": 0.8, "y": 0.8, "curve": "stepped"}, {"time": 1.5667, "x": 1.114, "y": 1.114, "curve": "stepped"}, {"time": 1.7, "x": 1.114, "y": 1.114, "curve": [1.902, 1.114, 2.122, 1.057, 1.902, 1.114, 2.122, 1.057]}, {"time": 2.3333, "x": 0.977, "y": 0.977}]}, "5": {"scale": [{"x": 1.416, "y": 1.416, "curve": [0.012, 1.414, 0.022, 1.412, 0.012, 1.414, 0.022, 1.412]}, {"time": 0.0333, "x": 1.409, "y": 1.409, "curve": [0.067, 1.401, 0.101, 1.392, 0.067, 1.401, 0.101, 1.392]}, {"time": 0.1333, "x": 1.381, "y": 1.381, "curve": [0.39, 1.3, 0.652, 1.14, 0.39, 1.3, 0.652, 1.14]}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.8667, "x": 1.434, "y": 1.434, "curve": "stepped"}, {"time": 1, "x": 1.434, "y": 1.434, "curve": [1.054, 1.434, 1.111, 1.427, 1.054, 1.434, 1.111, 1.427]}, {"time": 1.1667, "x": 1.416, "y": 1.416, "curve": [1.178, 1.414, 1.189, 1.412, 1.178, 1.414, 1.189, 1.412]}, {"time": 1.2, "x": 1.409, "y": 1.409, "curve": [1.233, 1.401, 1.267, 1.392, 1.233, 1.401, 1.267, 1.392]}, {"time": 1.3, "x": 1.381, "y": 1.381, "curve": [1.557, 1.3, 1.818, 1.14, 1.557, 1.3, 1.818, 1.14]}, {"time": 2, "curve": "stepped"}, {"time": 2.0333, "x": 1.434, "y": 1.434, "curve": "stepped"}, {"time": 2.1667, "x": 1.434, "y": 1.434, "curve": [2.221, 1.434, 2.278, 1.427, 2.221, 1.434, 2.278, 1.427]}, {"time": 2.3333, "x": 1.416, "y": 1.416}]}, "Portal_scale": {"scale": [{"x": 1.019, "y": 1.019, "curve": [0.133, 1.011, 0.267, 1, 0.133, 1.011, 0.267, 1]}, {"time": 0.4, "curve": [0.589, 1, 0.778, 1.026, 0.589, 1, 0.778, 1.026]}, {"time": 0.9667, "x": 1.026, "y": 1.026, "curve": [1.033, 1.026, 1.1, 1.023, 1.033, 1.026, 1.1, 1.023]}, {"time": 1.1667, "x": 1.019, "y": 1.019, "curve": [1.3, 1.011, 1.433, 1, 1.3, 1.011, 1.433, 1]}, {"time": 1.5667, "curve": [1.756, 1, 1.944, 1.026, 1.756, 1, 1.944, 1.026]}, {"time": 2.1333, "x": 1.026, "y": 1.026, "curve": [2.2, 1.026, 2.267, 1.023, 2.2, 1.026, 2.267, 1.023]}, {"time": 2.3333, "x": 1.019, "y": 1.019}]}, "Line": {"scale": [{"curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": [1.156, 0, 1.578, 1, 1.156, 0, 1.578, 1]}, {"time": 2, "curve": "stepped"}, {"time": 2.3333}]}, "Line2": {"scale": [{"x": 0, "y": 0, "curve": [0.422, 0, 0.844, 1, 0.422, 0, 0.844, 1]}, {"time": 1.2667, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0}]}, "Line3": {"scale": [{"curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": [0.756, 0, 1.178, 1, 0.756, 0, 1.178, 1]}, {"time": 1.6, "curve": "stepped"}, {"time": 2.2, "curve": "stepped"}, {"time": 2.3333}]}, "Line4": {"scale": [{"x": 0.175, "y": 0.175, "curve": [0.311, 0.461, 0.622, 1, 0.311, 0.461, 0.622, 1]}, {"time": 0.9333, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": [2.111, 0, 2.222, 0.073, 2.111, 0, 2.222, 0.073]}, {"time": 2.3333, "x": 0.175, "y": 0.175}]}, "Line5": {"scale": [{"x": 0.727, "y": 0.727, "curve": [0.146, 0.879, 0.29, 1, 0.146, 0.879, 0.29, 1]}, {"time": 0.4333, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": [1.779, 0, 2.057, 0.431, 1.779, 0, 2.057, 0.431]}, {"time": 2.3333, "x": 0.727, "y": 0.727}]}, "Line6": {"scale": [{"x": 0.727, "y": 0.727, "curve": [0.146, 0.879, 0.29, 1, 0.146, 0.879, 0.29, 1]}, {"time": 0.4333, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": [1.779, 0, 2.057, 0.431, 1.779, 0, 2.057, 0.431]}, {"time": 2.3333, "x": 0.727, "y": 0.727}]}, "Line7": {"scale": [{"x": 0.908, "y": 0.908, "curve": [0.079, 0.963, 0.156, 1, 0.079, 0.963, 0.156, 1]}, {"time": 0.2333, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": [1.645, 0, 1.99, 0.663, 1.645, 0, 1.99, 0.663]}, {"time": 2.3333, "x": 0.908, "y": 0.908}]}, "Line8": {"scale": [{"x": 0.422, "y": 0.422, "curve": [0.236, 0.694, 0.468, 1, 0.236, 0.694, 0.468, 1]}, {"time": 0.7, "curve": "stepped"}, {"time": 1.7667, "x": 0, "y": 0, "curve": [1.957, 0, 2.147, 0.199, 1.957, 0, 2.147, 0.199]}, {"time": 2.3333, "x": 0.422, "y": 0.422}]}, "Line9": {"scale": [{"curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0, "curve": [1.389, 0, 1.811, 1, 1.389, 0, 1.811, 1]}, {"time": 2.2333, "curve": "stepped"}, {"time": 2.3333}]}, "Line10": {"scale": [{"curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0, "curve": [1.356, 0, 1.778, 1, 1.356, 0, 1.778, 1]}, {"time": 2.2, "curve": "stepped"}, {"time": 2.3333}]}}}, "t5_Appear": {"slots": {"Puddle_ripple": {"rgba": [{"color": "ffffffff"}]}}, "bones": {"cntr": {"scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": [0.379, 0.805, 0.789, 1, 0.379, 0.805, 0.789, 1]}, {"time": 1.1667}]}, "Star_start": {"scale": [{"x": 0, "y": 0, "curve": [0.044, 0.474, 0.1, 1.422, 0.044, 0.474, 0.1, 1.422]}, {"time": 0.1333, "x": 1.422, "y": 1.422, "curve": [0.2, 1.422, 0.267, 0.474, 0.2, 1.422, 0.267, 0.474]}, {"time": 0.3333, "x": 0, "y": 0}]}, "Black_sphere": {"translate": [{}]}, "Black_Puddle_Edge": {"translate": [{}], "scale": [{}]}, "Black_droplet": {"scale": [{"y": 3.333}]}, "Black_puddle": {"translate": [{}], "scale": [{}]}, "Puddle_ripple": {"translate": [{}], "scale": [{}]}, "Base_circles": {"scale": [{"x": 0, "y": 0}]}, "Circle": {"translate": [{"y": -762.51}], "scale": [{"x": 0, "y": 0}]}, "Circle2": {"translate": [{"y": -1117.18}], "scale": [{"x": 0, "y": 0}]}, "Circle3": {"translate": [{"y": -1501.93}], "scale": [{"x": 0, "y": 0}]}, "Circle4": {"translate": [{"y": -1921.51}], "scale": [{"x": 0, "y": 0}]}, "Rainbow": {"scale": [{"x": 0, "y": 0}]}, "Rainbow_BG": {"translate": [{"y": -76.67}], "scale": [{"x": 0, "y": 0}]}}}, "t5_Drop": {"slots": {"Puddle_ripple": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": [0.278, 1, 0.356, 1, 0.278, 1, 0.356, 1, 0.278, 1, 0.356, 1, 0.278, 1, 0.356, 0]}, {"time": 0.4333, "color": "ffffff00"}]}}, "bones": {"cntr": {"scale": [{}]}, "Star_start": {"scale": [{"x": 0, "y": 0}]}, "Black_sphere": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.145, 0, 0.222, 0, 0.11, 165.21, 0.222, 263.09]}, {"time": 0.3, "y": 263.09, "curve": "stepped"}, {"time": 0.3667, "y": 263.09, "curve": [0.456, 0, 0.544, 0, 0.478, 263.09, 0.579, 128.11]}, {"time": 0.6333}]}, "Black_Puddle_Edge": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.145, 0, 0.222, 0, 0.11, 141.1, 0.232, 192.38]}, {"time": 0.3, "y": 192.38, "curve": [0.356, 0, 0.411, 0, 0.341, 192.38, 0.443, 96.6]}, {"time": 0.4667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.134, 1, 0.2, 0, 0.134, 1, 0.2, 1]}, {"time": 0.2667, "x": 0, "curve": [0.345, 0, 0.422, 1, 0.345, 1, 0.422, 1]}, {"time": 0.5}]}, "Black_droplet": {"scale": [{"y": 3.333, "curve": "stepped"}, {"time": 0.0667, "y": 3.333, "curve": "stepped"}, {"time": 0.2667, "y": 3.333, "curve": [0.3, 1, 0.333, 1, 0.3, 3.333, 0.333, 1]}, {"time": 0.3667}]}, "Black_puddle": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.144, 0, 0.222, 0, 0.144, 7.76, 0.222, 23.27]}, {"time": 0.3, "y": 23.27, "curve": [0.389, 0, 0.478, 0, 0.389, 23.27, 0.478, 7.76]}, {"time": 0.5667}], "scale": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.144, 1, 0.222, 1, 0.144, 1.028, 0.222, 1.085]}, {"time": 0.3, "y": 1.085, "curve": [0.389, 1, 0.478, 1, 0.389, 1.085, 0.478, 1.028]}, {"time": 0.5667}]}, "Puddle_ripple": {"translate": [{}], "scale": [{"curve": [0.076, 1.866, 0.225, 2.442, 0.076, 1.866, 0.225, 2.442]}, {"time": 0.4333, "x": 2.442, "y": 2.442}]}, "Base_circles": {"scale": [{"x": 0, "y": 0}]}, "Circle": {"translate": [{"y": -762.51}], "scale": [{"x": 0, "y": 0}]}, "Circle2": {"translate": [{"y": -1117.18}], "scale": [{"x": 0, "y": 0}]}, "Circle3": {"translate": [{"y": -1501.93}], "scale": [{"x": 0, "y": 0}]}, "Circle4": {"translate": [{"y": -1921.51}], "scale": [{"x": 0, "y": 0}]}, "Rainbow": {"scale": [{"x": 0, "y": 0}]}, "Rainbow_BG": {"translate": [{"y": -76.67}], "scale": [{"x": 0, "y": 0}]}}}, "t5_IDLE": {"slots": {"Puddle_ripple": {"rgba": [{"color": "ffffffff"}]}}, "bones": {"cntr": {"scale": [{}]}, "Star_start": {"scale": [{"x": 0, "y": 0}]}, "Black_sphere": {"translate": [{}]}, "Black_Puddle_Edge": {"translate": [{}], "scale": [{}]}, "Black_droplet": {"scale": [{"y": 3.333}]}, "Black_puddle": {"translate": [{}], "scale": [{}]}, "Puddle_ripple": {"translate": [{}], "scale": [{}]}, "Base_circles": {"scale": [{"x": 0, "y": 0}]}, "Circle": {"translate": [{"y": -762.51}], "scale": [{"x": 0, "y": 0}]}, "Circle2": {"translate": [{"y": -1117.18}], "scale": [{"x": 0, "y": 0}]}, "Circle3": {"translate": [{"y": -1501.93}], "scale": [{"x": 0, "y": 0}]}, "Circle4": {"translate": [{"y": -1921.51}], "scale": [{"x": 0, "y": 0}]}, "Rainbow": {"scale": [{"x": 0, "y": 0}]}, "Rainbow_BG": {"translate": [{"y": -76.67}], "scale": [{"x": 0, "y": 0}]}}}, "t5_Off": {"slots": {"Puddle_ripple": {"rgba": [{"color": "ffffffff"}]}}, "bones": {"cntr": {"scale": [{"x": 0, "y": 0}]}, "Star_start": {"scale": [{"x": 0, "y": 0}]}, "Black_sphere": {"translate": [{}]}, "Black_Puddle_Edge": {"translate": [{}], "scale": [{}]}, "Black_droplet": {"scale": [{"y": 3.333}]}, "Black_puddle": {"translate": [{}], "scale": [{}]}, "Puddle_ripple": {"translate": [{}], "scale": [{}]}, "Base_circles": {"scale": [{"x": 0, "y": 0}]}, "Circle": {"translate": [{"y": -762.51}], "scale": [{"x": 0, "y": 0}]}, "Circle2": {"translate": [{"y": -1117.18}], "scale": [{"x": 0, "y": 0}]}, "Circle3": {"translate": [{"y": -1501.93}], "scale": [{"x": 0, "y": 0}]}, "Circle4": {"translate": [{"y": -1921.51}], "scale": [{"x": 0, "y": 0}]}, "Rainbow": {"scale": [{"x": 0, "y": 0}]}, "Rainbow_BG": {"translate": [{"y": -76.67}], "scale": [{"x": 0, "y": 0}]}}}, "t5_Rainbow_Appear": {"slots": {"circle4": {"attachment": [{"time": 0.4667, "name": "Images/Revive_portal/circle2"}]}, "Puddle_ripple": {"rgba": [{"color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff", "curve": [0.267, 1, 0.367, 1, 0.267, 1, 0.367, 1, 0.267, 1, 0.367, 1, 0.267, 1, 0.367, 0]}, {"time": 0.4667, "color": "ffffff00"}]}}, "bones": {"cntr": {"scale": [{"curve": [0.065, 1.082, 0.244, 1.217, 0.065, 1.082, 0.244, 1.217]}, {"time": 0.3667, "x": 1.217, "y": 1.217, "curve": [0.522, 1.217, 0.678, 1.1, 0.522, 1.217, 0.678, 1.1]}, {"time": 0.8333, "x": 1.1, "y": 1.1}]}, "Star_start": {"scale": [{"x": 0, "y": 0}]}, "Black_sphere": {"translate": [{}]}, "Black_Puddle_Edge": {"translate": [{}], "scale": [{}]}, "Black_droplet": {"scale": [{"y": 3.333}]}, "Black_puddle": {"translate": [{}], "scale": [{}]}, "Puddle_ripple": {"translate": [{}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0333, "x": 4.415, "y": 4.415, "curve": [0.167, 5.478, 0.3, 5.478, 0.167, 5.478, 0.3, 5.867]}, {"time": 0.4333, "x": 5.478, "y": 5.867}]}, "Base_circles": {"scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": [0.269, 0.592, 0.7, 1, 0.234, 0, 0.7, 1]}, {"time": 1}]}, "Circle": {"translate": [{"y": -762.51, "curve": "stepped"}, {"time": 0.3333, "y": -762.51, "curve": [0.556, 0, 0.778, 0, 0.556, -762.51, 0.778, 0]}, {"time": 1}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": [0.269, 0.592, 0.7, 1, 0.234, 0, 0.7, 1]}, {"time": 1}]}, "Circle2": {"translate": [{"y": -1117.18, "curve": "stepped"}, {"time": 0.2667, "y": -1117.18, "curve": [0.511, 0, 0.756, 0, 0.511, -1117.18, 0.756, 0]}, {"time": 1}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": [0.269, 0.592, 0.7, 1, 0.234, 0, 0.7, 1]}, {"time": 1}]}, "Circle3": {"translate": [{"y": -1501.93, "curve": "stepped"}, {"time": 0.2, "y": -1501.93, "curve": [0.467, 0, 0.733, 0, 0.467, -1501.93, 0.733, 0]}, {"time": 1}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": [0.269, 0.592, 0.7, 1, 0.234, 0, 0.7, 1]}, {"time": 1}]}, "Circle4": {"translate": [{"y": -1921.51, "curve": "stepped"}, {"time": 0.1, "y": -1921.51, "curve": [0.4, 0, 0.7, 0, 0.4, -1921.51, 0.7, 0]}, {"time": 1}], "scale": [{"x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": [0.269, 0.592, 0.7, 1, 0.234, 0, 0.7, 1]}, {"time": 1}]}, "Rainbow_BG": {"translate": [{"y": -76.67, "curve": [0.156, 0, 0.311, 0, 0.156, -76.67, 0.311, 0]}, {"time": 0.4667}], "scale": [{"x": 0, "y": 0, "curve": [0.119, 0.17, 0.667, 1, 0.092, 0.818, 0.667, 1]}, {"time": 1}]}, "Rainbow": {"scale": [{"x": 0, "y": 0, "curve": [0.095, 0.91, 0.667, 1, 0.095, 0.91, 0.667, 1]}, {"time": 1}]}}}}}