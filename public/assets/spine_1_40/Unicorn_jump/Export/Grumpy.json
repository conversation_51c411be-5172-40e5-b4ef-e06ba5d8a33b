{"skeleton": {"hash": "BiQRSUdxZ5E", "spine": "4.2.40", "x": -155.45, "y": -79.55, "width": 313, "height": 160.15, "images": "./Images/Grumpy/", "audio": "./audio"}, "bones": [{"name": "root"}, {"name": "low_cntr", "parent": "root", "y": -75}, {"name": "body", "parent": "low_cntr", "length": 32.28, "rotation": 90.6, "x": 1.15, "y": 4.97, "color": "0a81fcff"}, {"name": "body_R", "parent": "body", "x": 73.23, "y": 107.76, "color": "39a2aaff", "icon": "circle"}, {"name": "body_L", "parent": "body", "x": 73.23, "y": -120.28, "color": "39a2aaff", "icon": "circle"}, {"name": "face", "parent": "body", "x": 83.67, "y": 0.09, "color": "0a81fcff", "icon": "arrowsB"}, {"name": "eyeR", "parent": "face", "x": -16.91, "y": 64.64, "color": "e46cf7ff", "icon": "arrowUpDown"}, {"name": "closed_eyeR_up", "parent": "eyeR", "x": 24.82, "color": "e46cf7ff"}, {"name": "closed_eyeL_down", "parent": "eyeR", "x": 16.01, "color": "e46cf7ff"}, {"name": "eyeR2", "parent": "face", "x": -16.91, "y": -80.42, "color": "f9a756ff", "icon": "arrowUpDown"}, {"name": "closed_eyeL_up", "parent": "eyeR2", "x": 21.89, "color": "f9a756ff"}, {"name": "closed_eyeR_down", "parent": "eyeR2", "x": 13.09, "color": "f9a756ff"}, {"name": "browR", "parent": "eyeR", "length": 37.5, "rotation": -98.83, "x": 63.1, "y": 9.11, "color": "e46cf7ff"}, {"name": "browL", "parent": "eyeR2", "length": 39.99, "rotation": 97.83, "x": 66.76, "y": -9.97, "color": "f9a756ff"}, {"name": "eye_r", "parent": "body", "x": 93.91, "y": 54, "color": "0a81fcff"}, {"name": "eye_l", "parent": "eye_r", "x": -2.16, "y": -139.71, "color": "0a81fcff"}, {"name": "mouth", "parent": "face", "x": -39.94, "y": -8.42, "color": "0a81fcff", "icon": "arrowsB"}, {"name": "body_R2", "parent": "body_R", "length": 57, "rotation": -169.76, "x": -8.34, "y": 22.59, "color": "39a2aaff"}, {"name": "body_L2", "parent": "body_L", "length": 62.5, "rotation": 171.74, "x": -7.49, "y": -10.24, "color": "39a2aaff"}, {"name": "death", "parent": "root"}, {"name": "blot", "parent": "death", "rotation": -0.04, "icon": "flower"}, {"name": "blot_drops_control", "parent": "blot", "y": -10.68, "icon": "arrowDown"}, {"name": "Drops", "parent": "blot", "rotation": 0.04}, {"name": "blot_drop2", "parent": "Drops"}, {"name": "blot_drop_s1", "parent": "Drops"}, {"name": "blot_drop3", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop4", "parent": "Drops", "scaleX": 1.2553, "scaleY": 1.2553}, {"name": "blot_drop_s2", "parent": "Drops"}, {"name": "blot_drop5", "parent": "Drops"}, {"name": "blot_drop_s3", "parent": "Drops"}, {"name": "blot_drop6", "parent": "Drops", "scaleX": 0.8698, "scaleY": 0.8698}, {"name": "blot_drop_s4", "parent": "Drops"}, {"name": "legL", "parent": "low_cntr", "x": 122.65, "y": 10.13, "color": "ff3f00ff", "icon": "ik"}, {"name": "legR", "parent": "low_cntr", "x": -119.15, "y": 12.51, "color": "ff3f00ff", "icon": "ik"}], "slots": [{"name": "body_outline", "bone": "body", "attachment": "body_outline"}, {"name": "leg_r_outline", "bone": "body", "attachment": "leg_r_outline"}, {"name": "leg_l_outline", "bone": "body", "attachment": "leg_l_outline"}, {"name": "body", "bone": "body", "attachment": "body"}, {"name": "leg_r", "bone": "body", "attachment": "leg_r"}, {"name": "leg_l", "bone": "body", "attachment": "leg_l"}, {"name": "eye_l", "bone": "body", "attachment": "eye_l"}, {"name": "pupil_l", "bone": "eye_r", "attachment": "pupil_l"}, {"name": "eyelid_l_l", "bone": "body", "attachment": "eyelid_l_l"}, {"name": "eyelid_u_l", "bone": "body", "attachment": "eyelid_u_l"}, {"name": "eye_r", "bone": "body", "attachment": "eye_r"}, {"name": "pupil_r", "bone": "eye_l", "attachment": "pupil_r"}, {"name": "eyelid_l_r", "bone": "body", "attachment": "eyelid_l_r"}, {"name": "eyelid_u_r", "bone": "body", "attachment": "eyelid_u_r"}, {"name": "brow_l", "bone": "body", "attachment": "brow_l"}, {"name": "brow_r", "bone": "body", "attachment": "brow_r"}, {"name": "mouth_base", "bone": "body", "attachment": "mouth_base"}, {"name": "mouth", "bone": "body", "attachment": "mouth"}, {"name": "teeth", "bone": "body", "attachment": "teeth"}, {"name": "blot", "bone": "blot", "color": "29b3c5ff", "dark": "e0e0e0"}, {"name": "blot_drop2", "bone": "blot_drop2", "color": "29b3c5ff", "dark": "e0e0e0"}, {"name": "blot_drop_s1", "bone": "blot_drop_s1", "color": "29b3c5ff", "dark": "e0e0e0"}, {"name": "blot_drop3", "bone": "blot_drop3", "color": "29b3c5ff", "dark": "e0e0e0"}, {"name": "blot_drop4", "bone": "blot_drop4", "color": "29b3c5ff", "dark": "e0e0e0"}, {"name": "blot_drop5", "bone": "blot_drop_s2", "color": "29b3c5ff", "dark": "e0e0e0"}, {"name": "blot_drop6", "bone": "blot_drop5", "color": "29b3c5ff", "dark": "e0e0e0"}, {"name": "blot_drop_s2", "bone": "blot_drop_s3", "color": "29b3c5ff", "dark": "e0e0e0"}, {"name": "blot_drop7", "bone": "blot_drop6", "color": "29b3c5ff", "dark": "e0e0e0"}, {"name": "blot_drop8", "bone": "blot_drop_s4", "color": "29b3c5ff", "dark": "e0e0e0"}], "ik": [{"name": "legL", "bones": ["body_L2"], "target": "legL", "compress": true, "stretch": true}, {"name": "legR", "order": 1, "bones": ["body_R2"], "target": "legR", "compress": true, "stretch": true}], "transform": [{"name": "brow_tr", "order": 2, "bones": ["browL"], "target": "closed_eyeL_up", "rotation": 97.83, "x": 44.87, "y": -9.97, "mixRotate": 0.1, "mixX": 0.1, "mixScaleX": 0.1, "mixShearY": 0.1}, {"name": "closed_eyeR_up", "order": 3, "bones": ["browR"], "target": "closed_eyeR_up", "rotation": -98.83, "x": 38.28, "y": 9.11, "mixRotate": 0.1, "mixX": 0.1, "mixScaleX": 0.1, "mixShearY": 0.1}, {"name": "_tr", "order": 4, "bones": ["eye_r"], "target": "face", "x": 10.24, "y": 53.91, "mixRotate": 0.4, "mixX": 0.4, "mixScaleX": 0.4, "mixShearY": 0.4}], "skins": [{"name": "default", "attachments": {"blot": {"blot": {"type": "mesh", "uvs": [0.16853, 0.04132, 0.20427, 0.04133, 0.235, 0.05776, 0.2567, 0.08503, 0.27019, 0.11893, 0.28083, 0.15284, 0.29008, 0.16859, 0.40306, 0.14175, 0.52511, 0.14243, 0.63958, 0.17341, 0.65099, 0.15069, 0.66785, 0.11583, 0.69938, 0.10078, 0.74656, 0.0982, 0.78663, 0.11447, 0.81478, 0.14412, 0.82679, 0.17844, 0.8274, 0.22268, 0.80551, 0.24979, 0.78146, 0.26764, 0.77045, 0.28539, 0.79007, 0.31899, 0.82203, 0.31695, 0.86186, 0.3347, 0.88752, 0.35974, 0.90407, 0.39632, 0.9038, 0.43154, 0.89703, 0.47361, 0.87282, 0.49811, 0.84367, 0.51769, 0.83976, 0.57695, 0.82721, 0.62819, 0.85091, 0.63399, 0.88661, 0.64029, 0.9085, 0.64943, 0.92589, 0.66799, 0.92962, 0.68768, 0.92352, 0.71185, 0.90345, 0.72913, 0.88078, 0.7345, 0.85869, 0.73204, 0.84233, 0.71683, 0.82827, 0.69353, 0.80387, 0.68283, 0.78923, 0.70054, 0.77272, 0.72456, 0.75456, 0.74543, 0.74252, 0.76337, 0.75174, 0.78241, 0.7707, 0.79541, 0.78966, 0.80098, 0.80915, 0.81654, 0.82604, 0.83018, 0.83576, 0.85319, 0.83587, 0.87721, 0.83318, 0.90725, 0.81778, 0.92725, 0.79106, 0.94395, 0.74837, 0.94704, 0.71572, 0.94051, 0.69219, 0.91664, 0.67776, 0.88946, 0.6729, 0.86403, 0.66254, 0.84197, 0.64012, 0.83666, 0.62051, 0.84647, 0.61585, 0.88202, 0.60876, 0.90761, 0.58596, 0.93801, 0.55205, 0.95614, 0.51472, 0.9587, 0.47446, 0.95391, 0.44301, 0.93005, 0.42458, 0.90092, 0.4098, 0.87756, 0.3531, 0.86126, 0.31071, 0.8462, 0.27287, 0.82849, 0.23944, 0.82863, 0.21336, 0.82462, 0.19205, 0.80713, 0.1763, 0.78103, 0.17544, 0.7546, 0.15225, 0.73275, 0.12704, 0.75153, 0.09896, 0.78407, 0.07158, 0.81002, 0.02474, 0.81038, 0.00898, 0.79541, 0.00014, 0.76864, 0.0126, 0.74387, 0.02727, 0.723, 0.06373, 0.71112, 0.1024, 0.70314, 0.11577, 0.6871, 0.11344, 0.66789, 0.10013, 0.64319, 0.07288, 0.63049, 0.0481, 0.60493, 0.0395, 0.57932, 0.04017, 0.53824, 0.05265, 0.51498, 0.04012, 0.49584, 0.02776, 0.45571, 0.02776, 0.39855, 0.042, 0.36049, 0.07071, 0.32161, 0.10651, 0.29848, 0.14993, 0.28593, 0.17204, 0.25511, 0.157, 0.23649, 0.12839, 0.21904, 0.09915, 0.19856, 0.07437, 0.1592, 0.07434, 0.10411, 0.09293, 0.06764, 0.12711, 0.04525, 0.7637, 0.87399, 0.51839, 0.89337, 0.06159, 0.75434, 0.88018, 0.68481, 0.82945, 0.40232, 0.45472, 0.47982, 0.74434, 0.1825], "triangles": [11, 12, 123, 123, 14, 15, 111, 112, 0, 122, 96, 101, 77, 78, 122, 79, 122, 78, 122, 79, 80, 96, 97, 99, 97, 98, 99, 87, 88, 119, 116, 0, 113, 115, 116, 114, 3, 0, 2, 2, 0, 1, 117, 50, 51, 117, 52, 53, 117, 51, 52, 120, 34, 35, 120, 35, 36, 37, 120, 36, 38, 120, 37, 38, 39, 120, 120, 39, 40, 117, 58, 59, 69, 118, 68, 71, 72, 118, 70, 71, 118, 86, 119, 85, 86, 87, 119, 69, 70, 118, 57, 58, 117, 56, 57, 117, 59, 60, 117, 56, 117, 55, 40, 41, 120, 55, 117, 54, 117, 53, 54, 123, 13, 14, 116, 113, 114, 85, 119, 84, 119, 88, 89, 119, 89, 90, 3, 4, 0, 111, 0, 4, 0, 112, 113, 123, 12, 13, 41, 42, 120, 43, 44, 31, 43, 31, 42, 42, 32, 120, 83, 94, 95, 120, 33, 34, 120, 32, 33, 45, 122, 44, 101, 96, 99, 31, 122, 30, 29, 30, 122, 122, 20, 21, 122, 121, 29, 21, 22, 121, 121, 122, 21, 29, 121, 28, 101, 108, 122, 108, 103, 107, 106, 107, 105, 101, 103, 108, 105, 107, 103, 28, 121, 27, 101, 102, 103, 105, 103, 104, 20, 9, 123, 20, 122, 9, 9, 10, 123, 10, 11, 123, 108, 109, 122, 109, 6, 122, 6, 7, 122, 122, 8, 9, 122, 7, 8, 27, 121, 26, 26, 121, 25, 121, 24, 25, 121, 23, 24, 121, 22, 23, 20, 123, 19, 19, 123, 18, 109, 110, 6, 18, 123, 17, 110, 5, 6, 110, 4, 5, 4, 110, 111, 123, 16, 17, 123, 15, 16, 31, 44, 122, 95, 96, 122, 101, 99, 100, 65, 74, 122, 122, 74, 75, 64, 65, 122, 76, 122, 75, 47, 64, 122, 77, 122, 76, 122, 80, 81, 81, 82, 122, 47, 122, 46, 82, 83, 122, 46, 122, 45, 122, 83, 95, 42, 31, 32, 68, 118, 67, 72, 73, 118, 60, 61, 117, 67, 118, 66, 73, 74, 118, 66, 118, 65, 61, 62, 117, 65, 118, 74, 62, 48, 117, 117, 49, 50, 117, 48, 49, 62, 63, 48, 48, 63, 47, 63, 64, 47, 93, 119, 92, 119, 93, 84, 90, 91, 119, 119, 91, 92, 84, 93, 83, 93, 94, 83], "vertices": [2, 20, -90.94, 159.52, 0.02155, 21, -90.94, 170.2, 0.97845, 2, 20, -79.44, 159.51, 0.00946, 21, -79.44, 170.19, 0.99054, 2, 20, -69.48, 154.05, 0.153, 21, -69.48, 164.72, 0.847, 2, 20, -62.37, 144.98, 0.39795, 21, -62.37, 155.65, 0.60205, 2, 20, -57.88, 133.7, 0.70584, 21, -57.88, 144.37, 0.29416, 2, 20, -54.34, 122.38, 0.93146, 21, -54.34, 133.06, 0.06854, 2, 20, -51.35, 117.08, 0.95362, 21, -51.35, 127.76, 0.04638, 2, 20, -15.07, 126.03, 0.7559, 21, -15.07, 136.71, 0.2441, 2, 20, 24.24, 125.81, 0.76179, 21, 24.24, 136.48, 0.23821, 2, 20, 61.09, 115.36, 0.74063, 21, 61.09, 126.03, 0.25937, 2, 20, 64.71, 122.97, 0.64513, 21, 64.71, 133.64, 0.35487, 2, 20, 70.01, 134.58, 0.36729, 21, 70.01, 145.26, 0.63271, 2, 20, 80.08, 139.57, 0.19886, 21, 80.08, 150.24, 0.80114, 2, 20, 95.22, 140.39, 0.10171, 21, 95.22, 151.07, 0.89829, 2, 20, 108.17, 134.96, 0.19647, 21, 108.17, 145.63, 0.80353, 2, 20, 117.31, 125.04, 0.36288, 21, 117.31, 135.72, 0.63712, 2, 20, 121.23, 113.53, 0.46208, 21, 121.23, 124.2, 0.53792, 2, 20, 121.49, 98.68, 0.59174, 21, 121.49, 109.36, 0.40826, 2, 20, 114.52, 89.62, 0.74078, 21, 114.52, 100.29, 0.25922, 2, 20, 106.83, 83.66, 0.86799, 21, 106.83, 94.34, 0.13201, 1, 20, 103.35, 77.75, 1, 1, 20, 109.67, 66.42, 1, 2, 20, 119.95, 67.1, 0.98349, 21, 119.95, 77.78, 0.01651, 2, 20, 132.7, 61.04, 0.81787, 21, 132.7, 71.72, 0.18213, 2, 20, 140.9, 52.55, 0.70315, 21, 140.9, 63.22, 0.29685, 2, 20, 146.2, 40.18, 0.62876, 21, 146.2, 50.86, 0.37124, 2, 20, 146.1, 28.3, 0.60882, 21, 146.1, 38.98, 0.39118, 2, 20, 143.93, 14.14, 0.63067, 21, 143.93, 24.81, 0.36933, 2, 20, 136.19, 5.93, 0.74414, 21, 136.19, 16.61, 0.25586, 2, 20, 126.87, -0.59, 0.88743, 21, 126.87, 10.08, 0.11257, 1, 20, 125.67, -20.51, 1, 2, 20, 121.59, -37.81, 0.92415, 21, 121.59, -27.14, 0.07585, 2, 20, 129.19, -39.8, 0.85626, 21, 129.19, -29.13, 0.14374, 2, 20, 140.59, -42.03, 0.65141, 21, 140.59, -31.35, 0.34859, 2, 20, 147.54, -45.2, 0.45337, 21, 147.54, -34.52, 0.54663, 2, 20, 153.03, -51.57, 0.22414, 21, 153.03, -40.89, 0.77586, 2, 20, 154.18, -58.25, 0.12863, 21, 154.18, -47.57, 0.87137, 2, 20, 152.19, -66.42, 0.06435, 21, 152.19, -55.75, 0.93565, 2, 20, 145.73, -72.24, 0.0759, 21, 145.73, -61.57, 0.9241, 2, 20, 138.45, -74.04, 0.11319, 21, 138.45, -63.36, 0.88681, 2, 20, 131.39, -73.15, 0.22246, 21, 131.39, -62.48, 0.77754, 2, 20, 126.21, -67.93, 0.41853, 21, 126.21, -57.26, 0.58147, 2, 20, 121.81, -59.96, 0.66246, 21, 121.81, -49.29, 0.33754, 2, 20, 114.11, -56.19, 0.99426, 21, 114.11, -45.52, 0.00574, 2, 20, 109.4, -62.16, 0.99314, 21, 109.4, -51.49, 0.00686, 2, 20, 104.08, -70.26, 0.99213, 21, 104.08, -59.58, 0.00787, 2, 20, 98.23, -77.29, 0.99754, 21, 98.23, -66.61, 0.00246, 2, 20, 94.17, -83.52, 0.60983, 21, 94.17, -72.85, 0.39017, 2, 20, 97.17, -89.91, 0.67939, 21, 97.17, -79.23, 0.32061, 2, 20, 103.23, -94.33, 0.58167, 21, 103.23, -83.66, 0.41833, 2, 20, 109.27, -96.28, 0.43988, 21, 109.27, -85.61, 0.56012, 2, 20, 115.48, -101.58, 0.3264, 21, 115.48, -90.9, 0.6736, 2, 20, 120.87, -106.23, 0.20757, 21, 120.87, -95.56, 0.79243, 2, 20, 124, -113.98, 0.2256, 21, 124, -103.3, 0.7744, 2, 20, 124.09, -122.02, 0.33257, 21, 124.09, -111.35, 0.66743, 2, 20, 123.2, -132.17, 0.27292, 21, 123.2, -121.5, 0.72708, 2, 20, 118.17, -138.98, 0.13068, 21, 118.17, -128.31, 0.86932, 1, 21, 109.5, -134, 1, 1, 21, 95.76, -135.04, 1, 2, 20, 85.29, -143.47, 0.09602, 21, 85.29, -132.79, 0.90398, 2, 20, 77.86, -135.28, 0.39368, 21, 77.86, -124.6, 0.60632, 2, 20, 73.36, -125.97, 0.70124, 21, 73.36, -115.29, 0.29876, 2, 20, 71.88, -117.32, 0.86966, 21, 71.88, -106.64, 0.13034, 2, 20, 68.58, -109.84, 0.95079, 21, 68.58, -99.17, 0.04921, 2, 20, 61.37, -108.04, 0.97473, 21, 61.37, -97.37, 0.02527, 2, 20, 55.05, -111.36, 0.95012, 21, 55.05, -100.69, 0.04988, 2, 20, 53.49, -123.4, 0.83978, 21, 53.49, -112.72, 0.16022, 2, 20, 51.17, -132.06, 0.7483, 21, 51.17, -121.39, 0.2517, 2, 20, 43.71, -142.42, 0.50798, 21, 43.71, -131.75, 0.49202, 2, 20, 32.64, -148.68, 0.20256, 21, 32.64, -138.01, 0.79744, 2, 20, 20.52, -149.64, 0.00552, 21, 20.52, -138.97, 0.99448, 1, 21, 7.56, -137.36, 1, 2, 20, -2.42, -139.85, 0.29991, 21, -2.42, -129.17, 0.70009, 2, 20, -8.2, -129.87, 0.61942, 21, -8.2, -119.2, 0.38058, 2, 20, -12.9, -121.94, 0.74609, 21, -12.9, -111.26, 0.25391, 2, 20, -31.12, -116.41, 0.81636, 21, -31.12, -105.74, 0.18364, 2, 20, -44.86, -111.42, 0.63451, 21, -44.86, -100.75, 0.36549, 2, 20, -57.07, -105.48, 0.59263, 21, -57.07, -94.8, 0.40737, 2, 20, -67.88, -105.57, 0.49938, 21, -67.88, -94.89, 0.50062, 2, 20, -76.3, -104.24, 0.44983, 21, -76.3, -93.57, 0.55017, 2, 20, -83.11, -98.3, 0.5575, 21, -83.11, -87.62, 0.4425, 2, 20, -88.05, -89.38, 0.81245, 21, -88.05, -78.7, 0.18755, 2, 20, -88.25, -80.38, 0.9861, 21, -88.25, -69.71, 0.0139, 2, 20, -95.71, -73.02, 0.99035, 21, -95.71, -62.34, 0.00965, 2, 20, -104.06, -79.57, 0.52724, 21, -104.06, -68.9, 0.47276, 1, 21, -113.36, -80.12, 1, 1, 21, -122.17, -88.86, 1, 1, 21, -137.25, -88.99, 1, 2, 20, -142.29, -94.57, 0.08531, 21, -142.29, -83.9, 0.91469, 2, 20, -145.01, -85.43, 0.33535, 21, -145.01, -74.76, 0.66465, 2, 20, -140.94, -77.02, 0.46148, 21, -140.94, -66.35, 0.53852, 2, 20, -136.17, -69.94, 0.55661, 21, -136.17, -59.27, 0.44339, 2, 20, -124.36, -65.88, 0.68332, 21, -124.36, -55.2, 0.31668, 2, 20, -111.87, -63.14, 0.77501, 21, -111.87, -52.47, 0.22499, 2, 20, -107.53, -57.71, 0.83963, 21, -107.53, -47.03, 0.16037, 1, 20, -108.21, -51.16, 1, 2, 20, -112.78, -43.12, 0.39981, 21, -112.78, -32.45, 0.60019, 2, 20, -121.59, -38.88, 0.33627, 21, -121.59, -28.2, 0.66373, 2, 20, -129.49, -30.18, 0.50107, 21, -129.49, -19.51, 0.49893, 2, 20, -132.2, -21.49, 0.61634, 21, -132.2, -10.82, 0.38366, 2, 20, -131.96, -7.63, 0.65964, 21, -131.96, 3.05, 0.34036, 2, 20, -127.95, 0.2, 0.65438, 21, -127.95, 10.88, 0.34562, 2, 20, -131.97, 6.67, 0.6746, 21, -131.97, 17.34, 0.3254, 2, 20, -135.84, 20.3, 0.90838, 21, -135.84, 30.98, 0.09162, 2, 20, -135.89, 39.52, 0.80863, 21, -135.89, 50.19, 0.19137, 2, 20, -131.35, 52.3, 0.71293, 21, -131.35, 62.98, 0.28707, 2, 20, -122.12, 65.39, 0.68699, 21, -122.12, 76.06, 0.31301, 2, 20, -110.54, 73.23, 0.78083, 21, -110.54, 83.9, 0.21917, 2, 20, -96.48, 77.54, 0.94418, 21, -96.48, 88.21, 0.05582, 2, 20, -89.49, 87.79, 0.67426, 21, -89.49, 98.47, 0.32574, 2, 20, -94.34, 94.07, 0.67915, 21, -94.34, 104.75, 0.32085, 2, 20, -103.62, 99.88, 0.53234, 21, -103.62, 110.56, 0.46766, 2, 20, -113.02, 106.79, 0.55156, 21, -113.02, 117.47, 0.44844, 2, 20, -121.13, 119.92, 0.28889, 21, -121.13, 130.6, 0.71111, 2, 20, -121.26, 138.37, 0.03582, 21, -121.26, 149.04, 0.96418, 1, 21, -115.3, 161.32, 1, 1, 21, -104.29, 168.86, 1, 2, 20, 101.04, -120.75, 0.71328, 21, 101.04, -110.08, 0.28672, 2, 20, 22.05, -127.28, 0.72168, 21, 22.05, -116.6, 0.27832, 2, 20, -125.15, -80.54, 0.48636, 21, -125.15, -69.86, 0.51364, 2, 20, 138.43, -57.11, 0.48188, 21, 138.43, -46.43, 0.51812, 2, 20, 122.3, 38.3, 0.90859, 21, 122.3, 48.97, 0.09141, 2, 20, 1.35, 11.89, 0.31422, 21, 1.35, 22.57, 0.68578, 2, 20, 94.85, 112.33, 0.81578, 21, 94.85, 123.01, 0.18422], "hull": 117, "edges": [10, 12, 12, 14, 14, 16, 16, 18, 22, 24, 24, 26, 34, 36, 58, 60, 60, 62, 72, 74, 152, 154, 172, 174, 174, 176, 176, 178, 210, 212, 218, 220, 224, 226, 226, 228, 228, 230, 230, 232, 166, 168, 168, 170, 170, 172, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 200, 202, 202, 204, 196, 198, 198, 200, 158, 160, 160, 162, 154, 156, 156, 158, 162, 164, 164, 166, 146, 148, 142, 144, 144, 146, 140, 142, 134, 136, 132, 134, 136, 138, 138, 140, 128, 130, 130, 132, 126, 128, 122, 124, 124, 126, 118, 120, 120, 122, 114, 116, 116, 118, 110, 112, 112, 114, 108, 110, 178, 180, 180, 182, 102, 104, 100, 102, 94, 96, 104, 106, 106, 108, 96, 98, 98, 100, 90, 92, 92, 94, 86, 88, 88, 90, 82, 84, 84, 86, 74, 76, 76, 78, 78, 80, 80, 82, 68, 70, 70, 72, 62, 64, 64, 66, 66, 68, 54, 56, 56, 58, 50, 52, 52, 54, 46, 48, 48, 50, 44, 46, 244, 60, 40, 42, 42, 44, 36, 38, 38, 40, 30, 32, 32, 34, 26, 28, 28, 30, 18, 20, 20, 22, 40, 244, 148, 150, 150, 152, 156, 244, 160, 244, 162, 244, 158, 244, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 232, 220, 222, 222, 224, 216, 218, 212, 214, 214, 216, 208, 210, 206, 208, 204, 206, 244, 202], "width": 322, "height": 337}}, "blot_drop2": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop3": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop4": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop5": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop6": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop7": {"blot_drop2": {"width": 63, "height": 52}}, "blot_drop8": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s1": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "blot_drop_s2": {"blot_drop1": {"rotation": -0.04, "width": 30, "height": 29}}, "body": {"body": {"type": "mesh", "uvs": [0.88135, 0.02842, 0.92447, 0.07002, 0.94595, 0.1091, 0.96556, 0.16664, 0.98402, 0.25149, 0.9944, 0.3482, 0.99844, 0.48329, 0.99314, 0.62102, 0.97894, 0.70389, 0.95603, 0.76961, 0.92472, 0.82508, 0.89403, 0.86604, 0.83773, 0.91457, 0.7738, 0.94388, 0.69708, 0.9682, 0.60419, 0.98592, 0.5065, 0.99659, 0.39202, 0.98615, 0.29509, 0.96334, 0.21598, 0.93436, 0.16911, 0.90629, 0.12268, 0.87348, 0.07585, 0.82419, 0.03795, 0.75025, 0.0169, 0.68762, 0.00435, 0.60888, 0.00218, 0.47344, 0.00377, 0.3396, 0.014, 0.26023, 0.0316, 0.18742, 0.05847, 0.12556, 0.09043, 0.07496, 0.14346, 0.03606, 0.19968, 0.01591, 0.28505, 0.0095, 0.36994, 0.01228, 0.4372, 0.02402, 0.50365, 0.04089, 0.55106, 0.05309, 0.59748, 0.06044, 0.6743, 0.03716, 0.74984, 0.00923, 0.79466, 0, 0.8401, 0.00659, 0.13851, 0.51102, 0.50228, 0.53986, 0.90005, 0.49661], "triangles": [13, 14, 46, 12, 13, 46, 46, 40, 41, 11, 12, 46, 43, 46, 42, 42, 46, 41, 11, 46, 10, 0, 46, 43, 10, 46, 9, 9, 46, 8, 8, 46, 7, 7, 46, 6, 46, 5, 6, 46, 4, 5, 46, 3, 4, 46, 2, 3, 46, 1, 2, 46, 0, 1, 20, 34, 19, 33, 34, 44, 21, 44, 20, 34, 20, 44, 33, 44, 32, 21, 22, 44, 22, 23, 44, 23, 24, 44, 24, 25, 44, 25, 26, 44, 26, 27, 44, 27, 28, 44, 28, 29, 44, 29, 30, 44, 30, 31, 44, 44, 31, 32, 15, 16, 45, 45, 16, 17, 17, 18, 45, 15, 45, 14, 45, 46, 14, 45, 39, 40, 18, 19, 45, 40, 46, 45, 45, 19, 34, 45, 35, 36, 45, 34, 35, 45, 38, 39, 45, 37, 38, 45, 36, 37], "vertices": [3, 2, 140.28, -115.39, 0.15667, 3, 67.06, -223.16, 0.00015, 4, 67.06, 4.89, 0.84318, 3, 2, 134.28, -128.22, 0.09547, 3, 61.06, -235.99, 0.00012, 4, 61.06, -7.94, 0.90441, 3, 2, 128.71, -134.59, 0.06948, 3, 55.48, -242.35, 9e-05, 4, 55.48, -14.3, 0.93043, 3, 2, 120.53, -140.37, 0.04349, 3, 47.31, -248.13, 7e-05, 4, 47.31, -20.08, 0.95644, 3, 2, 108.51, -145.76, 0.02317, 3, 35.29, -253.52, 5e-05, 4, 35.29, -25.48, 0.97678, 3, 2, 94.84, -148.72, 0.00286, 3, 21.62, -256.49, 2e-05, 4, 21.62, -28.44, 0.99712, 3, 2, 75.78, -149.73, 0.00181, 3, 2.56, -257.49, 1e-05, 4, 2.56, -29.45, 0.99818, 2, 2, 56.38, -147.94, 0.00076, 4, -16.84, -27.66, 0.99924, 3, 2, 44.74, -143.58, 0.0186, 3, -28.48, -251.34, 0, 4, -28.48, -23.29, 0.9814, 3, 2, 35.55, -136.63, 0.03644, 3, -37.68, -244.39, 0, 4, -37.68, -16.35, 0.96356, 3, 2, 27.82, -127.19, 0.08425, 3, -45.4, -234.95, 1e-05, 4, -45.4, -6.9, 0.91574, 3, 2, 22.14, -117.95, 0.13206, 3, -51.08, -225.71, 1e-05, 4, -51.08, 2.33, 0.86793, 3, 2, 15.48, -101.05, 0.31332, 3, -57.75, -208.81, 4e-05, 4, -57.75, 19.23, 0.68664, 3, 2, 11.54, -81.89, 0.43433, 3, -61.68, -189.66, 6e-05, 4, -61.68, 38.39, 0.56561, 3, 2, 8.35, -58.92, 0.63353, 3, -64.87, -166.68, 0.0001, 4, -64.87, 61.37, 0.36637, 3, 2, 6.15, -31.12, 0.83274, 3, -67.08, -138.88, 0.00013, 4, -67.08, 89.17, 0.16713, 3, 2, 4.95, -1.89, 0.83723, 3, -68.28, -109.66, 0.07917, 4, -68.28, 118.39, 0.0836, 3, 2, 6.77, 32.32, 0.84171, 3, -66.45, -75.45, 0.15821, 4, -66.45, 152.6, 7e-05, 3, 2, 10.29, 61.26, 0.53531, 3, -62.93, -46.5, 0.46464, 4, -62.93, 181.55, 4e-05, 3, 2, 14.62, 84.87, 0.32232, 3, -58.6, -22.89, 0.67766, 4, -58.6, 205.16, 2e-05, 3, 2, 18.73, 98.85, 0.21071, 3, -54.5, -8.92, 0.78928, 4, -54.5, 219.13, 1e-05, 3, 2, 23.5, 112.68, 0.09909, 3, -49.73, 4.92, 0.9009, 4, -49.73, 232.96, 0, 3, 2, 30.59, 126.61, 0.05239, 3, -42.63, 18.85, 0.94761, 4, -42.63, 246.89, 0, 2, 2, 41.14, 137.83, 0.00568, 3, -32.09, 30.07, 0.99432, 3, 2, 50.03, 144.03, 0.00284, 3, -23.19, 36.27, 0.99716, 4, -23.19, 264.32, 0, 2, 3, -12.05, 39.91, 1, 4, -12.05, 267.95, 0, 3, 2, 80.28, 148.12, 0.00075, 3, 7.05, 40.36, 0.99923, 4, 7.05, 268.4, 1e-05, 3, 2, 99.14, 147.45, 0.00151, 3, 25.92, 39.68, 0.99847, 4, 25.92, 267.73, 3e-05, 3, 2, 110.3, 144.27, 0.01312, 3, 37.07, 36.51, 0.98684, 4, 37.07, 264.56, 4e-05, 3, 2, 120.51, 138.9, 0.02474, 3, 47.29, 31.14, 0.9752, 4, 47.29, 259.19, 6e-05, 3, 2, 129.15, 130.78, 0.05664, 3, 55.92, 23.02, 0.94328, 4, 55.92, 251.06, 8e-05, 3, 2, 136.18, 121.15, 0.08854, 3, 62.96, 13.39, 0.91135, 4, 62.96, 241.43, 0.0001, 3, 2, 141.5, 105.24, 0.18881, 3, 68.28, -2.53, 0.81104, 4, 68.28, 225.52, 0.00015, 3, 2, 144.17, 88.4, 0.28908, 3, 70.94, -19.37, 0.71072, 4, 70.94, 208.68, 0.0002, 3, 2, 144.81, 62.86, 0.53288, 3, 71.58, -44.9, 0.46683, 4, 71.58, 183.15, 0.00029, 3, 2, 144.15, 37.49, 0.77668, 3, 70.92, -70.27, 0.22295, 4, 70.92, 157.77, 0.00038, 3, 2, 142.29, 17.4, 0.8876, 3, 69.06, -90.37, 0.11147, 4, 69.06, 137.68, 0.00093, 2, 2, 139.7, -2.45, 0.99852, 4, 66.47, 117.83, 0.00148, 3, 2, 137.83, -16.6, 0.92238, 3, 64.61, -124.37, 0.00021, 4, 64.61, 103.68, 0.07741, 3, 2, 136.65, -30.47, 0.84624, 3, 63.43, -138.23, 0.00042, 4, 63.43, 89.81, 0.15334, 3, 2, 139.7, -53.47, 0.6254, 3, 66.47, -161.24, 0.00035, 4, 66.47, 66.81, 0.37426, 3, 2, 143.4, -76.1, 0.40456, 3, 70.17, -183.86, 0.00027, 4, 70.17, 44.18, 0.59517, 3, 2, 144.56, -89.51, 0.31122, 3, 71.33, -197.28, 0.00023, 4, 71.33, 30.77, 0.68855, 3, 2, 143.49, -103.09, 0.21787, 3, 70.26, -210.85, 0.00019, 4, 70.26, 17.19, 0.78194, 3, 2, 74.55, 107.41, 0.00227, 3, 1.33, -0.35, 0.99773, 4, 1.33, 227.7, 0, 2, 2, 69.35, -1.31, 0.99875, 4, -3.87, 118.98, 0.00125, 3, 2, 74.21, -120.29, 0.00135, 3, 0.99, -228.06, 0, 4, 0.99, -0.01, 0.99865], "hull": 44, "edges": [58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 2, 0, 0, 86, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58], "width": 299, "height": 141}}, "body_outline": {"body_outline": {"type": "mesh", "uvs": [0.31011, 0.01883, 0.38772, 0.02107, 0.49464, 0.04527, 0.55539, 0.06153, 0.62466, 0.06153, 0.69478, 0.02764, 0.7556, 0.012, 0.82444, 0.01286, 0.89285, 0.05284, 0.93056, 0.09898, 0.96648, 0.18936, 0.98358, 0.29364, 1, 0.40507, 1, 0.55608, 0.98547, 0.68411, 0.94832, 0.78916, 0.8918, 0.87021, 0.82852, 0.91823, 0.7104, 0.97139, 0.57371, 1, 0.40211, 1, 0.26965, 0.96038, 0.14899, 0.89708, 0.06385, 0.80336, 0.02483, 0.70723, 0, 0.56303, 0, 0.37867, 0.01911, 0.24424, 0.04997, 0.15239, 0.09516, 0.08517, 0.16351, 0.03588, 0.23339, 0.01839, 0.17197, 0.50312, 0.51012, 0.54163, 0.87305, 0.41424], "triangles": [34, 18, 33, 17, 18, 34, 4, 34, 33, 34, 4, 5, 34, 5, 6, 17, 34, 16, 7, 34, 6, 16, 34, 15, 15, 34, 14, 14, 34, 13, 34, 12, 13, 34, 11, 12, 34, 10, 11, 34, 9, 10, 34, 8, 9, 34, 7, 8, 22, 32, 21, 21, 32, 33, 33, 32, 0, 32, 31, 0, 22, 23, 32, 23, 24, 32, 24, 25, 32, 25, 26, 32, 26, 27, 32, 27, 28, 32, 28, 29, 32, 29, 30, 32, 32, 30, 31, 20, 33, 19, 19, 33, 18, 33, 3, 4, 20, 21, 33, 33, 1, 2, 1, 33, 0, 33, 2, 3], "vertices": [3, 2, 150.2, 57.97, 0.27065, 3, 76.97, -49.79, 0.69117, 4, 76.97, 178.26, 0.03818, 3, 2, 149.6, 33.69, 0.39345, 3, 76.37, -74.08, 0.52175, 4, 76.37, 153.97, 0.0848, 3, 2, 145.52, 0.26, 0.49695, 3, 72.3, -107.5, 0.2956, 4, 72.3, 120.54, 0.20745, 3, 2, 142.82, -18.73, 0.4907, 3, 69.6, -126.49, 0.18616, 4, 69.6, 101.56, 0.32314, 3, 2, 142.59, -40.41, 0.41055, 3, 69.37, -148.17, 0.09318, 4, 69.37, 79.88, 0.49626, 3, 2, 147.59, -62.41, 0.2872, 3, 74.36, -170.17, 0.04086, 4, 74.36, 57.87, 0.67194, 3, 2, 149.8, -81.47, 0.19306, 3, 76.57, -189.23, 0.0192, 4, 76.57, 38.82, 0.78773, 3, 2, 149.44, -103.01, 0.10769, 3, 76.21, -210.78, 0.00683, 4, 76.21, 17.27, 0.88548, 3, 2, 143.06, -124.36, 0.0472, 3, 69.83, -232.12, 0.00146, 4, 69.83, -4.08, 0.95133, 3, 2, 135.83, -136.09, 0.02462, 3, 62.61, -243.85, 0.00032, 4, 62.61, -15.81, 0.97506, 2, 2, 121.8, -147.19, 0.00703, 4, 48.57, -26.9, 0.99297, 2, 2, 105.68, -152.37, 0.00033, 4, 32.46, -32.09, 0.99967, 1, 4, 15.24, -37.05, 1, 1, 4, -8.01, -36.81, 1, 2, 2, 45.55, -152.34, 0.00861, 4, -27.68, -32.05, 0.99139, 2, 2, 29.49, -140.54, 0.04648, 4, -43.74, -20.26, 0.95352, 2, 2, 17.19, -122.72, 0.13308, 4, -56.03, -2.44, 0.86692, 2, 2, 10.01, -102.84, 0.26605, 4, -63.22, 17.45, 0.73395, 3, 2, 2.21, -65.78, 0.59143, 3, -71.02, -173.55, 0, 4, -71.02, 54.5, 0.40857, 3, 2, -1.76, -22.96, 0.92646, 3, -74.98, -130.72, 4e-05, 4, -74.98, 97.33, 0.0735, 3, 2, -1.2, 30.75, 0.86618, 3, -74.42, -77.01, 0.13379, 4, -74.42, 151.04, 4e-05, 3, 2, 5.34, 72.15, 0.47949, 3, -67.89, -35.61, 0.52051, 4, -67.89, 192.43, 0, 2, 2, 15.48, 109.81, 0.15428, 3, -57.75, 2.05, 0.84572, 2, 2, 30.19, 136.31, 0.03041, 3, -43.04, 28.54, 0.96959, 2, 2, 45.12, 148.37, 0.00471, 3, -28.11, 40.6, 0.99529, 1, 3, -5.82, 48.14, 1, 1, 3, 22.57, 47.85, 1, 2, 2, 116.43, 149.42, 0.00022, 3, 43.21, 41.65, 0.99978, 3, 2, 130.48, 139.61, 0.00547, 3, 57.25, 31.84, 0.99453, 4, 57.25, 259.89, 0, 3, 2, 140.68, 125.35, 0.02278, 3, 67.45, 17.59, 0.97667, 4, 67.45, 245.64, 0.00055, 3, 2, 148.05, 103.89, 0.07118, 3, 74.82, -3.88, 0.92469, 4, 74.82, 224.17, 0.00412, 3, 2, 150.51, 81.99, 0.15084, 3, 77.29, -25.78, 0.83527, 4, 77.29, 202.27, 0.01389, 3, 2, 76.07, 101.99, 0.02374, 3, 2.84, -5.78, 0.97606, 4, 2.84, 222.27, 0.0002, 3, 2, 69.04, -3.79, 0.78448, 3, -4.19, -111.55, 0.10229, 4, -4.19, 116.5, 0.11322, 3, 2, 87.47, -117.58, 0.0191, 3, 14.24, -225.35, 0.00029, 4, 14.24, 2.7, 0.98062], "hull": 32, "edges": [52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 2, 4, 62, 0, 0, 2, 4, 6, 6, 8, 8, 10, 10, 12, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 48, 50, 12, 14], "width": 313, "height": 154}}, "brow_l": {"brow_l": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 12, 43.82, -5.37, 1, 1, 12, -3.69, -12.25, 1, 1, 12, -6.41, 6.55, 1, 1, 12, 41.1, 13.43, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 19}}, "brow_r": {"brow_r": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 13, -0.72, 12.21, 1, 1, 13, 46.76, 5.17, 1, 1, 13, 43.97, -13.62, 1, 1, 13, -3.51, -6.59, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 19}}, "eyelid_l_l": {"eyelid_l_l": {"type": "mesh", "uvs": [0.7883, 0.05134, 0.94078, 0.1784, 0.97145, 0.30781, 0.94411, 0.45369, 0.80278, 0.77604, 0.63545, 0.88428, 0.29748, 0.8871, 0.20814, 0.80239, 0.08681, 0.66592, 0.02119, 0.47016, 0.11486, 0.34428, 0.20853, 0.2184, 0.35386, 0.1031, 0.44986, 0.05604, 0.10548, 0.56239, 0.21881, 0.63769, 0.31348, 0.69886, 0.62281, 0.68475, 0.77881, 0.55769, 0.90148, 0.35533], "triangles": [0, 17, 13, 16, 12, 13, 5, 16, 17, 5, 6, 16, 7, 15, 6, 6, 15, 16, 5, 18, 4, 5, 17, 18, 8, 14, 7, 7, 14, 15, 4, 19, 3, 4, 18, 19, 16, 13, 17, 12, 16, 11, 18, 17, 0, 8, 9, 14, 14, 10, 15, 10, 11, 15, 16, 15, 11, 14, 9, 10, 18, 0, 19, 3, 19, 2, 19, 1, 2, 19, 0, 1], "vertices": [2, 6, 18.33, -19.32, 0.23428, 8, 2.32, -19.32, 0.76572, 2, 6, 16.08, -28.45, 0.71429, 8, 0.06, -28.45, 0.28571, 1, 6, 13.86, -30.26, 1, 1, 6, 11.39, -28.6, 1, 1, 6, 6, -20.06, 1, 1, 6, 4.27, -10, 1, 1, 6, 4.43, 10.28, 1, 1, 6, 5.93, 15.62, 1, 1, 6, 8.32, 22.88, 1, 1, 6, 11.69, 26.78, 1, 2, 6, 13.77, 21.14, 0.71429, 8, -2.24, 21.14, 0.28571, 2, 6, 15.85, 15.49, 0.49143, 8, -0.16, 15.49, 0.50857, 2, 6, 17.72, 6.75, 0.36, 8, 1.71, 6.75, 0.64, 2, 6, 18.46, 0.99, 0.23428, 8, 2.45, 0.99, 0.76572, 2, 6, 10.07, 21.74, 0.88571, 8, -5.94, 21.74, 0.11429, 2, 6, 8.72, 14.95, 0.88571, 8, -7.29, 14.95, 0.11429, 2, 6, 7.62, 9.28, 0.88571, 8, -8.39, 9.28, 0.11429, 2, 6, 7.67, -9.28, 0.88571, 8, -8.35, -9.28, 0.11429, 2, 6, 9.73, -18.66, 0.88571, 8, -6.28, -18.66, 0.11429, 2, 6, 13.09, -26.06, 0.88571, 8, -2.92, -26.06, 0.11429], "hull": 14, "edges": [22, 24, 24, 26, 26, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 18, 20, 20, 22, 16, 18, 18, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 4], "width": 60, "height": 17}}, "eyelid_l_r": {"eyelid_l_r": {"type": "mesh", "uvs": [0.83641, 0.09476, 0.91118, 0.16388, 1, 0.30073, 0.88328, 0.611, 0.76438, 0.79928, 0.63134, 0.89647, 0.389, 0.91765, 0.21199, 0.85014, 0.03463, 0.57971, 0.03319, 0.46692, 0.2243, 0.20298, 0.39879, 0, 0.61031, 0, 0.73392, 0, 0.22498, 0.58419, 0.39832, 0.57948, 0.73565, 0.51831, 0.6341, 0.53672, 0.86782, 0.40952], "triangles": [15, 11, 12, 17, 12, 13, 16, 13, 0, 15, 10, 11, 18, 0, 1, 14, 9, 10, 7, 14, 6, 6, 15, 5, 6, 14, 15, 5, 16, 4, 5, 17, 16, 5, 15, 17, 7, 8, 14, 3, 16, 18, 3, 4, 16, 3, 18, 2, 14, 10, 15, 14, 8, 9, 15, 12, 17, 17, 13, 16, 16, 0, 18, 18, 1, 2], "vertices": [2, 9, 16.1, -17.13, 0.3346, 11, 3.02, -17.13, 0.6654, 2, 9, 14.88, -21.6, 0.5027, 11, 1.79, -21.6, 0.4973, 2, 9, 12.5, -26.91, 0.77607, 11, -0.59, -26.91, 0.22393, 2, 9, 7.3, -19.85, 0.91857, 11, -5.79, -19.85, 0.08143, 1, 9, 4.17, -12.68, 1, 1, 9, 2.6, -4.68, 1, 1, 9, 2.4, 9.86, 1, 2, 9, 3.65, 20.47, 0.98476, 11, -9.43, 20.47, 0.01524, 2, 9, 8.36, 31.06, 0.8919, 11, -4.73, 31.06, 0.1081, 2, 9, 10.28, 31.13, 0.73702, 11, -2.81, 31.13, 0.26298, 2, 9, 14.65, 19.62, 0.47603, 11, 1.56, 19.62, 0.52397, 2, 9, 17.99, 9.11, 0.31937, 11, 4.9, 9.11, 0.68063, 2, 9, 17.86, -3.58, 0.25143, 11, 4.77, -3.58, 0.74857, 2, 9, 17.78, -11, 0.25143, 11, 4.69, -11, 0.74857, 2, 9, 8.17, 19.64, 0.76929, 11, -4.92, 19.64, 0.23071, 2, 9, 8.14, 9.24, 0.77143, 11, -4.95, 9.24, 0.22857, 2, 9, 8.97, -11.01, 0.79683, 11, -4.12, -11.01, 0.20317, 2, 9, 8.72, -4.91, 0.77143, 11, -4.37, -4.91, 0.22857, 2, 9, 10.73, -18.96, 0.79159, 11, -2.35, -18.96, 0.20841], "hull": 14, "edges": [18, 20, 20, 22, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 28, 30, 34, 34, 32, 22, 24, 24, 26, 2, 0, 0, 26, 4, 36, 36, 32], "width": 60, "height": 17}}, "eyelid_u_l": {"eyelid_u_l": {"type": "mesh", "uvs": [0.83561, 0.18554, 0.94423, 0.34535, 1, 0.60244, 1, 0.80122, 0.88992, 0.89427, 0.73907, 0.94441, 0.59425, 0.95831, 0.42228, 0.94441, 0.22617, 0.91511, 0.07834, 0.88732, 1e-05, 0.79864, 1e-05, 0.59728, 0.06627, 0.34535, 0.18695, 0.14385, 0.40116, 0, 0.61235, 0, 0.12057, 0.35925, 0.23798, 0.37197, 0.40719, 0.37862, 0.59136, 0.37862, 0.74208, 0.39281, 0.84768, 0.39369], "triangles": [5, 6, 19, 5, 21, 4, 6, 7, 19, 8, 18, 7, 9, 16, 8, 10, 11, 9, 2, 4, 21, 5, 19, 20, 16, 11, 12, 2, 21, 1, 20, 0, 21, 21, 0, 1, 0, 20, 15, 17, 14, 18, 18, 14, 19, 20, 19, 15, 19, 14, 15, 16, 13, 17, 17, 13, 14, 16, 12, 13, 8, 17, 18, 8, 16, 17, 7, 18, 19, 5, 20, 21, 9, 11, 16, 3, 4, 2], "vertices": [1, 6, 45.02, -25.81, 1, 1, 6, 39.66, -34.01, 1, 2, 6, 31.13, -38.16, 0.95663, 7, 6.31, -38.16, 0.04337, 2, 6, 24.57, -38.09, 0.55836, 7, -0.25, -38.09, 0.44164, 2, 6, 21.59, -29.69, 0.24245, 7, -3.23, -29.69, 0.75755, 2, 6, 20.05, -18.21, 0.22208, 7, -4.77, -18.21, 0.77792, 2, 6, 19.71, -7.2, 0.18873, 7, -5.11, -7.2, 0.81127, 2, 6, 20.3, 5.87, 0.20078, 7, -4.52, 5.87, 0.79922, 2, 6, 21.42, 20.76, 0.20209, 7, -3.4, 20.76, 0.79791, 2, 6, 22.46, 31.98, 0.25008, 7, -2.36, 31.98, 0.74992, 2, 6, 25.45, 37.91, 0.55118, 7, 0.63, 37.91, 0.44882, 2, 6, 32.09, 37.84, 0.90286, 7, 7.27, 37.84, 0.09714, 1, 6, 40.35, 32.71, 1, 1, 6, 46.9, 23.47, 1, 1, 6, 51.48, 7.15, 1, 1, 6, 51.31, -8.9, 1, 2, 6, 39.85, 28.59, 0.91479, 7, 15.03, 28.59, 0.08521, 2, 6, 39.34, 19.67, 0.90218, 7, 14.52, 19.67, 0.09782, 2, 6, 38.98, 6.82, 0.89413, 7, 14.16, 6.82, 0.10587, 2, 6, 38.84, -7.18, 0.91508, 7, 14.02, -7.18, 0.08492, 2, 6, 38.25, -18.63, 0.91044, 7, 13.43, -18.63, 0.08956, 2, 6, 38.14, -26.65, 0.93393, 7, 13.32, -26.65, 0.06607], "hull": 16, "edges": [22, 24, 24, 26, 26, 28, 28, 30, 30, 0, 0, 2, 2, 4, 20, 22, 20, 18, 18, 16, 16, 14, 12, 14, 10, 12, 10, 8, 4, 6, 8, 6, 24, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 2], "width": 76, "height": 33}}, "eyelid_u_r": {"eyelid_u_r": {"type": "mesh", "uvs": [0.8047, 0.14703, 0.9405, 0.37993, 1, 0.62615, 1, 0.81307, 0.9665, 0.84574, 0.81048, 0.92559, 0.51431, 0.92892, 0.22393, 0.91894, 0, 0.79976, 0, 0.53288, 0.09102, 0.30008, 0.25571, 0.10045, 0.43774, 0, 0.61111, 0, 0.23838, 0.47975, 0.48686, 0.45313, 0.75847, 0.4731], "triangles": [6, 16, 5, 6, 7, 15, 15, 12, 13, 16, 13, 0, 16, 0, 1, 15, 13, 16, 14, 10, 11, 15, 14, 11, 15, 11, 12, 9, 10, 14, 16, 1, 2, 8, 9, 14, 4, 16, 2, 6, 15, 16, 7, 14, 15, 3, 4, 2, 7, 8, 14, 5, 16, 4], "vertices": [1, 9, 44.85, -18.4, 1, 2, 9, 37.06, -28.64, 0.99429, 10, 15.17, -28.64, 0.00571, 2, 9, 28.89, -33.08, 0.93413, 10, 6.99, -33.08, 0.06587, 2, 9, 22.72, -33.01, 0.69345, 10, 0.83, -33.01, 0.30655, 2, 9, 21.67, -30.46, 0.45463, 10, -0.23, -30.46, 0.54537, 2, 9, 19.16, -18.57, 0.22188, 10, -2.74, -18.57, 0.77812, 2, 9, 19.28, 3.94, 0.22188, 10, -2.61, 3.94, 0.77812, 2, 9, 19.84, 26, 0.28079, 10, -2.05, 26, 0.71921, 2, 9, 23.95, 42.98, 0.51631, 10, 2.06, 42.98, 0.48369, 2, 9, 32.76, 42.89, 0.95148, 10, 10.86, 42.89, 0.04852, 2, 9, 40.37, 35.89, 0.98286, 10, 18.47, 35.89, 0.01714, 2, 9, 46.82, 23.3, 0.99429, 10, 24.93, 23.3, 0.00571, 1, 9, 49.99, 9.44, 1, 1, 9, 49.86, -3.74, 1, 2, 9, 34.32, 24.75, 0.85816, 10, 12.43, 24.75, 0.14184, 2, 9, 35, 5.86, 0.81757, 10, 13.11, 5.86, 0.18243, 2, 9, 34.13, -14.77, 0.84102, 10, 12.23, -14.77, 0.15898], "hull": 14, "edges": [18, 20, 20, 22, 22, 24, 24, 26, 26, 0, 0, 2, 2, 4, 16, 18, 16, 14, 10, 8, 4, 6, 8, 6, 14, 12, 12, 10, 18, 28, 28, 30, 30, 32, 32, 4], "width": 76, "height": 33}}, "eye_l": {"eye_l": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 6, -10.05, -43.73, 1, 1, 6, -9.12, 45.27, 1, 1, 6, 55.87, 44.59, 1, 1, 6, 54.94, -44.41, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 89, "height": 65}}, "eye_r": {"eye_r": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, -11.51, -38.66, 1, 1, 9, -10.58, 50.34, 1, 1, 9, 54.41, 49.66, 1, 1, 9, 53.49, -39.33, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 89, "height": 65}}, "leg_l": {"leg_l": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 33, 18.71, -10.05, 1, 1, 33, -29.29, -10.05, 1, 1, 3, 10.81, 40.97, 1, 1, 3, 10.31, -7.03, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 85}}, "leg_l_outline": {"leg_l_outline": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 33, 24.71, -17.05, 1, 1, 33, -35.29, -17.05, 1, 1, 3, 16.87, 46.91, 1, 1, 3, 16.25, -13.09, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "height": 98}}, "leg_r": {"leg_r": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 32, 27.9, -7.67, 1, 1, 32, -20.1, -7.67, 1, 1, 4, 8.2, 18.03, 1, 1, 4, 7.7, -29.97, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 48, "height": 85}}, "leg_r_outline": {"leg_r_outline": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [1, 32, 33.9, -14.67, 1, 1, 32, -26.1, -14.67, 1, 1, 4, 14.26, 23.97, 1, 1, 4, 13.63, -36.03, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 60, "height": 98}}, "mouth": {"mouth": {"type": "mesh", "uvs": [0.75459, 0.11197, 0.91602, 0.34537, 0.99998, 0.52457, 0.98622, 0.8237, 0.8983, 0.8613, 0.85105, 0.76302, 0.70538, 0.48663, 0.54789, 0.43136, 0.41009, 0.56034, 0.26245, 0.8183, 0.15024, 1, 0.0459, 1, 0, 0.75, 0, 0.5, 0.14236, 0.17339, 0.34119, 0, 0.54199, 0, 0.05435, 0.77821, 0.17951, 0.50711, 0.36764, 0.30565, 0.54296, 0.23849, 0.72232, 0.30813, 0.89691, 0.57177, 0.94474, 0.68121], "triangles": [3, 23, 2, 6, 21, 22, 11, 17, 10, 10, 17, 18, 11, 12, 17, 9, 18, 19, 18, 17, 13, 17, 12, 13, 23, 22, 2, 22, 21, 1, 13, 14, 18, 18, 14, 19, 8, 19, 20, 7, 20, 21, 19, 16, 20, 21, 20, 16, 4, 23, 3, 10, 18, 9, 22, 1, 2, 1, 21, 0, 0, 21, 16, 14, 15, 19, 19, 15, 16, 5, 22, 4, 9, 19, 8, 5, 6, 22, 7, 8, 20, 6, 7, 21, 4, 22, 23], "vertices": [2, 16, 2.71, -16.42, 0.90286, 5, -37.24, -24.84, 0.09714, 2, 16, -3.26, -28.95, 0.89714, 5, -43.2, -37.37, 0.10286, 2, 16, -7.81, -35.45, 0.72, 5, -47.75, -43.87, 0.28, 2, 16, -15.27, -34.3, 0.50286, 5, -55.22, -42.72, 0.49714, 2, 16, -16.14, -27.43, 0.88, 5, -56.09, -35.85, 0.12, 2, 16, -13.65, -23.77, 0.94286, 5, -53.59, -32.19, 0.05714, 1, 16, -6.62, -12.48, 1, 1, 16, -5.11, -0.22, 1, 1, 16, -8.22, 10.57, 1, 1, 16, -14.55, 22.15, 1, 2, 16, -19, 30.95, 0.70286, 5, -58.95, 22.53, 0.29714, 2, 16, -18.92, 39.09, 0.33143, 5, -58.86, 30.67, 0.66857, 1, 5, -52.57, 34.18, 1, 2, 16, -6.38, 42.54, 0.10286, 5, -46.32, 34.12, 0.89714, 2, 16, 1.67, 31.35, 0.93143, 5, -38.28, 22.93, 0.06857, 2, 16, 5.84, 15.79, 0.84571, 5, -34.1, 7.38, 0.15429, 2, 16, 5.68, 0.13, 0.82857, 5, -34.27, -8.28, 0.17143, 2, 16, -13.38, 38.37, 0.32571, 5, -53.32, 29.95, 0.67429, 2, 16, -6.7, 28.54, 0.10857, 5, -46.65, 20.12, 0.89143, 1, 5, -41.76, 5.39, 1, 1, 5, -40.23, -8.3, 1, 1, 5, -42.11, -22.27, 1, 2, 16, -8.9, -27.4, 0.49143, 5, -48.85, -35.82, 0.50857, 2, 16, -11.68, -31.1, 0.18286, 5, -51.62, -39.52, 0.81714], "hull": 17, "edges": [26, 28, 28, 30, 24, 26, 24, 22, 20, 22, 20, 18, 18, 16, 16, 14, 30, 32, 32, 0, 0, 2, 2, 4, 14, 12, 12, 10, 10, 8, 4, 6, 8, 6, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 78, "height": 25}}, "mouth_base": {"mouth_base": {"type": "mesh", "uvs": [0.77431, 0.0712, 0.91135, 0.21218, 1, 0.41082, 1, 0.70541, 0.95166, 0.80812, 0.8603, 0.91065, 0.73937, 0.89783, 0.62651, 0.76326, 0.51902, 0.75045, 0.36047, 0.84657, 0.25299, 1, 0.07294, 1, 0, 0.75, 0, 0.5, 0.12937, 0.19295, 0.29867, 0.0712, 0.5, 0, 0.081, 0.71841, 0.06263, 0.55925, 0.15552, 0.37182, 0.32843, 0.23892, 0.51421, 0.2321, 0.74, 0.35137, 0.82574, 0.43657, 0.10979, 0.82505, 0.21411, 0.79097, 0.35701, 0.58651, 0.51707, 0.47065, 0.62568, 0.49791, 0.71142, 0.58992, 0.78001, 0.69896, 0.86004, 0.73986, 0.89005, 0.66829, 0.88148, 0.53198], "triangles": [11, 24, 10, 24, 12, 17, 24, 11, 12, 24, 17, 25, 12, 18, 17, 12, 13, 18, 17, 18, 19, 18, 13, 14, 17, 19, 25, 24, 25, 10, 10, 25, 9, 6, 30, 5, 30, 31, 5, 4, 31, 32, 4, 5, 31, 7, 29, 6, 6, 29, 30, 25, 26, 9, 9, 26, 8, 4, 32, 3, 25, 19, 26, 8, 28, 7, 7, 28, 29, 26, 27, 8, 8, 27, 28, 32, 31, 33, 3, 33, 2, 31, 30, 33, 30, 23, 33, 30, 29, 23, 3, 32, 33, 29, 22, 23, 29, 28, 22, 19, 20, 26, 27, 20, 21, 27, 26, 20, 19, 18, 14, 2, 33, 1, 22, 28, 21, 28, 27, 21, 33, 23, 1, 1, 23, 0, 19, 15, 20, 19, 14, 15, 23, 22, 0, 22, 21, 0, 21, 20, 16, 20, 15, 16, 21, 16, 0], "vertices": [2, 16, 10.64, -24.66, 0.76406, 5, -29.3, -33.07, 0.23594, 2, 16, 5.01, -37.34, 0.78659, 5, -34.93, -45.76, 0.21341, 2, 16, -2.82, -45.51, 0.72, 5, -42.76, -53.92, 0.28, 2, 16, -14.31, -45.39, 0.57886, 5, -54.25, -53.8, 0.42114, 2, 16, -18.27, -40.85, 0.50286, 5, -58.21, -49.27, 0.49714, 2, 16, -22.18, -32.31, 0.65758, 5, -62.12, -40.73, 0.34242, 2, 16, -21.56, -21.07, 0.75701, 5, -61.5, -29.49, 0.24299, 2, 16, -16.2, -10.63, 0.68786, 5, -56.15, -19.05, 0.31214, 2, 16, -15.6, -0.64, 0.56571, 5, -55.54, -9.06, 0.43429, 2, 16, -19.19, 14.14, 0.68571, 5, -59.14, 5.73, 0.31429, 2, 16, -25.07, 24.2, 0.61312, 5, -65.02, 15.78, 0.38688, 2, 16, -24.9, 40.94, 0.33143, 5, -64.84, 32.53, 0.66857, 1, 5, -55.02, 39.21, 1, 2, 16, -5.33, 47.52, 0.10286, 5, -45.27, 39.11, 0.89714, 2, 16, 6.52, 35.37, 0.90548, 5, -33.42, 26.95, 0.09452, 2, 16, 11.1, 19.58, 0.75504, 5, -28.84, 11.16, 0.24496, 2, 16, 13.69, 0.82, 0.80656, 5, -26.26, -7.59, 0.19344, 2, 16, -13.92, 40.08, 0.20308, 5, -53.87, 31.66, 0.79692, 2, 16, -7.7, 41.72, 0.14485, 5, -47.64, 33.31, 0.85515, 2, 16, -0.48, 33.01, 0.71378, 5, -40.42, 24.59, 0.28622, 2, 16, 4.54, 16.88, 0.74815, 5, -35.41, 8.46, 0.25185, 2, 16, 4.62, -0.4, 0.69161, 5, -35.32, -8.82, 0.30839, 2, 16, -0.25, -21.35, 0.81051, 5, -40.19, -29.77, 0.18949, 2, 16, -3.65, -29.29, 0.87739, 5, -43.6, -37.71, 0.12261, 2, 16, -18.11, 37.45, 0.40051, 5, -58.05, 29.03, 0.5995, 2, 16, -16.88, 27.73, 0.78145, 5, -56.83, 19.32, 0.21855, 2, 16, -9.05, 14.36, 0.84746, 5, -48.99, 5.94, 0.15254, 2, 16, -4.68, -0.57, 0.90857, 5, -44.63, -8.99, 0.09143, 2, 16, -5.85, -10.66, 0.89714, 5, -45.8, -19.08, 0.10286, 2, 16, -9.52, -18.6, 0.91338, 5, -49.47, -27.01, 0.08662, 2, 16, -13.84, -24.93, 0.89102, 5, -53.79, -33.35, 0.10898, 2, 16, -15.51, -32.36, 0.6091, 5, -55.46, -40.77, 0.3909, 2, 16, -12.75, -35.18, 0.57828, 5, -52.7, -43.59, 0.42172, 2, 16, -7.43, -34.43, 0.72546, 5, -47.37, -42.85, 0.27454], "hull": 17, "edges": [26, 28, 28, 30, 30, 32, 32, 0, 0, 2, 2, 4, 24, 26, 24, 22, 20, 22, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 4, 6, 8, 6, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 34, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 46], "width": 93, "height": 39}}, "pupil_l": {"pupil_l": {"x": -0.35, "y": 0.13, "rotation": -90.6, "width": 19, "height": 19}}, "pupil_r": {"pupil_r": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-9.25, -9.56, -9.06, 9.44, 9.94, 9.24, 9.75, -9.76], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 19, "height": 19}}, "teeth": {"teeth": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -50.55, -35.84, 1, 1, 5, -49.94, 23.16, 1, 2, 16, 4, 31.43, 0.02857, 5, -35.94, 23.01, 0.97143, 1, 5, -36.55, -35.99, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 59, "height": 14}}}}], "animations": {"jump_L": {"bones": {"low_cntr": {"translatey": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.196, 72.6, 0.278, 110.7]}, {"time": 0.3333, "value": 114.83, "curve": [0.38, 118.34, 0.423, 120.92]}, {"time": 0.4667, "value": 114.83, "curve": [0.531, 105.86, 0.612, 43.98]}, {"time": 0.6333}], "scale": [{"curve": [0.044, 1, 0.089, 1.1, 0.044, 1, 0.089, 0.9]}, {"time": 0.1333, "x": 1.1, "y": 0.9, "curve": [0.144, 1.1, 0.156, 0.9, 0.144, 0.9, 0.156, 1.1]}, {"time": 0.1667, "x": 0.9, "y": 1.1, "curve": [0.233, 0.9, 0.3, 1, 0.233, 1.1, 0.3, 1]}, {"time": 0.3667}]}, "legL": {"translate": [{"curve": [0.056, 0, 0.111, 0, 0.056, 0, 0.111, 0.47]}, {"time": 0.1667, "y": 0.47, "curve": [0.189, 0, 0.211, 35.67, 0.189, 0.47, 0.211, -16.89]}, {"time": 0.2333, "x": 35.67, "y": -16.89, "curve": "stepped"}, {"time": 0.2667, "x": 35.67, "y": -16.89, "curve": [0.344, 35.67, 0.389, 0, 0.344, -16.89, 0.389, 12.94]}, {"time": 0.4667, "y": 12.94, "curve": [0.511, 0, 0.556, -2.07, 0.511, 12.94, 0.556, -14.52]}, {"time": 0.6, "x": -2.07, "y": -17.81, "curve": [0.611, -2.07, 0.622, 0, 0.611, -18.61, 0.622, 0]}, {"time": 0.6333}]}, "legR": {"translate": [{"curve": [0.056, 0, 0.111, 0, 0.056, 0, 0.111, 0.47]}, {"time": 0.1667, "y": 0.47, "curve": [0.189, 0, 0.211, 19.57, 0.189, 0.47, 0.211, -16.89]}, {"time": 0.2333, "x": 19.57, "y": -16.89, "curve": "stepped"}, {"time": 0.2667, "x": 19.57, "y": -16.89, "curve": [0.344, 19.57, 0.389, -1.04, 0.344, -16.89, 0.389, -19.16]}, {"time": 0.4667, "x": -1.04, "y": -19.16, "curve": [0.511, -1.04, 0.556, -0.37, 0.511, -19.16, 0.556, -22.39]}, {"time": 0.6, "x": -0.11, "y": -17.52, "curve": [0.611, -0.04, 0.622, 0, 0.611, -16.33, 0.622, 0]}, {"time": 0.6333}]}}}, "jump_R": {"bones": {"low_cntr": {"translatey": [{"curve": "stepped"}, {"time": 0.1667, "curve": [0.196, 72.6, 0.278, 110.7]}, {"time": 0.3333, "value": 114.83, "curve": [0.38, 118.34, 0.423, 120.92]}, {"time": 0.4667, "value": 114.83, "curve": [0.531, 105.86, 0.612, 43.98]}, {"time": 0.6333}], "scale": [{"curve": [0.044, 1, 0.089, 1.1, 0.044, 1, 0.089, 0.9]}, {"time": 0.1333, "x": 1.1, "y": 0.9, "curve": [0.144, 1.1, 0.156, 0.9, 0.144, 0.9, 0.156, 1.1]}, {"time": 0.1667, "x": 0.9, "y": 1.1, "curve": [0.233, 0.9, 0.3, 1, 0.233, 1.1, 0.3, 1]}, {"time": 0.3667}]}, "legR": {"translate": [{"curve": [0.056, 0, 0.111, 0, 0.056, 0, 0.111, 0.47]}, {"time": 0.1667, "y": 0.47, "curve": [0.189, 0, 0.211, -54.79, 0.189, 0.47, 0.211, -16.89]}, {"time": 0.2333, "x": -54.79, "y": -16.89, "curve": "stepped"}, {"time": 0.2667, "x": -54.79, "y": -16.89, "curve": [0.344, -54.79, 0.389, 0, 0.344, -16.89, 0.389, 12.94]}, {"time": 0.4667, "y": 12.94, "curve": [0.511, 0, 0.556, 0, 0.511, 12.94, 0.556, -22.29]}, {"time": 0.6, "y": -25.58, "curve": [0.611, 0, 0.622, 0, 0.611, -26.38, 0.622, 0]}, {"time": 0.6333}]}, "legL": {"translate": [{"curve": [0.056, 0, 0.111, 0, 0.056, 0, 0.111, 0.47]}, {"time": 0.1667, "y": 0.47, "curve": [0.189, 0, 0.211, -34.66, 0.189, 0.47, 0.211, -16.89]}, {"time": 0.2333, "x": -34.66, "y": -16.89, "curve": "stepped"}, {"time": 0.2667, "x": -34.66, "y": -16.89, "curve": [0.344, -34.66, 0.389, 12.94, 0.344, -16.89, 0.389, -17.09]}, {"time": 0.4667, "x": 12.94, "y": -17.09, "curve": [0.511, 12.94, 0.556, 4.63, 0.511, -17.09, 0.556, -35.11]}, {"time": 0.6, "x": 1.35, "y": -30.77, "curve": [0.611, 0.54, 0.622, 0, 0.611, -29.71, 0.622, 0]}, {"time": 0.6333}]}}}, "t0_405c80": {"slots": {"body_outline": {"rgba": [{"color": "405c80ff"}]}, "leg_l_outline": {"rgba": [{"color": "405c80ff"}]}, "leg_r_outline": {"rgba": [{"color": "405c80ff"}]}}}, "t1_Death": {"slots": {"blot": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0"}, {"time": 1, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.2667, "name": "blot"}]}, "blot_drop2": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0", "curve": [0.556, 0.16, 0.611, 0.16, 0.556, 0.7, 0.611, 0.7, 0.556, 0.77, 0.611, 0.77, 0.556, 1, 0.611, 0.33, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88]}, {"time": 0.6667, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5}]}, "blot_drop3": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0", "curve": [0.556, 0.16, 0.611, 0.16, 0.556, 0.7, 0.611, 0.7, 0.556, 0.77, 0.611, 0.77, 0.556, 1, 0.611, 0.33, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88]}, {"time": 0.6667, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6}]}, "blot_drop4": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0", "curve": [0.556, 0.16, 0.611, 0.16, 0.556, 0.7, 0.611, 0.7, 0.556, 0.77, 0.611, 0.77, 0.556, 1, 0.611, 0.33, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88]}, {"time": 0.6667, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop5": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0", "curve": [0.556, 0.16, 0.611, 0.16, 0.556, 0.7, 0.611, 0.7, 0.556, 0.77, 0.611, 0.77, 0.556, 1, 0.611, 0.33, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88]}, {"time": 0.6667, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.5}]}, "blot_drop6": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0", "curve": [0.556, 0.16, 0.611, 0.16, 0.556, 0.7, 0.611, 0.7, 0.556, 0.77, 0.611, 0.77, 0.556, 1, 0.611, 0.33, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88]}, {"time": 0.6667, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.5667}]}, "blot_drop7": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0", "curve": [0.556, 0.16, 0.611, 0.16, 0.556, 0.7, 0.611, 0.7, 0.556, 0.77, 0.611, 0.77, 0.556, 1, 0.611, 0.33, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88]}, {"time": 0.6667, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop2"}, {"time": 0.6333}]}, "blot_drop8": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0", "curve": [0.556, 0.16, 0.611, 0.16, 0.556, 0.7, 0.611, 0.7, 0.556, 0.77, 0.611, 0.77, 0.556, 1, 0.611, 0.33, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88]}, {"time": 0.6667, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.3, "name": "blot_drop1"}, {"time": 0.6667}]}, "blot_drop_s1": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0", "curve": [0.556, 0.16, 0.611, 0.16, 0.556, 0.7, 0.611, 0.7, 0.556, 0.77, 0.611, 0.77, 0.556, 1, 0.611, 0.33, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88]}, {"time": 0.6667, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4667}]}, "blot_drop_s2": {"rgba2": [{"light": "29b3c5ff", "dark": "e0e0e0", "curve": "stepped"}, {"time": 0.5, "light": "29b3c5ff", "dark": "e0e0e0", "curve": [0.556, 0.16, 0.611, 0.16, 0.556, 0.7, 0.611, 0.7, 0.556, 0.77, 0.611, 0.77, 0.556, 1, 0.611, 0.33, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88, 0.556, 0.88, 0.611, 0.88]}, {"time": 0.6667, "light": "29b3c500", "dark": "e0e0e0"}], "attachment": [{}, {"time": 0.2667, "name": "blot_drop1"}, {"time": 0.4333}]}, "body": {"attachment": [{"name": "body"}, {"time": 0.2667}]}, "body_outline": {"attachment": [{"time": 0.2667}]}, "brow_l": {"attachment": [{"name": "brow_l"}, {"time": 0.2667}]}, "brow_r": {"attachment": [{"name": "brow_r"}, {"time": 0.2667}]}, "eyelid_l_l": {"attachment": [{"name": "eyelid_l_l"}, {"time": 0.2667}]}, "eyelid_l_r": {"attachment": [{"name": "eyelid_l_r"}, {"time": 0.2667}]}, "eyelid_u_l": {"attachment": [{"name": "eyelid_u_l"}, {"time": 0.2667}]}, "eyelid_u_r": {"attachment": [{"name": "eyelid_u_r"}, {"time": 0.2667}]}, "eye_l": {"attachment": [{"name": "eye_l"}, {"time": 0.2667}]}, "eye_r": {"attachment": [{"name": "eye_r"}, {"time": 0.2667}]}, "leg_l": {"attachment": [{"name": "leg_l"}, {"time": 0.2667}]}, "leg_l_outline": {"attachment": [{"time": 0.2667}]}, "leg_r": {"attachment": [{"name": "leg_r"}, {"time": 0.2667}]}, "leg_r_outline": {"attachment": [{"time": 0.2667}]}, "mouth": {"attachment": [{"name": "mouth"}, {"time": 0.2667}]}, "mouth_base": {"attachment": [{"name": "mouth_base"}, {"time": 0.2667}]}, "pupil_l": {"attachment": [{"name": "pupil_l"}, {"time": 0.2667}]}, "pupil_r": {"attachment": [{"name": "pupil_r"}, {"time": 0.2667}]}, "teeth": {"attachment": [{"name": "teeth"}, {"time": 0.2667}]}}, "bones": {"blot": {"translate": [{"x": -12.21, "y": 15.4, "curve": "stepped"}, {"time": 0.3, "x": -12.21, "y": 15.4, "curve": [0.422, -12.21, 0.544, -12.22, 0.376, 7.46, 0.544, 1.44]}, {"time": 0.6667, "x": -12.22, "y": 1.44}], "scale": [{"x": 0.7, "y": 0.7}, {"time": 0.2667, "x": 0.449, "y": 0.449, "curve": [0.288, 1.12, 0.378, 1.2, 0.288, 1.12, 0.378, 1.2]}, {"time": 0.4333, "x": 1.2, "y": 1.2}]}, "blot_drops_control": {"translate": [{}, {"time": 0.3333, "x": -0.54, "y": -0.54, "curve": [0.556, -0.54, 0.778, 0, 0.479, -0.67, 0.778, -16.59]}, {"time": 1, "y": -16.59}]}, "blot_drop2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667, "curve": [0.333, 0, 0.4, -67.84]}, {"time": 0.4667, "value": -67.84}], "translate": [{}, {"time": 0.2667, "x": 41.44, "y": 7.91, "curve": [0.331, 77.65, 0.422, 273.93, 0.327, -38.99, 0.435, -211.23]}, {"time": 0.5, "x": 273.93, "y": -398.86}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop3": {"rotate": [{}, {"time": 0.2667, "value": -35.08, "curve": [0.329, 32.76, 0.489, 77.81]}, {"time": 0.6, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -74.68, "y": 24.95, "curve": [0.352, -115.07, 0.489, -322.02, 0.363, 261.05, 0.507, -337.68]}, {"time": 0.6, "x": -322.02, "y": -605.72}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4667, "curve": [0.511, 1, 0.556, 0.4, 0.511, 1, 0.556, 0.4]}, {"time": 0.6, "x": 0.4, "y": 0.4}]}, "blot_drop4": {"rotate": [{}, {"time": 0.2667, "value": 16.41, "curve": [0.363, 68.37, 0.467, 77.81]}, {"time": 0.5667, "value": 77.81}], "translate": [{}, {"time": 0.2667, "x": -48.78, "y": -9.58, "curve": [0.321, -164.38, 0.467, -211.51, 0.344, -53.5, 0.495, -476.89]}, {"time": 0.5667, "x": -211.51, "y": -781.84}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s1": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 54.1, "y": 60.11, "curve": [0.334, 209.62, 0.4, 276.96, 0.333, -14.18, 0.39, -99.2]}, {"time": 0.4667, "x": 276.96, "y": -195.12}]}, "blot_drop_s2": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": -53.64, "y": 83.69, "curve": [0.314, -164.57, 0.409, -315.66, 0.357, 159.13, 0.426, 84.28]}, {"time": 0.5, "x": -370.66, "y": -36.32}]}, "blot_drop5": {"rotate": [{}, {"time": 0.2667, "value": 103.14, "curve": [0.356, 103.14, 0.519, 97.56]}, {"time": 0.5333, "value": 97.56}], "translate": [{}, {"time": 0.2667, "x": 35.48, "y": -53.58, "curve": [0.355, 58.13, 0.467, 37.99, 0.344, -113.89, 0.483, -373.65]}, {"time": 0.5667, "x": 37.99, "y": -614.89}], "scale": [{"curve": "stepped"}, {"time": 0.4333, "curve": [0.478, 1, 0.522, 0.4, 0.478, 1, 0.522, 0.4]}, {"time": 0.5667, "x": 0.4, "y": 0.4}]}, "blot_drop_s3": {"rotate": [{"curve": "stepped"}, {"time": 0.2667}], "translate": [{}, {"time": 0.2667, "x": 10.55, "y": -19.73, "curve": [0.323, -6.01, 0.37, -21.01, 0.314, -79.55, 0.361, -201.42]}, {"time": 0.4333, "x": -31.65, "y": -390.13}]}, "blot_drop6": {"rotate": [{}, {"time": 0.2667, "value": -75.4, "curve": [0.309, -120.98, 0.393, -263.98]}, {"time": 0.5, "value": -261.68}], "translate": [{}, {"time": 0.2667, "x": 9.31, "y": 91.31, "curve": [0.358, 118.46, 0.511, 297.6, 0.322, 320.53, 0.481, 364.92]}, {"time": 0.6333, "x": 297.6, "y": -1347.33}], "scale": [{"curve": "stepped"}, {"time": 0.2667, "curve": "stepped"}, {"time": 0.5, "curve": [0.544, 1, 0.589, 0.4, 0.544, 1, 0.589, 0.4]}, {"time": 0.6333, "x": 0.4, "y": 0.4}]}, "blot_drop_s4": {"rotate": [{"curve": "stepped"}, {"time": 0.3}], "translate": [{}, {"time": 0.3, "x": -53.64, "y": 83.69, "curve": [0.388, -170.51, 0.54, -244.31, 0.46, -107.5, 0.576, -316.02]}, {"time": 0.6667, "x": -277.78, "y": -598.88}]}, "body_R": {"translate": [{"curve": [0.033, 0, 0.067, -0.27, 0.033, 0, 0.067, 22.04]}, {"time": 0.1, "x": -0.27, "y": 22.04, "curve": [0.144, -0.27, 0.189, 0.27, 0.144, 22.04, 0.189, 3.26]}, {"time": 0.2333, "x": 0.27, "y": 3.26}], "scale": [{"curve": [0.089, 1, 0.178, 1.987, 0.089, 1, 0.178, 1.662]}, {"time": 0.2667, "x": 1.987, "y": 1.662}]}, "body_L": {"translate": [{"curve": [0.033, 0, 0.067, 0.23, 0.033, 0, 0.067, -18.56]}, {"time": 0.1, "x": 0.23, "y": -18.56, "curve": [0.144, 0.23, 0.189, -0.03, 0.144, -18.56, 0.189, -9.8]}, {"time": 0.2333, "x": -0.03, "y": -9.8}], "scale": [{"curve": [0.089, 1, 0.222, 1.779, 0.089, 1, 0.222, 2.149]}, {"time": 0.2333, "x": 1.779, "y": 2.149}]}, "body": {"rotate": [{"curve": [0.022, 0.27, 0.044, 0.8]}, {"time": 0.0667, "value": 0.8, "curve": [0.089, 0.8, 0.111, -1.44]}, {"time": 0.1333, "value": -1.44, "curve": [0.156, -1.44, 0.178, 2.88]}, {"time": 0.2, "value": 2.88, "curve": [0.222, 2.88, 0.244, -4.12]}, {"time": 0.2667, "value": -4.12, "curve": [0.278, -4.12, 0.289, -1.37]}, {"time": 0.3}], "translate": [{"curve": [0.022, 0, 0.044, 0, 0.022, 3.15, 0.044, 2.1]}, {"time": 0.0667, "y": 9.45, "curve": [0.089, 0, 0.111, 0, 0.089, 16.81, 0.111, 44.12]}, {"time": 0.1333, "y": 44.12, "curve": [0.178, 0, 0.222, 0, 0.178, 44.12, 0.222, 28.1]}, {"time": 0.2667, "y": 13.39}], "scale": [{"curve": [0.022, 0.959, 0.044, 1.025, 0.022, 0.908, 0.044, 0.532]}, {"time": 0.0667, "x": 0.917, "y": 0.532, "curve": [0.089, 0.809, 0.144, 0.353, 0.089, 0.532, 0.144, 0.815]}, {"time": 0.1667, "x": 0.353, "y": 0.815, "curve": [0.211, 0.353, 0.222, 0.554, 0.211, 0.815, 0.222, 0.638]}, {"time": 0.2667, "x": 0.75, "y": 0.63}]}, "eyeR": {"translate": [{"curve": [0.03, 0, 0.065, -12.81, 0.03, 0, 0.065, 0.08]}, {"time": 0.1, "x": -10.96, "y": 0.08, "curve": [0.158, -7.97, 0.219, 10.23, 0.158, 0.08, 0.219, 0]}, {"time": 0.2667, "x": 10.23}], "scale": [{"curve": [0.089, 1, 0.178, 1.349, 0.089, 1, 0.178, 1.168]}, {"time": 0.2667, "x": 1.349, "y": 1.168}]}, "face": {"translate": [{"curve": [0.067, 0, 0.133, 8.66, 0.067, 0, 0.133, 0.3]}, {"time": 0.2, "x": 8.66, "y": 0.3}], "scale": [{"curve": [0.067, 1, 0.133, 1.219, 0.067, 1, 0.133, 1.219]}, {"time": 0.2, "x": 1.219, "y": 1.219}]}, "closed_eyeR_up": {"translate": [{"curve": [0.056, 0, 0.111, 29.08, 0.056, 0, 0.111, -0.34]}, {"time": 0.1667, "x": 29.08, "y": -0.34}]}, "closed_eyeL_up": {"translate": [{"curve": [0.056, 0, 0.111, 29.08, 0.056, 0, 0.111, -0.34]}, {"time": 0.1667, "x": 29.08, "y": -0.34}]}, "closed_eyeL_down": {"translate": [{"curve": [0.056, 0, 0.111, -11.79, 0.056, 0, 0.111, 0.14]}, {"time": 0.1667, "x": -11.79, "y": 0.14}]}, "closed_eyeR_down": {"translate": [{"curve": [0.056, 0, 0.111, -11.79, 0.056, 0, 0.111, 0.14]}, {"time": 0.1667, "x": -11.79, "y": 0.14}]}, "browR": {"rotate": [{"curve": [0.067, 0, 0.133, 26]}, {"time": 0.2, "value": 26}], "translate": [{"curve": [0.067, 0, 0.133, 0.85, 0.067, 0, 0.133, 29.32]}, {"time": 0.2, "x": 0.85, "y": 29.32}]}, "browL": {"rotate": [{"curve": [0.078, 0, 0.156, -18.3]}, {"time": 0.2333, "value": -18.3}], "translate": [{"curve": [0.078, 0, 0.156, 0.7, 0.078, 0, 0.156, -24.63]}, {"time": 0.2333, "x": 0.7, "y": -24.63}]}, "eye_r": {"translate": [{"curve": [0.011, 0, 0.022, 0.06, 0.011, 0, 0.022, 5.42]}, {"time": 0.0333, "x": 0.06, "y": 5.42, "curve": [0.044, 0.06, 0.056, -0.02, 0.044, 5.42, 0.056, -2.69]}, {"time": 0.0667, "x": -0.02, "y": -2.69, "curve": [0.078, -0.02, 0.089, 0.12, 0.078, -2.69, 0.089, 12.56]}, {"time": 0.1, "x": 0.12, "y": 12.56, "curve": [0.111, 0.12, 0.122, -9.51, 0.111, 12.56, 0.122, 0.76]}, {"time": 0.1333, "x": -9.51, "y": 0.76, "curve": [0.144, -9.51, 0.156, 6.82, 0.144, 0.76, 0.156, 0.57]}, {"time": 0.1667, "x": 6.82, "y": 0.57, "curve": [0.178, 6.82, 0.189, 7.01, 0.178, 0.57, 0.189, 21.45]}, {"time": 0.2, "x": 7.01, "y": 21.45, "curve": [0.211, 7.01, 0.222, 28.84, 0.211, 21.45, 0.222, 29.68]}, {"time": 0.2333, "x": 28.84, "y": 29.68}]}, "mouth": {"translate": [{"curve": [0.078, 0, 0.156, 0.59, 0.078, 0, 0.156, 0.02]}, {"time": 0.2333, "x": 0.59, "y": 0.02}], "scale": [{"curve": [0.022, 1, 0.178, 1.818, 0.022, 1, 0.178, 1.197]}, {"time": 0.2333, "x": 1.818, "y": 1.197}]}, "eyeR2": {"translate": [{"curve": [0.03, 0, 0.065, 7.83, 0.03, 0, 0.065, -0.19]}, {"time": 0.1, "x": 5.51, "y": -0.39, "curve": [0.158, 1.79, 0.219, -13.01, 0.158, -0.69, 0.219, -1.07]}, {"time": 0.2667, "x": -15.05, "y": -1.22}], "scale": [{"curve": [0.089, 1, 0.178, 1.301, 0.089, 1, 0.178, 1.139]}, {"time": 0.2667, "x": 1.301, "y": 1.139}]}, "body_R2": {"rotate": [{"curve": [0.078, 0, 0.156, -31.33]}, {"time": 0.2333, "value": -31.33}]}, "body_L2": {"rotate": [{"curve": [0.078, 0, 0.156, 22.61]}, {"time": 0.2333, "value": 22.61}], "translate": [{"curve": [0.044, 0, 0.089, 5.72, 0.044, 0, 0.089, 0.04]}, {"time": 0.1333, "x": 5.72, "y": 0.04, "curve": [0.167, 5.72, 0.2, -9.29, 0.167, 0.04, 0.2, 0.03]}, {"time": 0.2333, "x": -9.29, "y": 0.03}]}, "legR": {"translate": [{"curve": [0.022, -3.66, 0.044, -10.98, 0.022, 0, 0.044, 0]}, {"time": 0.0667, "x": -10.98, "curve": [0.1, -10.98, 0.133, 26.53, 0.1, 0, 0.133, 0]}, {"time": 0.1667, "x": 26.53, "curve": [0.2, 26.53, 0.233, 12.5, 0.2, 0, 0.233, 0]}, {"time": 0.2667, "x": 5.49}]}, "legL": {"translate": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.056, 0, 0.078, 10.98, 0.056, 0, 0.078, 0]}, {"time": 0.1, "x": 10.98, "curve": [0.133, 10.98, 0.167, -17.38, 0.133, 0, 0.167, 0]}, {"time": 0.2, "x": -17.38, "curve": [0.233, -17.38, 0.267, -3.96, 0.233, 0, 0.267, 0]}, {"time": 0.3, "x": 2.74}]}}}, "t1_IDLE_1_0_a": {"bones": {"body": {"rotate": [{"curve": [0.011, 0, 0.022, 0.62]}, {"time": 0.0333, "value": 0.62, "curve": [0.089, 0.62, 0.144, -3.16]}, {"time": 0.2, "value": -3.16, "curve": [0.311, -3.16, 0.422, 3.02]}, {"time": 0.5333, "value": 3.02, "curve": [0.611, 3.02, 0.689, -2.49]}, {"time": 0.7667, "value": -2.49, "curve": [0.811, -2.49, 0.856, 1.26]}, {"time": 0.9, "value": 1.26, "curve": [0.944, 1.26, 0.989, -0.5]}, {"time": 1.0333, "value": -0.5, "curve": [1.078, -0.5, 1.122, 0.49]}, {"time": 1.1667, "value": 0.49, "curve": [1.222, 0.49, 1.278, 0]}, {"time": 1.3333, "curve": [1.344, 0, 1.356, 0.62]}, {"time": 1.3667, "value": 0.62, "curve": [1.422, 0.62, 1.478, -3.16]}, {"time": 1.5333, "value": -3.16, "curve": [1.644, -3.16, 1.756, 3.02]}, {"time": 1.8667, "value": 3.02, "curve": [1.944, 3.02, 2.022, -2.49]}, {"time": 2.1, "value": -2.49, "curve": [2.144, -2.49, 2.189, 1.26]}, {"time": 2.2333, "value": 1.26, "curve": [2.278, 1.26, 2.322, -0.5]}, {"time": 2.3667, "value": -0.5, "curve": [2.411, -0.5, 2.456, 0.49]}, {"time": 2.5, "value": 0.49, "curve": [2.556, 0.49, 2.611, 0]}, {"time": 2.6667}], "scale": [{"curve": [0.022, 1, 0.044, 0.94, 0.022, 1, 0.044, 1.06]}, {"time": 0.0667, "x": 0.94, "y": 1.06, "curve": [0.122, 0.94, 0.178, 1.04, 0.122, 1.06, 0.178, 0.96]}, {"time": 0.2333, "x": 1.04, "y": 0.96, "curve": [0.289, 1.04, 0.344, 0.98, 0.289, 0.96, 0.344, 1.02]}, {"time": 0.4, "x": 0.98, "y": 1.02, "curve": [0.456, 0.98, 0.511, 1.04, 0.456, 1.02, 0.511, 0.96]}, {"time": 0.5667, "x": 1.04, "y": 0.96, "curve": [0.622, 1.04, 0.678, 0.92, 0.622, 0.96, 0.678, 1.08]}, {"time": 0.7333, "x": 0.92, "y": 1.08, "curve": [0.778, 0.92, 0.822, 1.04, 0.778, 1.08, 0.822, 0.96]}, {"time": 0.8667, "x": 1.04, "y": 0.96, "curve": [0.911, 1.04, 0.956, 0.976, 0.911, 0.96, 0.956, 1.024]}, {"time": 1, "x": 0.976, "y": 1.024, "curve": [1.044, 0.976, 1.089, 1.013, 1.044, 1.024, 1.089, 0.987]}, {"time": 1.1333, "x": 1.013, "y": 0.987, "curve": [1.178, 1.013, 1.267, 1, 1.178, 0.987, 1.267, 1]}, {"time": 1.3333, "curve": [1.356, 1, 1.378, 0.94, 1.356, 1, 1.378, 1.06]}, {"time": 1.4, "x": 0.94, "y": 1.06, "curve": [1.456, 0.94, 1.511, 1.04, 1.456, 1.06, 1.511, 0.96]}, {"time": 1.5667, "x": 1.04, "y": 0.96, "curve": [1.622, 1.04, 1.678, 0.98, 1.622, 0.96, 1.678, 1.02]}, {"time": 1.7333, "x": 0.98, "y": 1.02, "curve": [1.789, 0.98, 1.844, 1.04, 1.789, 1.02, 1.844, 0.96]}, {"time": 1.9, "x": 1.04, "y": 0.96, "curve": [1.956, 1.04, 2.011, 0.92, 1.956, 0.96, 2.011, 1.08]}, {"time": 2.0667, "x": 0.92, "y": 1.08, "curve": [2.111, 0.92, 2.156, 1.04, 2.111, 1.08, 2.156, 0.96]}, {"time": 2.2, "x": 1.04, "y": 0.96, "curve": [2.244, 1.04, 2.289, 0.976, 2.244, 0.96, 2.289, 1.024]}, {"time": 2.3333, "x": 0.976, "y": 1.024, "curve": [2.378, 0.976, 2.422, 1.013, 2.378, 1.024, 2.422, 0.987]}, {"time": 2.4667, "x": 1.013, "y": 0.987, "curve": [2.511, 1.013, 2.6, 1, 2.511, 0.987, 2.6, 1]}, {"time": 2.6667}]}, "body_R": {"translate": [{"x": 3.6, "y": -0.03, "curve": [0.022, 3.6, 0.044, 7.34, 0.022, -0.03, 0.044, -0.04]}, {"time": 0.0667, "x": 7.34, "y": -0.09, "curve": [0.122, 7.34, 0.178, -13.11, 0.122, -0.19, 0.178, -0.69]}, {"time": 0.2333, "x": -13.11, "y": -0.69, "curve": [0.289, -13.11, 0.344, 9.24, 0.289, -0.69, 0.344, -0.19]}, {"time": 0.4, "x": 10.77, "y": -0.19, "curve": [0.478, 12.91, 0.556, 12.91, 0.478, -0.19, 0.556, -0.19]}, {"time": 0.6333, "x": 12.91, "y": -0.22, "curve": [0.667, 12.91, 0.7, -4.99, 0.667, -0.22, 0.7, -0.31]}, {"time": 0.7333, "x": -4.99, "y": -0.31, "curve": [0.778, -4.99, 0.822, 6.98, 0.778, -0.31, 0.822, -0.16]}, {"time": 0.8667, "x": 6.98, "y": -0.11, "curve": [0.911, 6.98, 0.956, 1.8, 0.911, -0.06, 0.956, -0.02]}, {"time": 1, "x": 1.8, "y": -0.02, "curve": [1.044, 1.8, 1.089, 7.07, 1.044, -0.02, 1.089, -0.1]}, {"time": 1.1333, "x": 7.07, "y": -0.1, "curve": [1.178, 7.07, 1.267, 3.6, 1.178, -0.1, 1.267, -0.03]}, {"time": 1.3333, "x": 3.6, "y": -0.03, "curve": [1.356, 3.6, 1.378, 7.34, 1.356, -0.03, 1.378, -0.04]}, {"time": 1.4, "x": 7.34, "y": -0.09, "curve": [1.456, 7.34, 1.511, -13.11, 1.456, -0.19, 1.511, -0.69]}, {"time": 1.5667, "x": -13.11, "y": -0.69, "curve": [1.622, -13.11, 1.678, 9.24, 1.622, -0.69, 1.678, -0.19]}, {"time": 1.7333, "x": 10.77, "y": -0.19, "curve": [1.811, 12.91, 1.889, 12.91, 1.811, -0.19, 1.889, -0.19]}, {"time": 1.9667, "x": 12.91, "y": -0.22, "curve": [2, 12.91, 2.033, -4.99, 2, -0.22, 2.033, -0.31]}, {"time": 2.0667, "x": -4.99, "y": -0.31, "curve": [2.111, -4.99, 2.156, 6.98, 2.111, -0.31, 2.156, -0.16]}, {"time": 2.2, "x": 6.98, "y": -0.11, "curve": [2.244, 6.98, 2.289, 1.8, 2.244, -0.06, 2.289, -0.02]}, {"time": 2.3333, "x": 1.8, "y": -0.02, "curve": [2.378, 1.8, 2.422, 7.07, 2.378, -0.02, 2.422, -0.1]}, {"time": 2.4667, "x": 7.07, "y": -0.1, "curve": [2.511, 7.07, 2.6, 3.6, 2.511, -0.1, 2.6, -0.03]}, {"time": 2.6667, "x": 3.6, "y": -0.03}]}, "body_L": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.089, 0, 0.111, 3.74, 0.089, 0, 0.111, -0.01]}, {"time": 0.1333, "x": 3.74, "y": -0.05, "curve": [0.189, 3.74, 0.244, -9.95, 0.189, -0.16, 0.244, -0.43]}, {"time": 0.3, "x": -9.95, "y": -0.43, "curve": [0.356, -9.95, 0.411, 5.64, 0.356, -0.43, 0.411, -0.16]}, {"time": 0.4667, "x": 7.17, "y": -0.16, "curve": [0.544, 9.32, 0.622, 9.32, 0.544, -0.16, 0.622, -0.16]}, {"time": 0.7, "x": 9.32, "y": -0.18, "curve": [0.733, 9.32, 0.767, -8.59, 0.733, -0.19, 0.767, -0.27]}, {"time": 0.8, "x": -8.59, "y": -0.27, "curve": [0.844, -8.59, 0.889, 3.38, 0.844, -0.27, 0.889, -0.13]}, {"time": 0.9333, "x": 3.38, "y": -0.08, "curve": [0.978, 3.38, 1.022, -1.8, 0.978, -0.03, 1.022, 0.01]}, {"time": 1.0667, "x": -1.8, "y": 0.01, "curve": [1.111, -1.8, 1.156, 3.47, 1.111, 0.01, 1.156, -0.07]}, {"time": 1.2, "x": 3.47, "y": -0.07, "curve": [1.244, 3.47, 1.289, 0, 1.244, -0.07, 1.289, 0]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.4, "curve": [1.422, 0, 1.444, 3.74, 1.422, 0, 1.444, -0.01]}, {"time": 1.4667, "x": 3.74, "y": -0.05, "curve": [1.522, 3.74, 1.578, -9.95, 1.522, -0.16, 1.578, -0.43]}, {"time": 1.6333, "x": -9.95, "y": -0.43, "curve": [1.689, -9.95, 1.744, 5.64, 1.689, -0.43, 1.744, -0.16]}, {"time": 1.8, "x": 7.17, "y": -0.16, "curve": [1.878, 9.32, 1.956, 9.32, 1.878, -0.16, 1.956, -0.16]}, {"time": 2.0333, "x": 9.32, "y": -0.18, "curve": [2.067, 9.32, 2.1, -8.59, 2.067, -0.19, 2.1, -0.27]}, {"time": 2.1333, "x": -8.59, "y": -0.27, "curve": [2.178, -8.59, 2.222, 3.38, 2.178, -0.27, 2.222, -0.13]}, {"time": 2.2667, "x": 3.38, "y": -0.08, "curve": [2.311, 3.38, 2.356, -1.8, 2.311, -0.03, 2.356, 0.01]}, {"time": 2.4, "x": -1.8, "y": 0.01, "curve": [2.444, -1.8, 2.489, 3.47, 2.444, 0.01, 2.489, -0.07]}, {"time": 2.5333, "x": 3.47, "y": -0.07, "curve": [2.578, 3.47, 2.622, 0, 2.578, -0.07, 2.622, 0]}, {"time": 2.6667}]}, "closed_eyeL_down": {"translate": [{"curve": [0.034, 0, 0.066, 3.43, 0.034, 0, 0.066, 0]}, {"time": 0.1, "x": 3.43, "curve": [0.134, 3.43, 0.166, 0, 0.134, 0, 0.166, 0]}, {"time": 0.2, "curve": [0.234, 0, 0.266, 6.16, 0.234, 0, 0.266, 0]}, {"time": 0.3, "x": 6.16, "curve": [0.334, 6.16, 0.366, 0, 0.334, 0, 0.366, 0]}, {"time": 0.4, "curve": [0.434, 0, 0.466, 2.77, 0.434, 0, 0.466, 0]}, {"time": 0.5, "x": 2.77, "curve": [0.534, 2.77, 0.566, 0, 0.534, 0, 0.566, 0]}, {"time": 0.6, "curve": [0.634, 0, 0.699, 5.24, 0.634, 0, 0.699, 0]}, {"time": 0.7333, "x": 5.24, "curve": [0.767, 5.24, 0.799, 0, 0.767, 0, 0.799, 0]}, {"time": 0.8333, "curve": [0.867, 0, 0.899, 3.43, 0.867, 0, 0.899, 0]}, {"time": 0.9333, "x": 3.43, "curve": [0.967, 3.43, 0.999, 0, 0.967, 0, 0.999, 0]}, {"time": 1.0333, "curve": [1.067, 0, 1.099, 6.16, 1.067, 0, 1.099, 0]}, {"time": 1.1333, "x": 6.16, "curve": [1.167, 6.16, 1.199, 0, 1.167, 0, 1.199, 0]}, {"time": 1.2333, "curve": [1.267, 0, 1.299, 2.77, 1.267, 0, 1.299, 0]}, {"time": 1.3333, "x": 2.77, "curve": [1.367, 2.77, 1.399, 0, 1.367, 0, 1.399, 0]}, {"time": 1.4333, "curve": [1.467, 0, 1.499, 5.24, 1.467, 0, 1.499, 0]}, {"time": 1.5333, "x": 5.24, "curve": [1.567, 5.24, 1.599, 0, 1.567, 0, 1.599, 0]}, {"time": 1.6333, "curve": [1.667, 0, 1.699, 3.43, 1.667, 0, 1.699, 0]}, {"time": 1.7333, "x": 3.43, "curve": [1.767, 3.43, 1.799, 0, 1.767, 0, 1.799, 0]}, {"time": 1.8333, "curve": [1.867, 0, 1.899, 6.16, 1.867, 0, 1.899, 0]}, {"time": 1.9333, "x": 6.16, "curve": [1.967, 6.16, 1.999, 0, 1.967, 0, 1.999, 0]}, {"time": 2.0333, "curve": [2.067, 0, 2.133, 2.77, 2.067, 0, 2.133, 0]}, {"time": 2.1667, "x": 2.77, "curve": [2.201, 2.77, 2.233, 0, 2.201, 0, 2.233, 0]}, {"time": 2.2667, "curve": [2.301, 0, 2.333, 5.24, 2.301, 0, 2.333, 0]}, {"time": 2.3667, "x": 5.24, "curve": [2.401, 5.24, 2.433, 0, 2.401, 0, 2.433, 0]}, {"time": 2.4667, "curve": [2.501, 0, 2.533, 3.43, 2.501, 0, 2.533, 0]}, {"time": 2.5667, "x": 3.43, "curve": [2.601, 3.43, 2.633, 0, 2.601, 0, 2.633, 0]}, {"time": 2.6667}]}, "browR": {"rotate": [{"value": -4.67, "curve": "stepped"}, {"time": 1.3333, "value": -4.67, "curve": "stepped"}, {"time": 2.6667, "value": -4.67}], "translate": [{"x": -2.98, "y": 0.04, "curve": [0.033, -2.16, 0.067, 6.27, 0.033, 0.04, 0.067, 0.04]}, {"time": 0.1, "x": 6.27, "y": 0.02, "curve": [0.156, 6.27, 0.211, -4.78, 0.156, -0.02, 0.211, -0.17]}, {"time": 0.2667, "x": -4.78, "y": -0.28, "curve": [0.344, -4.78, 0.422, 3.21, 0.344, -0.42, 0.422, -0.6]}, {"time": 0.5, "x": 3.84, "y": -0.73, "curve": [0.567, 4.38, 0.633, 4.38, 0.567, -0.85, 0.633, -1]}, {"time": 0.7, "x": 4.38, "y": -1.05, "curve": [0.744, 4.38, 0.789, -6.82, 0.744, -1.08, 0.789, -1.07]}, {"time": 0.8333, "x": -6.82, "y": -1.08, "curve": [0.9, -6.82, 0.967, -1.44, 0.9, -1.09, 0.967, -1.09]}, {"time": 1.0333, "x": -1.44, "y": -1.09, "curve": [1.089, -1.44, 1.144, -4.06, 1.089, -1.09, 1.144, -1.09]}, {"time": 1.2, "x": -4.06, "y": -1.03, "curve": [1.244, -4.06, 1.289, -4.06, 1.244, -0.99, 1.289, 0.04]}, {"time": 1.3333, "x": -2.98, "y": 0.04, "curve": [1.367, -2.16, 1.4, 6.27, 1.367, 0.04, 1.4, 0.04]}, {"time": 1.4333, "x": 6.27, "y": 0.02, "curve": [1.489, 6.27, 1.544, -4.78, 1.489, -0.02, 1.544, -0.17]}, {"time": 1.6, "x": -4.78, "y": -0.28, "curve": [1.678, -4.78, 1.756, 3.21, 1.678, -0.42, 1.756, -0.6]}, {"time": 1.8333, "x": 3.84, "y": -0.73, "curve": [1.9, 4.38, 1.967, 4.38, 1.9, -0.85, 1.967, -1]}, {"time": 2.0333, "x": 4.38, "y": -1.05, "curve": [2.078, 4.38, 2.122, -6.82, 2.078, -1.08, 2.122, -1.07]}, {"time": 2.1667, "x": -6.82, "y": -1.08, "curve": [2.233, -6.82, 2.3, -1.44, 2.233, -1.09, 2.3, -1.09]}, {"time": 2.3667, "x": -1.44, "y": -1.09, "curve": [2.422, -1.44, 2.478, -4.06, 2.422, -1.09, 2.478, -1.09]}, {"time": 2.5333, "x": -4.06, "y": -1.03, "curve": [2.578, -4.06, 2.622, -4.06, 2.578, -0.99, 2.622, 0.04]}, {"time": 2.6667, "x": -2.98, "y": 0.04}]}, "browL": {"rotate": [{"value": 4.31, "curve": "stepped"}, {"time": 1.3333, "value": 4.31, "curve": "stepped"}, {"time": 2.6667, "value": 4.31}], "translate": [{"x": -1.46, "y": 0.03, "curve": [0.056, 1, 0.111, 7.78, 0.056, 0.03, 0.111, 0.03]}, {"time": 0.1667, "x": 7.78, "y": 0.01, "curve": [0.222, 7.78, 0.278, -3.26, 0.222, -0.02, 0.278, -0.18]}, {"time": 0.3333, "x": -3.26, "y": -0.29, "curve": [0.411, -3.26, 0.489, 4.72, 0.411, -0.44, 0.489, -0.61]}, {"time": 0.5667, "x": 5.35, "y": -0.75, "curve": [0.633, 5.89, 0.7, 5.89, 0.633, -0.87, 0.7, -1.01]}, {"time": 0.7667, "x": 5.89, "y": -1.06, "curve": [0.811, 5.89, 0.856, -5.3, 0.811, -1.09, 0.856, -1.09]}, {"time": 0.9, "x": -5.3, "y": -1.09, "curve": [0.967, -5.3, 1.033, 0.07, 0.967, -1.1, 1.033, -1.1]}, {"time": 1.1, "x": 0.07, "y": -1.1, "curve": [1.156, 0.07, 1.211, -2.54, 1.156, -1.1, 1.211, -1.1]}, {"time": 1.2667, "x": -2.54, "y": -1.05, "curve": [1.289, -2.54, 1.311, -2.45, 1.289, -1.02, 1.311, 0.03]}, {"time": 1.3333, "x": -1.46, "y": 0.03, "curve": [1.389, 1, 1.444, 7.78, 1.389, 0.03, 1.444, 0.03]}, {"time": 1.5, "x": 7.78, "y": 0.01, "curve": [1.556, 7.78, 1.611, -3.26, 1.556, -0.02, 1.611, -0.18]}, {"time": 1.6667, "x": -3.26, "y": -0.29, "curve": [1.744, -3.26, 1.822, 4.72, 1.744, -0.44, 1.822, -0.61]}, {"time": 1.9, "x": 5.35, "y": -0.75, "curve": [1.967, 5.89, 2.033, 5.89, 1.967, -0.87, 2.033, -1.01]}, {"time": 2.1, "x": 5.89, "y": -1.06, "curve": [2.144, 5.89, 2.189, -5.3, 2.144, -1.09, 2.189, -1.09]}, {"time": 2.2333, "x": -5.3, "y": -1.09, "curve": [2.3, -5.3, 2.367, 0.07, 2.3, -1.1, 2.367, -1.1]}, {"time": 2.4333, "x": 0.07, "y": -1.1, "curve": [2.489, 0.07, 2.544, -2.54, 2.489, -1.1, 2.544, -1.1]}, {"time": 2.6, "x": -2.54, "y": -1.05, "curve": [2.622, -2.54, 2.644, -2.45, 2.622, -1.02, 2.644, 0.03]}, {"time": 2.6667, "x": -1.46, "y": 0.03}]}, "closed_eyeR_up": {"translate": [{"x": 5.23, "y": -0.07, "curve": [0.144, 5.23, 0.289, 12.97, 0.144, -0.07, 0.289, -0.17]}, {"time": 0.4333, "x": 12.97, "y": -0.17, "curve": [0.533, 12.97, 0.633, 3.68, 0.533, -0.17, 0.633, -0.06]}, {"time": 0.7333, "x": 3.68, "y": -0.06, "curve": [0.933, 3.68, 1.133, 5.23, 0.933, -0.06, 1.133, -0.07]}, {"time": 1.3333, "x": 5.23, "y": -0.07, "curve": [1.478, 5.23, 1.622, 12.97, 1.478, -0.07, 1.622, -0.17]}, {"time": 1.7667, "x": 12.97, "y": -0.17, "curve": [1.867, 12.97, 1.967, 3.68, 1.867, -0.17, 1.967, -0.06]}, {"time": 2.0667, "x": 3.68, "y": -0.06, "curve": [2.267, 3.68, 2.467, 5.23, 2.267, -0.06, 2.467, -0.07]}, {"time": 2.6667, "x": 5.23, "y": -0.07}]}, "closed_eyeL_up": {"translate": [{"x": 12.68, "y": -0.13, "curve": [0.144, 12.68, 0.289, 20.42, 0.144, -0.13, 0.289, -0.22]}, {"time": 0.4333, "x": 20.42, "y": -0.22, "curve": [0.533, 20.42, 0.633, 11.13, 0.533, -0.22, 0.633, -0.11]}, {"time": 0.7333, "x": 11.13, "y": -0.11, "curve": [0.933, 11.13, 1.133, 12.68, 0.933, -0.11, 1.133, -0.13]}, {"time": 1.3333, "x": 12.68, "y": -0.13, "curve": [1.478, 12.68, 1.622, 20.42, 1.478, -0.13, 1.622, -0.22]}, {"time": 1.7667, "x": 20.42, "y": -0.22, "curve": [1.867, 20.42, 1.967, 11.13, 1.867, -0.22, 1.967, -0.11]}, {"time": 2.0667, "x": 11.13, "y": -0.11, "curve": [2.211, 11.13, 2.356, 16.55, 2.211, -0.11, 2.356, -0.17]}, {"time": 2.5, "x": 16.55, "y": -0.17, "curve": [2.556, 16.55, 2.611, 12.68, 2.556, -0.17, 2.611, -0.13]}, {"time": 2.6667, "x": 12.68, "y": -0.13}]}, "closed_eyeR_down": {"translate": [{"x": 3.1, "y": -0.03, "curve": "stepped"}, {"time": 2.6667, "x": 3.1, "y": -0.03}], "scale": [{"curve": "stepped"}, {"time": 2.6667}]}, "eye_l": {"translate": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2.6667}]}, "mouth": {"scale": [{"x": 0.51, "curve": [0.133, 0.51, 0.267, 1.329, 0.133, 1, 0.267, 1]}, {"time": 0.4, "x": 1.329, "curve": [0.511, 1.329, 0.622, 0.51, 0.511, 1, 0.622, 1]}, {"time": 0.7333, "x": 0.51, "curve": [0.8, 0.51, 0.867, 0.595, 0.8, 1, 0.867, 1]}, {"time": 0.9333, "x": 0.595, "curve": [1.011, 0.595, 1.089, 0.469, 1.011, 1, 1.089, 1]}, {"time": 1.1667, "x": 0.469, "curve": [1.222, 0.469, 1.278, 0.51, 1.222, 1, 1.278, 1]}, {"time": 1.3333, "x": 0.51, "curve": [1.467, 0.51, 1.6, 1.329, 1.467, 1, 1.6, 1]}, {"time": 1.7333, "x": 1.329, "curve": [1.844, 1.329, 1.956, 0.51, 1.844, 1, 1.956, 1]}, {"time": 2.0667, "x": 0.51, "curve": [2.133, 0.51, 2.2, 0.595, 2.133, 1, 2.2, 1]}, {"time": 2.2667, "x": 0.595, "curve": [2.344, 0.595, 2.422, 0.469, 2.344, 1, 2.422, 1]}, {"time": 2.5, "x": 0.469, "curve": [2.556, 0.469, 2.611, 0.51, 2.556, 1, 2.611, 1]}, {"time": 2.6667, "x": 0.51}]}, "body_R2": {"translate": [{"x": 4.86, "y": -0.05, "curve": "stepped"}, {"time": 1.3333, "x": 4.86, "y": -0.05, "curve": "stepped"}, {"time": 2.6667, "x": 4.86, "y": -0.05}]}, "body_L2": {"translate": [{"x": 4.14, "y": -0.04, "curve": "stepped"}, {"time": 1.3333, "x": 4.14, "y": -0.04, "curve": "stepped"}, {"time": 2.6667, "x": 4.14, "y": -0.04}]}, "face": {"translate": [{"curve": [0.022, 0, 0.044, 9.32, 0.022, 0, 0.044, -0.05]}, {"time": 0.0667, "x": 9.32, "y": -0.09, "curve": [0.111, 9.32, 0.156, 1.14, 0.111, -0.17, 0.156, -0.26]}, {"time": 0.2, "x": 1.14, "y": -0.34, "curve": [0.278, 1.14, 0.356, 15.48, 0.278, -0.49, 0.356, -0.79]}, {"time": 0.4333, "x": 15.48, "y": -0.79, "curve": [0.467, 15.48, 0.5, 7.51, 0.467, -0.79, 0.5, -0.11]}, {"time": 0.5333, "x": 7.51, "y": -0.11, "curve": [0.578, 7.51, 0.622, 15.83, 0.578, -0.11, 0.622, -0.3]}, {"time": 0.6667, "x": 15.83, "y": -0.3, "curve": [0.733, 15.83, 0.8, 2.93, 0.733, -0.3, 0.8, -0.12]}, {"time": 0.8667, "x": 2.93, "y": -0.12, "curve": [0.911, 2.93, 0.956, 12.89, 0.911, -0.12, 0.956, -0.4]}, {"time": 1, "x": 12.89, "y": -0.4, "curve": [1.044, 12.89, 1.122, 7.96, 1.044, -0.4, 1.122, -0.29]}, {"time": 1.1667, "x": 6.25, "y": -0.24, "curve": [1.233, 3.67, 1.278, 0, 1.233, -0.16, 1.278, 0]}, {"time": 1.3333, "curve": [1.356, 0, 1.378, 9.32, 1.356, 0, 1.378, -0.05]}, {"time": 1.4, "x": 9.32, "y": -0.09, "curve": [1.444, 9.32, 1.489, 1.14, 1.444, -0.17, 1.489, -0.26]}, {"time": 1.5333, "x": 1.14, "y": -0.34, "curve": [1.611, 1.14, 1.689, 15.48, 1.611, -0.49, 1.689, -0.79]}, {"time": 1.7667, "x": 15.48, "y": -0.79, "curve": [1.8, 15.48, 1.833, 7.51, 1.8, -0.79, 1.833, -0.11]}, {"time": 1.8667, "x": 7.51, "y": -0.11, "curve": [1.911, 7.51, 1.956, 15.83, 1.911, -0.11, 1.956, -0.3]}, {"time": 2, "x": 15.83, "y": -0.3, "curve": [2.067, 15.83, 2.133, 2.93, 2.067, -0.3, 2.133, -0.12]}, {"time": 2.2, "x": 2.93, "y": -0.12, "curve": [2.244, 2.93, 2.289, 12.89, 2.244, -0.12, 2.289, -0.4]}, {"time": 2.3333, "x": 12.89, "y": -0.4, "curve": [2.378, 12.89, 2.456, 7.96, 2.378, -0.4, 2.456, -0.29]}, {"time": 2.5, "x": 6.25, "y": -0.24, "curve": [2.567, 3.67, 2.611, 0, 2.567, -0.16, 2.611, 0]}, {"time": 2.6667}]}}}, "t1_IDLE_1_1_a": {"bones": {"body_R": {"translate": [{"x": 3.6, "y": -0.03, "curve": [0.022, 3.6, 0.044, 7.34, 0.022, -0.03, 0.044, -0.04]}, {"time": 0.0667, "x": 7.34, "y": -0.09, "curve": [0.122, 7.34, 0.178, -13.11, 0.122, -0.19, 0.178, -0.69]}, {"time": 0.2333, "x": -13.11, "y": -0.69, "curve": [0.289, -13.11, 0.344, 9.24, 0.289, -0.69, 0.344, -0.19]}, {"time": 0.4, "x": 10.77, "y": -0.19, "curve": [0.478, 12.91, 0.556, 12.91, 0.478, -0.19, 0.556, -0.19]}, {"time": 0.6333, "x": 12.91, "y": -0.22, "curve": [0.667, 12.91, 0.7, -4.99, 0.667, -0.22, 0.7, -0.31]}, {"time": 0.7333, "x": -4.99, "y": -0.31, "curve": [0.778, -4.99, 0.822, 6.98, 0.778, -0.31, 0.822, -0.16]}, {"time": 0.8667, "x": 6.98, "y": -0.11, "curve": [0.911, 6.98, 0.956, 1.8, 0.911, -0.06, 0.956, -0.02]}, {"time": 1, "x": 1.8, "y": -0.02, "curve": [1.044, 1.8, 1.089, 7.07, 1.044, -0.02, 1.089, -0.1]}, {"time": 1.1333, "x": 7.07, "y": -0.1, "curve": [1.178, 7.07, 1.267, 3.6, 1.178, -0.1, 1.267, -0.03]}, {"time": 1.3333, "x": 3.6, "y": -0.03, "curve": [1.356, 3.6, 1.378, 7.34, 1.356, -0.03, 1.378, -0.04]}, {"time": 1.4, "x": 7.34, "y": -0.09, "curve": [1.456, 7.34, 1.511, -13.11, 1.456, -0.19, 1.511, -0.69]}, {"time": 1.5667, "x": -13.11, "y": -0.69, "curve": [1.622, -13.11, 1.678, 9.24, 1.622, -0.69, 1.678, -0.19]}, {"time": 1.7333, "x": 10.77, "y": -0.19, "curve": [1.811, 12.91, 1.889, 12.91, 1.811, -0.19, 1.889, -0.19]}, {"time": 1.9667, "x": 12.91, "y": -0.22, "curve": [2, 12.91, 2.033, -4.99, 2, -0.22, 2.033, -0.31]}, {"time": 2.0667, "x": -4.99, "y": -0.31, "curve": [2.111, -4.99, 2.156, 6.98, 2.111, -0.31, 2.156, -0.16]}, {"time": 2.2, "x": 6.98, "y": -0.11, "curve": [2.244, 6.98, 2.289, 1.8, 2.244, -0.06, 2.289, -0.02]}, {"time": 2.3333, "x": 1.8, "y": -0.02, "curve": [2.378, 1.8, 2.422, 7.07, 2.378, -0.02, 2.422, -0.1]}, {"time": 2.4667, "x": 7.07, "y": -0.1, "curve": [2.511, 7.07, 2.6, 3.6, 2.511, -0.1, 2.6, -0.03]}, {"time": 2.6667, "x": 3.6, "y": -0.03}]}, "body_L": {"translate": [{"curve": "stepped"}, {"time": 0.0667, "curve": [0.089, 0, 0.111, 3.74, 0.089, 0, 0.111, -0.01]}, {"time": 0.1333, "x": 3.74, "y": -0.05, "curve": [0.189, 3.74, 0.244, -9.95, 0.189, -0.16, 0.244, -0.43]}, {"time": 0.3, "x": -9.95, "y": -0.43, "curve": [0.356, -9.95, 0.411, 5.64, 0.356, -0.43, 0.411, -0.16]}, {"time": 0.4667, "x": 7.17, "y": -0.16, "curve": [0.544, 9.32, 0.622, 9.32, 0.544, -0.16, 0.622, -0.16]}, {"time": 0.7, "x": 9.32, "y": -0.18, "curve": [0.733, 9.32, 0.767, -8.59, 0.733, -0.19, 0.767, -0.27]}, {"time": 0.8, "x": -8.59, "y": -0.27, "curve": [0.844, -8.59, 0.889, 3.38, 0.844, -0.27, 0.889, -0.13]}, {"time": 0.9333, "x": 3.38, "y": -0.08, "curve": [0.978, 3.38, 1.022, -1.8, 0.978, -0.03, 1.022, 0.01]}, {"time": 1.0667, "x": -1.8, "y": 0.01, "curve": [1.111, -1.8, 1.156, 3.47, 1.111, 0.01, 1.156, -0.07]}, {"time": 1.2, "x": 3.47, "y": -0.07, "curve": [1.244, 3.47, 1.289, 0, 1.244, -0.07, 1.289, 0]}, {"time": 1.3333, "curve": "stepped"}, {"time": 1.4, "curve": [1.422, 0, 1.444, 3.74, 1.422, 0, 1.444, -0.01]}, {"time": 1.4667, "x": 3.74, "y": -0.05, "curve": [1.522, 3.74, 1.578, -9.95, 1.522, -0.16, 1.578, -0.43]}, {"time": 1.6333, "x": -9.95, "y": -0.43, "curve": [1.689, -9.95, 1.744, 5.64, 1.689, -0.43, 1.744, -0.16]}, {"time": 1.8, "x": 7.17, "y": -0.16, "curve": [1.878, 9.32, 1.956, 9.32, 1.878, -0.16, 1.956, -0.16]}, {"time": 2.0333, "x": 9.32, "y": -0.18, "curve": [2.067, 9.32, 2.1, -8.59, 2.067, -0.19, 2.1, -0.27]}, {"time": 2.1333, "x": -8.59, "y": -0.27, "curve": [2.178, -8.59, 2.222, 3.38, 2.178, -0.27, 2.222, -0.13]}, {"time": 2.2667, "x": 3.38, "y": -0.08, "curve": [2.311, 3.38, 2.356, -1.8, 2.311, -0.03, 2.356, 0.01]}, {"time": 2.4, "x": -1.8, "y": 0.01, "curve": [2.444, -1.8, 2.489, 3.47, 2.444, 0.01, 2.489, -0.07]}, {"time": 2.5333, "x": 3.47, "y": -0.07, "curve": [2.578, 3.47, 2.622, 0, 2.578, -0.07, 2.622, 0]}, {"time": 2.6667}]}, "browR": {"rotate": [{"value": -4.67, "curve": "stepped"}, {"time": 1.3333, "value": -4.67, "curve": "stepped"}, {"time": 2.6667, "value": -4.67}], "translate": [{"x": -2.98, "y": 0.04, "curve": [0.033, -2.16, 0.067, 6.27, 0.033, 0.04, 0.067, 0.04]}, {"time": 0.1, "x": 6.27, "y": 0.02, "curve": [0.156, 6.27, 0.211, -4.78, 0.156, -0.02, 0.211, -0.17]}, {"time": 0.2667, "x": -4.78, "y": -0.28, "curve": [0.344, -4.78, 0.422, 3.21, 0.344, -0.42, 0.422, -0.6]}, {"time": 0.5, "x": 3.84, "y": -0.73, "curve": [0.567, 4.38, 0.633, 4.38, 0.567, -0.85, 0.633, -1]}, {"time": 0.7, "x": 4.38, "y": -1.05, "curve": [0.744, 4.38, 0.789, -6.82, 0.744, -1.08, 0.789, -1.07]}, {"time": 0.8333, "x": -6.82, "y": -1.08, "curve": [0.9, -6.82, 0.967, -1.44, 0.9, -1.09, 0.967, -1.09]}, {"time": 1.0333, "x": -1.44, "y": -1.09, "curve": [1.089, -1.44, 1.144, -4.06, 1.089, -1.09, 1.144, -1.09]}, {"time": 1.2, "x": -4.06, "y": -1.03, "curve": [1.244, -4.06, 1.289, -4.06, 1.244, -0.99, 1.289, 0.04]}, {"time": 1.3333, "x": -2.98, "y": 0.04, "curve": [1.367, -2.16, 1.4, 6.27, 1.367, 0.04, 1.4, 0.04]}, {"time": 1.4333, "x": 6.27, "y": 0.02, "curve": [1.489, 6.27, 1.544, -4.78, 1.489, -0.02, 1.544, -0.17]}, {"time": 1.6, "x": -4.78, "y": -0.28, "curve": [1.678, -4.78, 1.756, 3.21, 1.678, -0.42, 1.756, -0.6]}, {"time": 1.8333, "x": 3.84, "y": -0.73, "curve": [1.9, 4.38, 1.967, 4.38, 1.9, -0.85, 1.967, -1]}, {"time": 2.0333, "x": 4.38, "y": -1.05, "curve": [2.078, 4.38, 2.122, -6.82, 2.078, -1.08, 2.122, -1.07]}, {"time": 2.1667, "x": -6.82, "y": -1.08, "curve": [2.233, -6.82, 2.3, -1.44, 2.233, -1.09, 2.3, -1.09]}, {"time": 2.3667, "x": -1.44, "y": -1.09, "curve": [2.422, -1.44, 2.478, -4.06, 2.422, -1.09, 2.478, -1.09]}, {"time": 2.5333, "x": -4.06, "y": -1.03, "curve": [2.578, -4.06, 2.622, -4.06, 2.578, -0.99, 2.622, 0.04]}, {"time": 2.6667, "x": -2.98, "y": 0.04}]}, "browL": {"rotate": [{"value": 4.31, "curve": "stepped"}, {"time": 1.3333, "value": 4.31, "curve": "stepped"}, {"time": 2.6667, "value": 4.31}], "translate": [{"x": -1.46, "y": 0.03, "curve": [0.056, 1, 0.111, 7.78, 0.056, 0.03, 0.111, 0.03]}, {"time": 0.1667, "x": 7.78, "y": 0.01, "curve": [0.222, 7.78, 0.278, -3.26, 0.222, -0.02, 0.278, -0.18]}, {"time": 0.3333, "x": -3.26, "y": -0.29, "curve": [0.411, -3.26, 0.489, 4.72, 0.411, -0.44, 0.489, -0.61]}, {"time": 0.5667, "x": 5.35, "y": -0.75, "curve": [0.633, 5.89, 0.7, 5.89, 0.633, -0.87, 0.7, -1.01]}, {"time": 0.7667, "x": 5.89, "y": -1.06, "curve": [0.811, 5.89, 0.856, -5.3, 0.811, -1.09, 0.856, -1.09]}, {"time": 0.9, "x": -5.3, "y": -1.09, "curve": [0.967, -5.3, 1.033, 0.07, 0.967, -1.1, 1.033, -1.1]}, {"time": 1.1, "x": 0.07, "y": -1.1, "curve": [1.156, 0.07, 1.211, -2.54, 1.156, -1.1, 1.211, -1.1]}, {"time": 1.2667, "x": -2.54, "y": -1.05, "curve": [1.289, -2.54, 1.311, -2.45, 1.289, -1.02, 1.311, 0.03]}, {"time": 1.3333, "x": -1.46, "y": 0.03, "curve": [1.389, 1, 1.444, 7.78, 1.389, 0.03, 1.444, 0.03]}, {"time": 1.5, "x": 7.78, "y": 0.01, "curve": [1.556, 7.78, 1.611, -3.26, 1.556, -0.02, 1.611, -0.18]}, {"time": 1.6667, "x": -3.26, "y": -0.29, "curve": [1.744, -3.26, 1.822, 4.72, 1.744, -0.44, 1.822, -0.61]}, {"time": 1.9, "x": 5.35, "y": -0.75, "curve": [1.967, 5.89, 2.033, 5.89, 1.967, -0.87, 2.033, -1.01]}, {"time": 2.1, "x": 5.89, "y": -1.06, "curve": [2.144, 5.89, 2.189, -5.3, 2.144, -1.09, 2.189, -1.09]}, {"time": 2.2333, "x": -5.3, "y": -1.09, "curve": [2.3, -5.3, 2.367, 0.07, 2.3, -1.1, 2.367, -1.1]}, {"time": 2.4333, "x": 0.07, "y": -1.1, "curve": [2.489, 0.07, 2.544, -2.54, 2.489, -1.1, 2.544, -1.1]}, {"time": 2.6, "x": -2.54, "y": -1.05, "curve": [2.622, -2.54, 2.644, -2.45, 2.622, -1.02, 2.644, 0.03]}, {"time": 2.6667, "x": -1.46, "y": 0.03}]}, "eye_l": {"translate": [{"curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2.6667}]}, "mouth": {"scale": [{"x": 0.51, "curve": [0.133, 0.51, 0.267, 1.329, 0.133, 1, 0.267, 1]}, {"time": 0.4, "x": 1.329, "curve": [0.511, 1.329, 0.622, 0.51, 0.511, 1, 0.622, 1]}, {"time": 0.7333, "x": 0.51, "curve": [0.8, 0.51, 0.867, 0.595, 0.8, 1, 0.867, 1]}, {"time": 0.9333, "x": 0.595, "curve": [1.011, 0.595, 1.089, 0.469, 1.011, 1, 1.089, 1]}, {"time": 1.1667, "x": 0.469, "curve": [1.222, 0.469, 1.278, 0.51, 1.222, 1, 1.278, 1]}, {"time": 1.3333, "x": 0.51, "curve": [1.467, 0.51, 1.6, 1.329, 1.467, 1, 1.6, 1]}, {"time": 1.7333, "x": 1.329, "curve": [1.844, 1.329, 1.956, 0.51, 1.844, 1, 1.956, 1]}, {"time": 2.0667, "x": 0.51, "curve": [2.133, 0.51, 2.2, 0.595, 2.133, 1, 2.2, 1]}, {"time": 2.2667, "x": 0.595, "curve": [2.344, 0.595, 2.422, 0.469, 2.344, 1, 2.422, 1]}, {"time": 2.5, "x": 0.469, "curve": [2.556, 0.469, 2.611, 0.51, 2.556, 1, 2.611, 1]}, {"time": 2.6667, "x": 0.51}]}, "body_R2": {"translate": [{"x": 4.86, "y": -0.05, "curve": "stepped"}, {"time": 1.3333, "x": 4.86, "y": -0.05, "curve": "stepped"}, {"time": 2.6667, "x": 4.86, "y": -0.05}]}, "body_L2": {"translate": [{"x": 4.14, "y": -0.04, "curve": "stepped"}, {"time": 1.3333, "x": 4.14, "y": -0.04, "curve": "stepped"}, {"time": 2.6667, "x": 4.14, "y": -0.04}]}, "body": {"rotate": [{"curve": [0.011, 0, 0.022, 0.62]}, {"time": 0.0333, "value": 0.62, "curve": [0.089, 0.62, 0.144, -3.16]}, {"time": 0.2, "value": -3.16, "curve": [0.311, -3.16, 0.422, 3.02]}, {"time": 0.5333, "value": 3.02, "curve": [0.611, 3.02, 0.689, -2.49]}, {"time": 0.7667, "value": -2.49, "curve": [0.811, -2.49, 0.856, 1.26]}, {"time": 0.9, "value": 1.26, "curve": [0.944, 1.26, 0.989, -0.5]}, {"time": 1.0333, "value": -0.5, "curve": [1.078, -0.5, 1.122, 0.49]}, {"time": 1.1667, "value": 0.49, "curve": [1.222, 0.49, 1.278, 0]}, {"time": 1.3333, "curve": [1.344, 0, 1.356, 0.62]}, {"time": 1.3667, "value": 0.62, "curve": [1.422, 0.62, 1.478, -3.16]}, {"time": 1.5333, "value": -3.16, "curve": [1.644, -3.16, 1.756, 3.02]}, {"time": 1.8667, "value": 3.02, "curve": [1.944, 3.02, 2.022, -2.49]}, {"time": 2.1, "value": -2.49, "curve": [2.144, -2.49, 2.189, 1.26]}, {"time": 2.2333, "value": 1.26, "curve": [2.278, 1.26, 2.322, -0.5]}, {"time": 2.3667, "value": -0.5, "curve": [2.411, -0.5, 2.456, 0.49]}, {"time": 2.5, "value": 0.49, "curve": [2.556, 0.49, 2.611, 0]}, {"time": 2.6667}], "translatex": [{}], "translatey": [{}], "scale": [{"curve": [0.022, 1, 0.044, 0.94, 0.022, 1, 0.044, 1.06]}, {"time": 0.0667, "x": 0.94, "y": 1.06, "curve": [0.122, 0.94, 0.178, 1.04, 0.122, 1.06, 0.178, 0.96]}, {"time": 0.2333, "x": 1.04, "y": 0.96, "curve": [0.289, 1.04, 0.344, 0.98, 0.289, 0.96, 0.344, 1.02]}, {"time": 0.4, "x": 0.98, "y": 1.02, "curve": [0.456, 0.98, 0.511, 1.04, 0.456, 1.02, 0.511, 0.96]}, {"time": 0.5667, "x": 1.04, "y": 0.96, "curve": [0.622, 1.04, 0.678, 0.92, 0.622, 0.96, 0.678, 1.08]}, {"time": 0.7333, "x": 0.92, "y": 1.08, "curve": [0.778, 0.92, 0.822, 1.04, 0.778, 1.08, 0.822, 0.96]}, {"time": 0.8667, "x": 1.04, "y": 0.96, "curve": [0.911, 1.04, 0.956, 0.976, 0.911, 0.96, 0.956, 1.024]}, {"time": 1, "x": 0.976, "y": 1.024, "curve": [1.044, 0.976, 1.089, 1.013, 1.044, 1.024, 1.089, 0.987]}, {"time": 1.1333, "x": 1.013, "y": 0.987, "curve": [1.178, 1.013, 1.267, 1, 1.178, 0.987, 1.267, 1]}, {"time": 1.3333, "curve": [1.356, 1, 1.378, 0.94, 1.356, 1, 1.378, 1.06]}, {"time": 1.4, "x": 0.94, "y": 1.06, "curve": [1.456, 0.94, 1.511, 1.04, 1.456, 1.06, 1.511, 0.96]}, {"time": 1.5667, "x": 1.04, "y": 0.96, "curve": [1.622, 1.04, 1.678, 0.98, 1.622, 0.96, 1.678, 1.02]}, {"time": 1.7333, "x": 0.98, "y": 1.02, "curve": [1.789, 0.98, 1.844, 1.04, 1.789, 1.02, 1.844, 0.96]}, {"time": 1.9, "x": 1.04, "y": 0.96, "curve": [1.956, 1.04, 2.011, 0.92, 1.956, 0.96, 2.011, 1.08]}, {"time": 2.0667, "x": 0.92, "y": 1.08, "curve": [2.111, 0.92, 2.156, 1.04, 2.111, 1.08, 2.156, 0.96]}, {"time": 2.2, "x": 1.04, "y": 0.96, "curve": [2.244, 1.04, 2.289, 0.976, 2.244, 0.96, 2.289, 1.024]}, {"time": 2.3333, "x": 0.976, "y": 1.024, "curve": [2.378, 0.976, 2.422, 1.013, 2.378, 1.024, 2.422, 0.987]}, {"time": 2.4667, "x": 1.013, "y": 0.987, "curve": [2.511, 1.013, 2.6, 1, 2.511, 0.987, 2.6, 1]}, {"time": 2.6667}]}, "face": {"translatex": [{"curve": [0.022, 0, 0.044, 9.32]}, {"time": 0.0667, "value": 9.32, "curve": [0.111, 9.32, 0.156, 1.14]}, {"time": 0.2, "value": 1.14, "curve": [0.278, 1.14, 0.356, 15.48]}, {"time": 0.4333, "value": 15.48, "curve": [0.467, 15.48, 0.5, 7.51]}, {"time": 0.5333, "value": 7.51, "curve": [0.578, 7.51, 0.622, 15.83]}, {"time": 0.6667, "value": 15.83, "curve": [0.733, 15.83, 0.8, 2.93]}, {"time": 0.8667, "value": 2.93, "curve": [0.911, 2.93, 0.956, 12.94]}, {"time": 1, "value": 12.94, "curve": [1.044, 12.94, 1.122, 7.96]}, {"time": 1.1667, "value": 6.25, "curve": [1.233, 3.67, 1.278, 0]}, {"time": 1.3333, "curve": [1.356, 0, 1.378, 9.32]}, {"time": 1.4, "value": 9.32, "curve": [1.444, 9.32, 1.489, 1.14]}, {"time": 1.5333, "value": 1.14, "curve": [1.611, 1.14, 1.689, 15.48]}, {"time": 1.7667, "value": 15.48, "curve": [1.8, 15.48, 1.833, 7.51]}, {"time": 1.8667, "value": 7.51, "curve": [1.911, 7.51, 1.956, 15.83]}, {"time": 2, "value": 15.83, "curve": [2.067, 15.83, 2.133, 2.93]}, {"time": 2.2, "value": 2.93, "curve": [2.244, 2.93, 2.289, 12.89]}, {"time": 2.3333, "value": 12.89, "curve": [2.378, 12.89, 2.456, 7.96]}, {"time": 2.5, "value": 6.25, "curve": [2.567, 3.67, 2.611, 0]}, {"time": 2.6667}], "translatey": [{"curve": "stepped"}, {"time": 0.6667, "curve": [0.733, 0, 0.8, -3.52]}, {"time": 0.8667, "value": -3.52, "curve": [0.911, -3.52, 0.956, 9.47]}, {"time": 1, "value": 9.47, "curve": [1.056, 9.47, 1.111, 8.26]}, {"time": 1.1667, "value": 8.25, "curve": [1.489, 8.19, 1.811, 8.25]}, {"time": 2.1333, "value": 8.19, "curve": [2.2, 8.18, 2.267, 0.13]}, {"time": 2.3333, "value": -2.11, "curve": [2.389, -3.98, 2.444, -4.03]}, {"time": 2.5, "value": -4.12}]}, "eye_r": {"translate": [{"curve": "stepped"}, {"time": 0.8667, "curve": [0.889, 0, 0.911, 0, 0.889, 0, 0.911, 30.75]}, {"time": 0.9333, "y": 30.75, "curve": "stepped"}, {"time": 2, "y": 30.75, "curve": [2.022, 0, 2.044, 0, 2.022, 30.75, 2.044, 23.37]}, {"time": 2.0667, "y": 23.37, "curve": "stepped"}, {"time": 2.2667, "y": 23.37, "curve": [2.289, 0, 2.311, 0, 2.289, 23.37, 2.311, -6.51]}, {"time": 2.3333, "y": -6.51, "curve": "stepped"}, {"time": 2.4333, "y": -6.51, "curve": [2.456, 0, 2.478, 0, 2.456, -6.51, 2.478, -2.17]}, {"time": 2.5}]}, "closed_eyeR_up": {"translate": [{"x": 19.69, "y": -0.07, "curve": [0.333, 19.69, 0.667, 27.43, 0.333, -0.07, 0.667, -0.17]}, {"time": 1, "x": 27.43, "y": -0.17, "curve": [1.333, 27.43, 1.5, 18.14, 1.333, -0.17, 1.5, -0.06]}, {"time": 1.8333, "x": 18.14, "y": -0.06, "curve": [1.889, 18.14, 1.944, 21.78, 1.889, -0.06, 1.944, -0.11]}, {"time": 2, "x": 23.56, "y": -0.11, "curve": [2.056, 25.33, 2.111, 28.79, 2.056, -0.11, 2.111, -0.11]}, {"time": 2.1667, "x": 28.79, "y": -0.11, "curve": [2.2, 28.79, 2.233, -15.88, 2.2, -0.11, 2.233, -0.11]}, {"time": 2.2667, "x": -15.88, "y": -0.11, "curve": "stepped"}, {"time": 2.3, "x": -15.88, "y": -0.11, "curve": [2.344, -15.88, 2.389, 27.28, 2.344, -0.11, 2.389, -0.11]}, {"time": 2.4333, "x": 27.28, "y": -0.11, "curve": [2.478, 27.28, 2.522, 19.69, 2.478, -0.11, 2.522, -0.07]}, {"time": 2.5667, "x": 19.69, "y": -0.07}]}, "closed_eyeL_down": {"translate": [{"curve": [0.034, 0, 0.066, 3.43, 0.034, 0, 0.066, 0]}, {"time": 0.1, "x": 3.43, "curve": [0.134, 3.43, 0.166, 0, 0.134, 0, 0.166, 0]}, {"time": 0.2, "curve": [0.234, 0, 0.266, 6.16, 0.234, 0, 0.266, 0]}, {"time": 0.3, "x": 6.16, "curve": [0.334, 6.16, 0.366, 0, 0.334, 0, 0.366, 0]}, {"time": 0.4, "curve": [0.434, 0, 0.466, 2.77, 0.434, 0, 0.466, 0]}, {"time": 0.5, "x": 2.77, "curve": [0.534, 2.77, 0.566, 0, 0.534, 0, 0.566, 0]}, {"time": 0.6, "curve": [0.634, 0, 0.699, 5.24, 0.634, 0, 0.699, 0]}, {"time": 0.7333, "x": 5.24, "curve": [0.767, 5.24, 0.799, 0, 0.767, 0, 0.799, 0]}, {"time": 0.8333, "curve": [0.867, 0, 0.899, 3.43, 0.867, 0, 0.899, 0]}, {"time": 0.9333, "x": 3.43, "curve": [0.967, 3.43, 0.999, 0, 0.967, 0, 0.999, 0]}, {"time": 1.0333, "curve": [1.067, 0, 1.099, 6.16, 1.067, 0, 1.099, 0]}, {"time": 1.1333, "x": 6.16, "curve": [1.167, 6.16, 1.199, 0, 1.167, 0, 1.199, 0]}, {"time": 1.2333, "curve": [1.267, 0, 1.299, 2.77, 1.267, 0, 1.299, 0]}, {"time": 1.3333, "x": 2.77, "curve": [1.367, 2.77, 1.399, 0, 1.367, 0, 1.399, 0]}, {"time": 1.4333, "curve": [1.467, 0, 1.499, 5.24, 1.467, 0, 1.499, 0]}, {"time": 1.5333, "x": 5.24, "curve": [1.567, 5.24, 1.599, 0, 1.567, 0, 1.599, 0]}, {"time": 1.6333, "curve": [1.667, 0, 1.699, 3.43, 1.667, 0, 1.699, 0]}, {"time": 1.7333, "x": 3.43, "curve": [1.767, 3.43, 1.799, 0, 1.767, 0, 1.799, 0]}, {"time": 1.8333, "curve": [1.867, 0, 1.899, 6.16, 1.867, 0, 1.899, 0]}, {"time": 1.9333, "x": 6.16, "curve": [1.967, 6.16, 1.999, 0, 1.967, 0, 1.999, 0]}, {"time": 2.0333, "curve": [2.067, 0, 2.133, 2.77, 2.067, 0, 2.133, 0]}, {"time": 2.1667, "x": 2.77, "curve": [2.201, 2.77, 2.233, 0, 2.201, 0, 2.233, 0]}, {"time": 2.2667, "curve": [2.301, 0, 2.333, 5.24, 2.301, 0, 2.333, 0]}, {"time": 2.3667, "x": 5.24, "curve": [2.401, 5.24, 2.433, 0, 2.401, 0, 2.433, 0]}, {"time": 2.4667, "curve": [2.501, 0, 2.533, 3.43, 2.501, 0, 2.533, 0]}, {"time": 2.5667, "x": 3.43, "curve": [2.601, 3.43, 2.633, 0, 2.601, 0, 2.633, 0]}, {"time": 2.6667}]}, "closed_eyeL_up": {"translate": [{"x": 12.68, "y": -0.13, "curve": [0.333, 12.68, 0.667, 20.42, 0.333, -0.13, 0.667, -0.22]}, {"time": 1, "x": 20.42, "y": -0.22, "curve": [1.333, 20.42, 1.5, 11.13, 1.333, -0.22, 1.5, -0.11]}, {"time": 1.8333, "x": 11.13, "y": -0.11, "curve": [1.889, 11.13, 1.944, 15.1, 1.889, -0.11, 1.944, -0.13]}, {"time": 2, "x": 16.55, "y": -0.17, "curve": [2.056, 18.01, 2.111, 19.87, 2.056, -0.2, 2.111, -0.32]}, {"time": 2.1667, "x": 19.87, "y": -0.32, "curve": [2.2, 19.87, 2.233, -15.11, 2.2, -0.32, 2.233, 0.49]}, {"time": 2.2667, "x": -15.11, "y": 0.49, "curve": "stepped"}, {"time": 2.3, "x": -15.11, "y": 0.49, "curve": [2.344, -15.11, 2.389, 19.87, 2.344, 0.49, 2.389, -0.32]}, {"time": 2.4333, "x": 19.87, "y": -0.32, "curve": [2.478, 19.87, 2.522, 12.68, 2.478, -0.32, 2.522, -0.13]}, {"time": 2.5667, "x": 12.68, "y": -0.13}]}, "closed_eyeR_down": {"translate": [{"curve": "stepped"}, {"time": 2.1667, "curve": [2.2, 0, 2.233, 9.15, 2.2, 0, 2.233, -0.49]}, {"time": 2.2667, "x": 9.15, "y": -0.49, "curve": "stepped"}, {"time": 2.3, "x": 9.15, "y": -0.49, "curve": [2.344, 9.15, 2.389, 3.05, 2.344, -0.49, 2.389, -0.16]}, {"time": 2.4333}], "scale": [{"curve": "stepped"}, {"time": 2.1667, "curve": [2.2, 1, 2.233, 1.032, 2.2, 1, 2.233, 1.902]}, {"time": 2.2667, "x": 1.032, "y": 1.902, "curve": "stepped"}, {"time": 2.3, "x": 1.032, "y": 1.902, "curve": [2.344, 1.032, 2.389, 1.011, 2.344, 1.902, 2.389, 1.301]}, {"time": 2.4333}]}, "legR": {"translate": [{}]}, "low_cntr": {"translatey": [{}], "scale": [{}]}, "legL": {"translate": [{}]}}}, "t1_IDLE_2_0_a": {"slots": {"eyelid_u_r": {"attachment": [{"name": "eyelid_u_r"}, {"time": 2.6667}]}}, "bones": {"body": {"rotate": [{"value": 0.28, "curve": [0.144, -0.58, 0.289, -2.68]}, {"time": 0.4333, "value": -2.68, "curve": [0.656, -2.68, 0.878, 1.32]}, {"time": 1.1, "value": 1.32, "curve": [1.178, 1.32, 1.256, 0.75]}, {"time": 1.3333, "value": 0.28, "curve": [1.478, -0.58, 1.622, -2.68]}, {"time": 1.7667, "value": -2.68, "curve": [1.989, -2.68, 2.211, 1.32]}, {"time": 2.4333, "value": 1.32, "curve": [2.511, 1.32, 2.589, 0.75]}, {"time": 2.6667, "value": 0.28}], "translatex": [{"curve": "stepped"}, {"time": 0.0333, "curve": "stepped"}, {"time": 0.6, "curve": "stepped"}, {"time": 0.7333, "curve": "stepped"}, {"time": 1.2667, "curve": "stepped"}, {"time": 1.4, "curve": "stepped"}, {"time": 1.9333, "curve": "stepped"}, {"time": 2.0667, "curve": "stepped"}, {"time": 2.6333, "curve": "stepped"}, {"time": 2.6667}], "translatey": [{"curve": "stepped"}, {"time": 0.0333, "curve": [0.222, 0, 0.411, 26.61]}, {"time": 0.6, "value": 26.61, "curve": "stepped"}, {"time": 0.7333, "value": 26.61, "curve": [0.911, 26.61, 1.089, 0]}, {"time": 1.2667, "curve": "stepped"}, {"time": 1.4, "curve": [1.578, 0, 1.756, 26.61]}, {"time": 1.9333, "value": 26.61, "curve": "stepped"}, {"time": 2.0667, "value": 26.61, "curve": [2.256, 26.61, 2.444, 0]}, {"time": 2.6333, "curve": "stepped"}, {"time": 2.6667}], "scale": [{"x": 1.02, "y": 0.98, "curve": [0.056, 1.01, 0.111, 1, 0.056, 0.99, 0.111, 1]}, {"time": 0.1667, "curve": [0.278, 1, 0.389, 1.04, 0.278, 1, 0.389, 0.96]}, {"time": 0.5, "x": 1.04, "y": 0.96, "curve": [0.611, 1.04, 0.722, 1, 0.611, 0.96, 0.722, 1]}, {"time": 0.8333, "curve": [0.944, 1, 1.056, 1.04, 0.944, 1, 1.056, 0.96]}, {"time": 1.1667, "x": 1.04, "y": 0.96, "curve": [1.223, 1.04, 1.278, 1.03, 1.223, 0.96, 1.278, 0.97]}, {"time": 1.3333, "x": 1.02, "y": 0.98, "curve": [1.39, 1.01, 1.445, 1, 1.39, 0.99, 1.445, 1]}, {"time": 1.5, "curve": [1.611, 1, 1.722, 1.04, 1.611, 1, 1.722, 0.96]}, {"time": 1.8333, "x": 1.04, "y": 0.96, "curve": [1.944, 1.04, 2.056, 1, 1.944, 0.96, 2.056, 1]}, {"time": 2.1667, "curve": [2.278, 1, 2.389, 1.04, 2.278, 1, 2.389, 0.96]}, {"time": 2.5, "x": 1.04, "y": 0.96, "curve": [2.556, 1.04, 2.612, 1.03, 2.556, 0.96, 2.612, 0.97]}, {"time": 2.6667, "x": 1.02, "y": 0.98}]}, "browL": {"rotate": [{"value": 18.46}], "translate": [{"x": -3.63, "y": 11.28, "curve": "stepped"}, {"time": 0.1, "x": -3.63, "y": 11.28, "curve": [0.144, -2.22, 0.189, 0.61, 0.144, 11.29, 0.189, 11.3]}, {"time": 0.2333, "x": 0.61, "y": 11.3, "curve": [0.267, 0.61, 0.3, -2.22, 0.267, 11.3, 0.3, 11.29]}, {"time": 0.3333, "x": -3.63, "y": 11.28, "curve": "stepped"}, {"time": 1.3, "x": -3.63, "y": 11.28, "curve": [1.344, -2.22, 1.389, 0.61, 1.344, 11.29, 1.389, 11.3]}, {"time": 1.4333, "x": 0.61, "y": 11.3, "curve": [1.467, 0.61, 1.5, -2.22, 1.467, 11.3, 1.5, 11.29]}, {"time": 1.5333, "x": -3.63, "y": 11.28}]}, "browR": {"rotate": [{"value": -20.45}], "translate": [{"x": -0.11, "y": -10.31, "curve": "stepped"}, {"time": 0.0333, "x": -0.11, "y": -10.31, "curve": [0.078, 1.31, 0.122, 4.14, 0.078, -10.3, 0.122, -10.29]}, {"time": 0.1667, "x": 4.14, "y": -10.29, "curve": [0.2, 4.14, 0.233, 1.31, 0.2, -10.29, 0.233, -10.3]}, {"time": 0.2667, "x": -0.11, "y": -10.31, "curve": "stepped"}, {"time": 1.2333, "x": -0.11, "y": -10.31, "curve": [1.278, 1.31, 1.322, 4.14, 1.278, -10.3, 1.322, -10.29]}, {"time": 1.3667, "x": 4.14, "y": -10.29, "curve": [1.4, 4.14, 1.433, 1.31, 1.4, -10.29, 1.433, -10.3]}, {"time": 1.4667, "x": -0.11, "y": -10.31}]}, "closed_eyeL_up": {"translate": [{"x": 30.35, "y": -0.39}]}, "closed_eyeR_up": {"translate": [{"x": 10.93, "y": -0.24}]}, "closed_eyeR_down": {"translate": [{"x": 2.71, "y": -0.01}]}, "closed_eyeL_down": {"translate": [{"x": -2.01, "y": 0.02}]}, "eye_r": {"translate": [{"x": -11.44, "y": 0.12, "curve": "stepped"}, {"time": 0.1333, "x": -11.44, "y": 0.12, "curve": [0.144, -11.44, 0.156, -16.73, 0.144, 0.12, 0.156, 8]}, {"time": 0.1667, "x": -16.73, "y": 8, "curve": "stepped"}, {"time": 0.7667, "x": -16.73, "y": 8, "curve": [0.778, -16.73, 0.789, -16.57, 0.778, 8, 0.789, 23.64]}, {"time": 0.8, "x": -16.57, "y": 23.64, "curve": "stepped"}, {"time": 1.2, "x": -16.57, "y": 23.64, "curve": [1.222, -16.57, 1.244, -16.85, 1.222, 23.64, 1.244, -4.46]}, {"time": 1.2667, "x": -16.85, "y": -4.46, "curve": "stepped"}, {"time": 2.0667, "x": -16.85, "y": -4.46, "curve": [2.078, -16.85, 2.089, -13.24, 2.078, -4.46, 2.089, -1.41]}, {"time": 2.1, "x": -11.44, "y": 0.12}]}, "face": {"translate": [{"x": -5.72, "y": 0.06, "curve": [0.044, -5.72, 0.089, -5.36, 0.044, 0.06, 0.089, 0]}, {"time": 0.1333, "x": -5.36, "y": -0.02, "curve": [0.178, -5.36, 0.222, -11.34, 0.178, -0.04, 0.222, -0.07]}, {"time": 0.2667, "x": -11.34, "y": -0.07, "curve": [0.433, -11.34, 0.6, -5.72, 0.433, -0.07, 0.6, -0.07]}, {"time": 0.7667, "x": -5.72, "y": 0.06, "curve": [0.844, -5.72, 0.922, -5.8, 0.844, 0.12, 0.922, 10.09]}, {"time": 1, "x": -5.83, "y": 10.09, "curve": [1.067, -5.86, 1.133, -5.89, 1.067, 10.09, 1.133, 7.51]}, {"time": 1.2, "x": -5.89, "y": 5.84, "curve": [1.267, -5.89, 1.333, -5.72, 1.267, 4.17, 1.333, 0.06]}, {"time": 1.4, "x": -5.72, "y": 0.06, "curve": [1.644, -5.72, 1.889, -5.89, 1.644, 0.06, 1.889, 5.84]}, {"time": 2.1333, "x": -5.89, "y": 5.84, "curve": [2.2, -5.89, 2.267, -5.72, 2.2, 5.84, 2.267, 0.06]}, {"time": 2.3333, "x": -5.72, "y": 0.06, "curve": "stepped"}, {"time": 2.6667, "x": -5.72, "y": 0.06}]}, "body_R2": {"rotate": [{"curve": [0.09, -1.4, 0.534, -17.52]}, {"time": 0.6667, "value": -17.52, "curve": [0.889, -17.52, 1.111, 3.46]}, {"time": 1.3333, "curve": [1.556, -3.46, 1.778, -17.52]}, {"time": 2, "value": -17.52, "curve": [2.222, -17.52, 2.444, -3.46]}, {"time": 2.6667}], "translate": [{}]}, "body_L2": {"rotate": [{"curve": [0.222, 0, 0.444, 8.85]}, {"time": 0.6667, "value": 8.85, "curve": [0.889, 8.85, 1.111, 0]}, {"time": 1.3333, "curve": [1.556, 0, 1.778, 8.85]}, {"time": 2, "value": 8.85, "curve": [2.222, 8.85, 2.444, 0]}, {"time": 2.6667}], "translate": [{}]}, "mouth": {"scale": [{"x": 0.441, "curve": [0.222, 0.441, 0.444, 0.487, 0.222, 1, 0.444, 1]}, {"time": 0.6667, "x": 0.487, "curve": [0.889, 0.487, 1.111, 0.441, 0.889, 1, 1.111, 1]}, {"time": 1.3333, "x": 0.441, "curve": [1.444, 0.441, 1.556, 0.815, 1.444, 1, 1.556, 1]}, {"time": 1.6667, "x": 0.815, "curve": [1.889, 0.815, 2.111, 0.644, 1.889, 1, 2.111, 1]}, {"time": 2.3333, "x": 0.561, "curve": [2.444, 0.519, 2.556, 0.441, 2.444, 1, 2.556, 1]}, {"time": 2.6667, "x": 0.441}]}, "body_R": {"translate": [{}]}, "low_cntr": {"translatey": [{}], "scale": [{}]}, "legR": {"translate": [{}]}, "legL": {"translate": [{}]}}}}}